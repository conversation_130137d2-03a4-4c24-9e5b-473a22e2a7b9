/**
 * Smart AsthmaCare App Theme
 * Color palette and common styles
 */

import { StyleSheet } from 'react-native';

// Color palette from Coolors
export const COLORS = {
  LIGHT_BG: '#DAD7CD',      // Light background (cards, sections)
  SOFT_HIGHLIGHT: '#A3B18A', // Soft highlight (secondary buttons, icons)
  PRIMARY: '#588157',       // Primary color (buttons, headers, CTAs)
  ACCENT: '#3A5A40',        // Accent or focus border
  TEXT: '#344E41',          // Text, strong backgrounds and overlays
  LIGHT_TEXT: '#6B7D6A',    // Light text for secondary information
  TEXT_LIGHT: '#6B7D6A',    // Alias for LIGHT_TEXT for consistency
  LIGHT_GRAY: '#E0E0E0',    // Light gray for dividers and borders
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  DANGER: '#E63946',        // For emergency buttons
  WARNING: '#FFBA08',       // For warnings
  SUCCESS: '#57CC99',       // For success messages
  TRANSPARENT: 'transparent',
};

// Typography
export const FONTS = {
  SIZES: {
    xs: 10,
    small: 12,
    medium: 14,
    large: 16,
    xl: 18,
    xxl: 22,
    xxxl: 28,
  },
  WEIGHTS: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};

// Spacing
export const SPACING = {
  xs: 4,
  small: 8,
  medium: 16,
  large: 24,
  xl: 32,
  xxl: 48,
  xxlarge: 64,
};

// Border radius
export const BORDER_RADIUS = {
  small: 4,
  medium: 8,
  large: 16,
  xl: 24,
  round: 50,
};

// Common styles
export const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
    padding: SPACING.medium,
  },
  card: {
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: FONTS.SIZES.xxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
  },
  subtitle: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  text: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  buttonPrimary: {
    backgroundColor: COLORS.PRIMARY,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonSecondary: {
    backgroundColor: COLORS.SOFT_HIGHLIGHT,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: COLORS.WHITE,
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  input: {
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    borderWidth: 1,
    borderColor: COLORS.SOFT_HIGHLIGHT,
    padding: SPACING.medium,
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
  },
});

export default {
  COLORS,
  FONTS,
  SPACING,
  BORDER_RADIUS,
  commonStyles,
};
