diff --git a/node_modules/@native-html/transient-render-engine/src/dom/dom-utils.ts b/node_modules/@native-html/transient-render-engine/src/dom/dom-utils.ts
index 1234567..abcdef0 100644
--- a/node_modules/@native-html/transient-render-engine/src/dom/dom-utils.ts
+++ b/node_modules/@native-html/transient-render-engine/src/dom/dom-utils.ts
@@ -1,5 +1,5 @@
 import { Text, Element, Node, Document, NodeWithChildren } from 'domhandler';
-import { Text as TextType, Tag as TagType } from 'domelementtype';
+import { ElementType } from 'domelementtype';

 export { Text, Element, Node, NodeWithChildren, Document };

 export function isDomText(node: any): node is Text {
-  return node && node.type === TextType;
+  return node && node.type === ElementType.Text;
 }

 export function isDomElement(node: any): node is Element {
-  return node && node.type === TagType;
+  return node && node.type === ElementType.Tag;
 }

diff --git a/node_modules/@native-html/transient-render-engine/src/TRenderEngine.ts b/node_modules/@native-html/transient-render-engine/src/TRenderEngine.ts
index 1234567..abcdef0 100644
--- a/node_modules/@native-html/transient-render-engine/src/TRenderEngine.ts
+++ b/node_modules/@native-html/transient-render-engine/src/TRenderEngine.ts
@@ -2,7 +2,7 @@ import { collapse } from './flow/collapse';
 import { hoist } from './flow/hoist';
 import { translateDocument } from './flow/translate';
 import { ParserOptions as HTMLParserOptions } from 'htmlparser2';
-import omit from 'ramda/src/omit';
+import omit from 'ramda/es/omit';
 import {
   CSSProcessorConfig,
   defaultCSSProcessorConfig
