-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enhanced profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT,
  email TEXT UNIQUE,
  phone TEXT,
  photo_url TEXT,
  date_of_birth DATE,
  gender TEXT,
  height NUMERIC, -- in cm
  weight NUMERIC, -- in kg
  emergency_contact_name TEXT,
  emergency_contact_phone TEXT,
  medical_conditions TEXT[],
  allergies TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own profile
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Users can view their own profile'
    ) THEN
        CREATE POLICY "Users can view their own profile"
          ON profiles
          FOR SELECT
          USING (auth.uid() = id);
    END IF;
END $$;

-- Policy for users to update their own profile
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Users can update their own profile'
    ) THEN
        CREATE POLICY "Users can update their own profile"
          ON profiles
          FOR UPDATE
          USING (auth.uid() = id);
    END IF;
END $$;

-- Policy for users to insert their own profile
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Users can insert their own profile'
    ) THEN
        CREATE POLICY "Users can insert their own profile"
          ON profiles
          FOR INSERT
          WITH CHECK (auth.uid() = id);
    END IF;
END $$;

-- Policy for users to delete their own profile
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Users can delete their own profile'
    ) THEN
        CREATE POLICY "Users can delete their own profile"
          ON profiles
          FOR DELETE
          USING (auth.uid() = id);
    END IF;
END $$;

-- Drop old tables if they exist
DROP TABLE IF EXISTS peak_flow_readings CASCADE;
DROP TABLE IF EXISTS symptom_reports CASCADE;
DROP TABLE IF EXISTS weather_data CASCADE;
DROP TABLE IF EXISTS medications CASCADE;
DROP TABLE IF EXISTS medication_reminders CASCADE;
DROP TABLE IF EXISTS medication_logs CASCADE;
DROP TABLE IF EXISTS chat_conversations CASCADE;
DROP TABLE IF EXISTS chat_messages CASCADE;

-- Create merged health_reports table (combines peak_flow_readings, symptom_reports, and weather_data)
CREATE TABLE IF NOT EXISTS health_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  report_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  report_type TEXT NOT NULL, -- 'peak_flow', 'symptom', or 'weather'

  -- Peak flow specific fields
  peak_flow_reading INTEGER,
  reading_time_of_day TEXT, -- 'morning', 'afternoon', 'evening'
  predicted_peak_flow INTEGER, -- calculated based on height, age, gender
  percent_of_personal_best NUMERIC,
  zone TEXT, -- 'green', 'yellow', 'red' based on percentage

  -- Symptom specific fields
  symptoms TEXT[],
  symptom_severity JSONB, -- {'coughing': 'mild', 'wheezing': 'severe', etc.}
  activity_limitation TEXT, -- 'none', 'mild', 'moderate', 'severe'
  sleep_disturbance TEXT, -- 'none', 'mild', 'moderate', 'severe'

  -- Common fields
  medication_used TEXT[],
  trigger_factors TEXT[],
  notes TEXT,

  -- Weather data fields
  weather_condition TEXT,
  weather_description TEXT,
  temperature NUMERIC,
  humidity NUMERIC,
  pressure NUMERIC,
  wind_speed NUMERIC,
  wind_direction TEXT,
  air_quality_index INTEGER,
  pollen_count TEXT,
  pollen_types TEXT[],
  asthma_risk_level TEXT, -- 'low', 'moderate', 'high'

  -- Location data
  location JSONB, -- {latitude, longitude, city, country}

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for health_reports
ALTER TABLE health_reports ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own health reports
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'health_reports' AND policyname = 'Users can view their own health reports'
    ) THEN
        CREATE POLICY "Users can view their own health reports"
          ON health_reports
          FOR SELECT
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to insert their own health reports
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'health_reports' AND policyname = 'Users can insert their own health reports'
    ) THEN
        CREATE POLICY "Users can insert their own health reports"
          ON health_reports
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to update their own health reports
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'health_reports' AND policyname = 'Users can update their own health reports'
    ) THEN
        CREATE POLICY "Users can update their own health reports"
          ON health_reports
          FOR UPDATE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to delete their own health reports
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'health_reports' AND policyname = 'Users can delete their own health reports'
    ) THEN
        CREATE POLICY "Users can delete their own health reports"
          ON health_reports
          FOR DELETE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Create merged medications table (combines medications, medication_reminders, and medication_logs)
CREATE TABLE IF NOT EXISTS medications_unified (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  dosage TEXT,
  medication_type TEXT, -- 'controller', 'reliever', etc.

  -- Schedule information (from medication_reminders)
  morning_time TIME,
  morning_enabled BOOLEAN DEFAULT FALSE,
  afternoon_time TIME,
  afternoon_enabled BOOLEAN DEFAULT FALSE,
  evening_time TIME,
  evening_enabled BOOLEAN DEFAULT FALSE,
  days_of_week TEXT[], -- Array of days: ['Monday', 'Tuesday', etc.]

  -- Medication log information
  morning_taken BOOLEAN DEFAULT FALSE,
  morning_taken_at TIMESTAMP WITH TIME ZONE,
  afternoon_taken BOOLEAN DEFAULT FALSE,
  afternoon_taken_at TIMESTAMP WITH TIME ZONE,
  evening_taken BOOLEAN DEFAULT FALSE,
  evening_taken_at TIMESTAMP WITH TIME ZONE,

  -- Additional fields
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  prescription_doctor TEXT,
  pharmacy TEXT,
  refill_date DATE,
  refill_reminder_enabled BOOLEAN DEFAULT TRUE,
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for medications_unified
ALTER TABLE medications_unified ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own medications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'medications_unified' AND policyname = 'Users can view their own medications'
    ) THEN
        CREATE POLICY "Users can view their own medications"
          ON medications_unified
          FOR SELECT
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to insert their own medications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'medications_unified' AND policyname = 'Users can insert their own medications'
    ) THEN
        CREATE POLICY "Users can insert their own medications"
          ON medications_unified
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to update their own medications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'medications_unified' AND policyname = 'Users can update their own medications'
    ) THEN
        CREATE POLICY "Users can update their own medications"
          ON medications_unified
          FOR UPDATE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to delete their own medications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'medications_unified' AND policyname = 'Users can delete their own medications'
    ) THEN
        CREATE POLICY "Users can delete their own medications"
          ON medications_unified
          FOR DELETE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Create merged chat table (combines chat_conversations and chat_messages)
CREATE TABLE IF NOT EXISTS chat_unified (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  conversation_id UUID DEFAULT uuid_generate_v4(),
  conversation_title TEXT,
  message TEXT,
  is_from_user BOOLEAN DEFAULT TRUE,
  message_type TEXT DEFAULT 'text', -- 'text', 'image', 'data'
  referenced_data JSONB, -- can contain references to peak flow readings, medications, etc.
  ai_model TEXT DEFAULT 'gpt-4o', -- which AI model was used
  ai_analysis JSONB, -- AI's analysis of user data
  ai_recommendations JSONB, -- AI's recommendations
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for chat_unified
ALTER TABLE chat_unified ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own chat messages
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'chat_unified' AND policyname = 'Users can view their own chat messages'
    ) THEN
        CREATE POLICY "Users can view their own chat messages"
          ON chat_unified
          FOR SELECT
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to insert their own chat messages
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'chat_unified' AND policyname = 'Users can insert their own chat messages'
    ) THEN
        CREATE POLICY "Users can insert their own chat messages"
          ON chat_unified
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to update their own chat messages
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'chat_unified' AND policyname = 'Users can update their own chat messages'
    ) THEN
        CREATE POLICY "Users can update their own chat messages"
          ON chat_unified
          FOR UPDATE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to delete their own chat messages
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'chat_unified' AND policyname = 'Users can delete their own chat messages'
    ) THEN
        CREATE POLICY "Users can delete their own chat messages"
          ON chat_unified
          FOR DELETE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Create user_settings table with expanded options
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  language TEXT DEFAULT 'en',
  theme TEXT DEFAULT 'light',
  notification_enabled BOOLEAN DEFAULT TRUE,
  medication_reminders_enabled BOOLEAN DEFAULT TRUE,
  peak_flow_reminders_enabled BOOLEAN DEFAULT TRUE,
  weather_alerts_enabled BOOLEAN DEFAULT TRUE,
  daily_tips_enabled BOOLEAN DEFAULT TRUE,
  share_data_with_healthcare_providers BOOLEAN DEFAULT FALSE,
  location_tracking_enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for user_settings
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own settings
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'user_settings' AND policyname = 'Users can view their own settings'
    ) THEN
        CREATE POLICY "Users can view their own settings"
          ON user_settings
          FOR SELECT
          USING (auth.uid() = id);
    END IF;
END $$;

-- Policy for users to update their own settings
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'user_settings' AND policyname = 'Users can update their own settings'
    ) THEN
        CREATE POLICY "Users can update their own settings"
          ON user_settings
          FOR UPDATE
          USING (auth.uid() = id);
    END IF;
END $$;

-- Policy for users to insert their own settings
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'user_settings' AND policyname = 'Users can insert their own settings'
    ) THEN
        CREATE POLICY "Users can insert their own settings"
          ON user_settings
          FOR INSERT
          WITH CHECK (auth.uid() = id);
    END IF;
END $$;

-- Note: symptom_reports functionality is now part of the health_reports table

-- Create action_plans table
CREATE TABLE IF NOT EXISTS action_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_name TEXT DEFAULT 'My Asthma Action Plan',
  doctor_name TEXT,
  doctor_phone TEXT,
  emergency_contact_name TEXT,
  emergency_contact_phone TEXT,
  green_zone_description TEXT,
  green_zone_peak_flow_min INTEGER,
  green_zone_peak_flow_max INTEGER,
  green_zone_medications JSONB,
  yellow_zone_description TEXT,
  yellow_zone_peak_flow_min INTEGER,
  yellow_zone_peak_flow_max INTEGER,
  yellow_zone_medications JSONB,
  yellow_zone_instructions TEXT,
  red_zone_description TEXT,
  red_zone_peak_flow_min INTEGER,
  red_zone_peak_flow_max INTEGER,
  red_zone_medications JSONB,
  red_zone_instructions TEXT,
  emergency_instructions TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for action_plans
ALTER TABLE action_plans ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own action plans
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'action_plans' AND policyname = 'Users can view their own action plans'
    ) THEN
        CREATE POLICY "Users can view their own action plans"
          ON action_plans
          FOR SELECT
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to insert their own action plans
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'action_plans' AND policyname = 'Users can insert their own action plans'
    ) THEN
        CREATE POLICY "Users can insert their own action plans"
          ON action_plans
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to update their own action plans
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'action_plans' AND policyname = 'Users can update their own action plans'
    ) THEN
        CREATE POLICY "Users can update their own action plans"
          ON action_plans
          FOR UPDATE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to delete their own action plans
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'action_plans' AND policyname = 'Users can delete their own action plans'
    ) THEN
        CREATE POLICY "Users can delete their own action plans"
          ON action_plans
          FOR DELETE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_type TEXT, -- 'medication', 'peak_flow', 'weather', 'tip', etc.
  related_entity_id UUID, -- can reference a medication, reading, etc.
  related_entity_type TEXT, -- 'medication', 'peak_flow_reading', etc.
  is_read BOOLEAN DEFAULT FALSE,
  is_dismissed BOOLEAN DEFAULT FALSE,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own notifications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'notifications' AND policyname = 'Users can view their own notifications'
    ) THEN
        CREATE POLICY "Users can view their own notifications"
          ON notifications
          FOR SELECT
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to insert their own notifications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'notifications' AND policyname = 'Users can insert their own notifications'
    ) THEN
        CREATE POLICY "Users can insert their own notifications"
          ON notifications
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to update their own notifications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'notifications' AND policyname = 'Users can update their own notifications'
    ) THEN
        CREATE POLICY "Users can update their own notifications"
          ON notifications
          FOR UPDATE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Policy for users to delete their own notifications
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'notifications' AND policyname = 'Users can delete their own notifications'
    ) THEN
        CREATE POLICY "Users can delete their own notifications"
          ON notifications
          FOR DELETE
          USING (auth.uid() = user_id);
    END IF;
END $$;

-- Note: weather_data functionality is now part of the health_reports table

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if profile already exists
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    INSERT INTO public.profiles (id, name, email)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'name', NEW.email);
  END IF;

  -- Check if user settings already exist
  IF NOT EXISTS (SELECT 1 FROM public.user_settings WHERE id = NEW.id) THEN
    INSERT INTO public.user_settings (id)
    VALUES (NEW.id);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger
    WHERE tgname = 'on_auth_user_created'
  ) THEN
    CREATE TRIGGER on_auth_user_created
      AFTER INSERT ON auth.users
      FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
  END IF;
END $$;
