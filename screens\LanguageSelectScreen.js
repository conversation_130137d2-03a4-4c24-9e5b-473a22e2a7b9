/**
 * Language Selection Screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS, commonStyles } from '../components/theme';
import Button from '../components/Button';
import i18n, { setLanguage } from '../i18n/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LanguageSelectScreen = ({ navigation }) => {
  const [selectedLanguage, setSelectedLanguage] = useState('en');

  const languages = [
    { code: 'en', name: 'English', flag: '🇬🇧' },
    { code: 'lg', name: 'Luganda', flag: '🇺🇬' },
    { code: 'at', name: 'Ateso', flag: '🇺🇬' },
    { code: 'lu', name: '<PERSON><PERSON>', flag: '🇺🇬' },
  ];

  const handleLanguageSelect = (languageCode) => {
    setSelectedLanguage(languageCode);
  };

  const handleConfirm = async () => {
    await setLanguage(selectedLanguage);
    await AsyncStorage.setItem('hasSelectedLanguage', 'true');
    await AsyncStorage.setItem('userLanguage', selectedLanguage);
    navigation.replace('Onboarding');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.title}>mHealth</Text>
          <Text style={styles.subtitle}>{i18n.t('languageSelection')}</Text>
        </View>

        <View style={styles.languagesContainer}>
          {languages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.languageOption,
                selectedLanguage === language.code && styles.selectedLanguage,
              ]}
              onPress={() => handleLanguageSelect(language.code)}
            >
              <Text style={styles.languageFlag}>{language.flag}</Text>
              <Text style={styles.languageName}>{language.name}</Text>
              {selectedLanguage === language.code && (
                <Ionicons name="checkmark-circle" size={24} color={COLORS.PRIMARY} />
              )}
            </TouchableOpacity>
          ))}
        </View>

        <Button
          title={i18n.t('languageConfirm')}
          onPress={handleConfirm}
          style={styles.confirmButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.large,
    justifyContent: 'space-between',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: SPACING.xxl,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: SPACING.medium,
  },
  title: {
    fontSize: FONTS.SIZES.xxxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
    marginBottom: SPACING.small,
  },
  subtitle: {
    fontSize: FONTS.SIZES.large,
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  languagesContainer: {
    marginVertical: SPACING.xxl,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    padding: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
    marginBottom: SPACING.medium,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedLanguage: {
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
  },
  languageFlag: {
    fontSize: FONTS.SIZES.xxl,
    marginRight: SPACING.medium,
  },
  languageName: {
    fontSize: FONTS.SIZES.large,
    color: COLORS.TEXT,
    flex: 1,
  },
  confirmButton: {
    marginBottom: SPACING.large,
  },
});

export default LanguageSelectScreen;
