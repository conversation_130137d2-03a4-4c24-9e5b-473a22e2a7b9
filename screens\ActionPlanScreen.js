/**
 * Asthma Action Plan Screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Input from '../components/Input';
import Button from '../components/Button';
import i18n from '../i18n/i18n';

const ActionPlanScreen = ({ navigation }) => {
  const [showForm, setShowForm] = useState(true);
  const [formData, setFormData] = useState({
    age: '',
    weight: '',
    height: '',
    diagnosisYear: '',
    severity: 'moderate',
    triggers: {
      dust: false,
      pollen: false,
      coldAir: false,
      exercise: false,
      smoke: false,
      animals: false,
      food: false,
      stress: false,
    },
    medications: {
      reliever: '',
      controller: '',
      other: '',
    },
    doctorName: '',
    doctorPhone: '',
  });

  const handleInputChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleTriggerToggle = (trigger) => {
    setFormData({
      ...formData,
      triggers: {
        ...formData.triggers,
        [trigger]: !formData.triggers[trigger],
      },
    });
  };

  const handleMedicationChange = (field, value) => {
    setFormData({
      ...formData,
      medications: {
        ...formData.medications,
        [field]: value,
      },
    });
  };

  const handleGeneratePlan = () => {
    setShowForm(false);
  };

  const handleEditPlan = () => {
    setShowForm(true);
  };

  const renderForm = () => (
    <ScrollView style={styles.formContainer}>
      {/* Personal Information */}
      <Card title={i18n.t('actionPlan.personalInfo')} style={styles.section}>
        <Input
          label="Age"
          placeholder="Enter your age"
          value={formData.age}
          onChangeText={(value) => handleInputChange('age', value)}
          keyboardType="numeric"
        />
        <Input
          label="Weight (kg)"
          placeholder="Enter your weight"
          value={formData.weight}
          onChangeText={(value) => handleInputChange('weight', value)}
          keyboardType="numeric"
        />
        <Input
          label="Height (cm)"
          placeholder="Enter your height"
          value={formData.height}
          onChangeText={(value) => handleInputChange('height', value)}
          keyboardType="numeric"
        />
        <Input
          label="Year of Diagnosis"
          placeholder="When were you diagnosed?"
          value={formData.diagnosisYear}
          onChangeText={(value) => handleInputChange('diagnosisYear', value)}
          keyboardType="numeric"
        />
      </Card>

      {/* Triggers */}
      <Card title={i18n.t('actionPlan.triggers')} style={styles.section}>
        <Text style={styles.subtitle}>Select all that apply:</Text>
        <View style={styles.triggersContainer}>
          {Object.keys(formData.triggers).map((trigger) => (
            <TouchableOpacity
              key={trigger}
              style={[
                styles.triggerItem,
                formData.triggers[trigger] && styles.triggerSelected,
              ]}
              onPress={() => handleTriggerToggle(trigger)}
            >
              <Text
                style={[
                  styles.triggerText,
                  formData.triggers[trigger] && styles.triggerTextSelected,
                ]}
              >
                {trigger.charAt(0).toUpperCase() + trigger.slice(1)}
              </Text>
              {formData.triggers[trigger] && (
                <Ionicons name="checkmark" size={16} color={COLORS.WHITE} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Medications */}
      <Card title={i18n.t('actionPlan.medications')} style={styles.section}>
        <Input
          label="Reliever Medication (Rescue)"
          placeholder="e.g., Salbutamol, 2 puffs as needed"
          value={formData.medications.reliever}
          onChangeText={(value) => handleMedicationChange('reliever', value)}
        />
        <Input
          label="Controller Medication (Daily)"
          placeholder="e.g., Fluticasone, 1 puff twice daily"
          value={formData.medications.controller}
          onChangeText={(value) => handleMedicationChange('controller', value)}
        />
        <Input
          label="Other Medications"
          placeholder="Any other medications you take"
          value={formData.medications.other}
          onChangeText={(value) => handleMedicationChange('other', value)}
          multiline
          numberOfLines={3}
        />
      </Card>

      {/* Doctor Information */}
      <Card title={i18n.t('actionPlan.doctorInfo')} style={styles.section}>
        <Input
          label="Doctor's Name"
          placeholder="Enter your doctor's name"
          value={formData.doctorName}
          onChangeText={(value) => handleInputChange('doctorName', value)}
        />
        <Input
          label="Doctor's Phone"
          placeholder="Enter your doctor's phone number"
          value={formData.doctorPhone}
          onChangeText={(value) => handleInputChange('doctorPhone', value)}
          keyboardType="phone-pad"
        />
      </Card>

      <Button
        title={i18n.t('actionPlan.generatePlan')}
        onPress={handleGeneratePlan}
        style={styles.generateButton}
      />
    </ScrollView>
  );

  const renderPlan = () => (
    <ScrollView style={styles.planContainer}>
      {/* Green Zone */}
      <Card
        title={i18n.t('actionPlan.greenZone')}
        style={[styles.section, styles.greenZone]}
      >
        <Text style={styles.zoneDescription}>
          Your asthma is under control when:
        </Text>
        <View style={styles.bulletList}>
          <Text style={styles.bulletItem}>• No cough, wheeze, chest tightness, or shortness of breath</Text>
          <Text style={styles.bulletItem}>• Can do all usual activities</Text>
          <Text style={styles.bulletItem}>• No need for quick-relief medicines</Text>
          <Text style={styles.bulletItem}>• No asthma symptoms at night</Text>
        </View>
        <Text style={styles.zoneAction}>
          Take your controller medication daily:
        </Text>
        <Text style={styles.medicationText}>{formData.medications.controller || "Not specified"}</Text>
      </Card>

      {/* Yellow Zone */}
      <Card
        title={i18n.t('actionPlan.yellowZone')}
        style={[styles.section, styles.yellowZone]}
      >
        <Text style={styles.zoneDescription}>
          Your asthma is getting worse when:
        </Text>
        <View style={styles.bulletList}>
          <Text style={styles.bulletItem}>• Cough, wheeze, chest tightness, or shortness of breath</Text>
          <Text style={styles.bulletItem}>• Waking at night due to asthma symptoms</Text>
          <Text style={styles.bulletItem}>• Can do some, but not all, usual activities</Text>
        </View>
        <Text style={styles.zoneAction}>
          Take your quick-relief medicine:
        </Text>
        <Text style={styles.medicationText}>{formData.medications.reliever || "Not specified"}</Text>
        <Text style={styles.zoneAction}>
          Continue your controller medication
        </Text>
        <Text style={styles.zoneWarning}>
          If symptoms don't improve within 24 hours, move to Red Zone
        </Text>
      </Card>

      {/* Red Zone */}
      <Card
        title={i18n.t('actionPlan.redZone')}
        style={[styles.section, styles.redZone]}
      >
        <Text style={styles.zoneDescription}>
          Medical alert! Get help when:
        </Text>
        <View style={styles.bulletList}>
          <Text style={styles.bulletItem}>• Severe shortness of breath, trouble walking or talking</Text>
          <Text style={styles.bulletItem}>• Quick-relief medicines don't help</Text>
          <Text style={styles.bulletItem}>• Symptoms are same or worse after 24 hours in Yellow Zone</Text>
        </View>
        <Text style={styles.zoneAction}>
          Take your quick-relief medicine immediately:
        </Text>
        <Text style={styles.medicationText}>{formData.medications.reliever || "Not specified"}</Text>
        <Text style={styles.zoneWarning}>
          Call your doctor: {formData.doctorPhone || "Not specified"}
        </Text>
        <Text style={styles.zoneWarning}>
          If you cannot reach your doctor, go to the emergency room or call emergency services
        </Text>
      </Card>

      <Button
        title={i18n.t('actionPlan.updatePlan')}
        onPress={handleEditPlan}
        style={styles.updateButton}
      />
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('actionPlan.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
      />
      
      {showForm ? renderForm() : renderPlan()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  formContainer: {
    flex: 1,
    padding: SPACING.medium,
  },
  planContainer: {
    flex: 1,
    padding: SPACING.medium,
  },
  section: {
    marginBottom: SPACING.medium,
  },
  subtitle: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  triggersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  triggerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.small,
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    marginRight: SPACING.small,
    marginBottom: SPACING.small,
  },
  triggerSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  triggerText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    marginRight: SPACING.xs,
  },
  triggerTextSelected: {
    color: COLORS.WHITE,
  },
  generateButton: {
    marginBottom: SPACING.large,
  },
  updateButton: {
    marginBottom: SPACING.large,
  },
  greenZone: {
    borderLeftWidth: 8,
    borderLeftColor: COLORS.SUCCESS,
  },
  yellowZone: {
    borderLeftWidth: 8,
    borderLeftColor: COLORS.WARNING,
  },
  redZone: {
    borderLeftWidth: 8,
    borderLeftColor: COLORS.DANGER,
  },
  zoneDescription: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  bulletList: {
    marginBottom: SPACING.small,
  },
  bulletItem: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  zoneAction: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  medicationText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.PRIMARY,
    fontWeight: FONTS.WEIGHTS.semibold,
    marginBottom: SPACING.small,
  },
  zoneWarning: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.DANGER,
    fontWeight: FONTS.WEIGHTS.medium,
    marginTop: SPACING.small,
  },
});

export default ActionPlanScreen;
