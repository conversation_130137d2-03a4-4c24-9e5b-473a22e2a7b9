/**
 * OpenRouter.ai API Service for Smart AsthmaCare
 * Provides chat functionality using OpenRouter.ai API
 */

// Import environment variables (commented out for now due to issues)
// import { OPENROUTER_API_KEY, OPENROUTER_MODEL, SYSTEM_PROMPT } from '@env';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Hardcoded values since environment variables might not be loading correctly
const OPENROUTER_API_KEY = '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';
const OPENROUTER_MODEL = 'mistralai/mistral-7b-instruct';
const SYSTEM_PROMPT = "You are an AI assistant for Smart AsthmaCare, an asthma management app. You provide helpful, accurate information about asthma management, peak flow readings, medications, and emergency advice. Your responses should be concise, informative, and focused on helping users manage their asthma effectively. Always prioritize medical accuracy and encourage users to consult healthcare professionals for personalized medical advice.";

// Default values as fallbacks
const DEFAULT_MODEL = 'mistralai/mistral-7b-instruct';
const DEFAULT_SYSTEM_PROMPT = SYSTEM_PROMPT;

/**
 * Send a message to the OpenRouter API and get a response
 * @param {string} message - The user's message
 * @param {Array} history - Previous messages in the conversation
 * @param {Object} userData - User data including peak flow readings and medications
 * @returns {Promise<string>} - The AI's response
 */
export const sendMessageToOpenRouter = async (message, history = [], userData = {}) => {
  try {
    // Format the conversation history for the API
    const formattedHistory = formatConversationHistory(history);

    // Add context about the user's health data if available
    const contextualSystemPrompt = addContextToSystemPrompt(SYSTEM_PROMPT || DEFAULT_SYSTEM_PROMPT, userData);

    // Prepare the request payload
    const payload = {
      model: OPENROUTER_MODEL || DEFAULT_MODEL,
      messages: [
        { role: 'system', content: contextualSystemPrompt },
        ...formattedHistory,
        { role: 'user', content: message }
      ],
      temperature: 0.7,
      max_tokens: 1000,
      top_p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
    };

    console.log('Sending request to OpenRouter API:', JSON.stringify(payload, null, 2));

    // Make the API request
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://smartasthmacare-app.com', // Replace with your actual domain
        'X-Title': 'Smart AsthmaCare App'
      },
      body: JSON.stringify(payload)
    });

    // Parse the response
    const data = await response.json();

    if (!response.ok) {
      console.error('OpenRouter API error:', data);
      throw new Error(data.error?.message || 'Error communicating with AI service');
    }

    // Extract and return the AI's response
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error in OpenRouter API call:', error);
    throw error;
  }
};

/**
 * Format conversation history from GiftedChat format to OpenRouter format
 * @param {Array} messages - Messages in GiftedChat format
 * @returns {Array} - Messages formatted for OpenRouter API
 */
const formatConversationHistory = (messages) => {
  if (!messages || messages.length === 0) return [];

  // Convert from newest-first to oldest-first
  const chronologicalMessages = [...messages].reverse();

  // Format for OpenRouter API (limit to last 10 messages to avoid token limits)
  return chronologicalMessages.slice(0, 10).map(msg => ({
    role: msg.user._id === 1 ? 'user' : 'assistant',
    content: msg.text
  }));
};

/**
 * Add user context to the system prompt
 * @param {string} basePrompt - The base system prompt
 * @param {Object} userData - User data including peak flow readings and medications
 * @returns {string} - Enhanced system prompt with user context
 */
const addContextToSystemPrompt = (basePrompt, userData) => {
  let enhancedPrompt = basePrompt;

  // Add peak flow data context if available
  if (userData.peakFlowData && userData.peakFlowData.length > 0) {
    const recentReadings = userData.peakFlowData.slice(-5); // Last 5 readings
    const averagePeakFlow = recentReadings.reduce((sum, reading) => sum + reading.value, 0) / recentReadings.length;

    enhancedPrompt += `\n\nThe user has recent peak flow readings with an average of ${Math.round(averagePeakFlow)} L/min.`;
  }

  // Add medication data context if available
  if (userData.medicationData && userData.medicationData.length > 0) {
    enhancedPrompt += `\n\nThe user is currently taking ${userData.medicationData.length} medication(s): `;
    enhancedPrompt += userData.medicationData.map(med => `${med.name} (${med.dosage})`).join(', ');
  }

  return enhancedPrompt;
};

/**
 * Analyze peak flow data and provide recommendations
 * @param {Array} peakFlowData - User's peak flow readings
 * @returns {Promise<Object>} - Analysis results
 */
export const analyzePeakFlow = async (peakFlowData) => {
  if (!peakFlowData || peakFlowData.length === 0) {
    return {
      average: 0,
      status: "I don't have enough peak flow data to analyze. Please record your peak flow readings regularly in the Symptom Tracker.",
      recommendation: "Regular peak flow monitoring is important for managing asthma effectively.",
      disclaimer: "This is not medical advice. Always consult with your healthcare provider."
    };
  }

  // Calculate average peak flow
  const sum = peakFlowData.reduce((acc, reading) => acc + reading.value, 0);
  const average = Math.round(sum / peakFlowData.length);

  // Determine zone based on average (simplified - in a real app, this would be personalized)
  let status, recommendation;

  if (average >= 400) {
    status = "Your peak flow readings are in the GREEN ZONE, which is good! Your airways appear to be open and your asthma seems well-controlled.";
    recommendation = "Continue your current asthma management plan and medications as prescribed.";
  } else if (average >= 300) {
    status = "Your peak flow readings are in the YELLOW ZONE, which means caution. Your airways may be narrowing and you might need to adjust your medication.";
    recommendation = "Use your rescue inhaler as needed and consider contacting your healthcare provider if symptoms persist.";
  } else {
    status = "Your peak flow readings are in the RED ZONE, which requires immediate attention. Your airways are significantly narrowed.";
    recommendation = "Follow your action plan for the red zone. Use your rescue medication and contact your healthcare provider right away.";
  }

  return {
    average,
    status,
    recommendation,
    disclaimer: "This analysis is based on general guidelines and is not a substitute for medical advice. Always consult with your healthcare provider."
  };
};

export default { sendMessageToOpenRouter, analyzePeakFlow };
