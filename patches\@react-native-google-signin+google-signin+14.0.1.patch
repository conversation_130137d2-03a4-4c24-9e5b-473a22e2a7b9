diff --git a/node_modules/@react-native-google-signin/google-signin/android/src/main/java/com/reactnativegooglesignin/RNGoogleSigninModule.java b/node_modules/@react-native-google-signin/google-signin/android/src/main/java/com/reactnativegooglesignin/RNGoogleSigninModule.java
index 7c9a2e1..a5c9b2e 100644
--- a/node_modules/@react-native-google-signin/google-signin/android/src/main/java/com/reactnativegooglesignin/RNGoogleSigninModule.java
+++ b/node_modules/@react-native-google-signin/google-signin/android/src/main/java/com/reactnativegooglesignin/RNGoogleSigninModule.java
@@ -15,6 +15,7 @@ import com.facebook.react.bridge.ReactMethod;
 import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
 import com.facebook.react.bridge.WritableMap;
+import com.facebook.react.module.annotations.ReactModule;
 import com.google.android.gms.auth.GoogleAuthException;
 import com.google.android.gms.auth.GoogleAuthUtil;
 import com.google.android.gms.auth.UserRecoverableAuthException;
@@ -36,6 +37,7 @@ import java.util.HashMap;
 import java.util.Map;
 
 
+@ReactModule(name = RNGoogleSigninModule.MODULE_NAME)
 public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
   public static final String MODULE_NAME = "RNGoogleSignin";
   private static final int RC_SIGN_IN = 9001;
@@ -76,6 +78,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
     constants.put("BUTTON_SIZE_WIDE", 1);
     constants.put("BUTTON_SIZE_ICON", 2);
     constants.put("BUTTON_COLOR_LIGHT", 0);
+    constants.put("BUTTON_COLOR_AUTO", 2);
     constants.put("BUTTON_COLOR_DARK", 1);
     constants.put("SIGN_IN_CANCELLED", String.valueOf(GoogleSignInStatusCodes.SIGN_IN_CANCELLED));
     constants.put("IN_PROGRESS", String.valueOf(GoogleSignInStatusCodes.SIGN_IN_CURRENTLY_IN_PROGRESS));
@@ -83,6 +86,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
     constants.put("SIGN_IN_REQUIRED", String.valueOf(CommonStatusCodes.SIGN_IN_REQUIRED));
     constants.put("NETWORK_ERROR", String.valueOf(CommonStatusCodes.NETWORK_ERROR));
     constants.put("DEVELOPER_ERROR", String.valueOf(CommonStatusCodes.DEVELOPER_ERROR));
+    constants.put("INTERNAL_ERROR", String.valueOf(CommonStatusCodes.INTERNAL_ERROR));
     return constants;
   }
 
@@ -91,6 +95,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
     if (options.hasKey("webClientId")) {
       String webClientId = options.getString("webClientId");
       builder = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
+              .requestProfile()
               .requestEmail()
               .requestIdToken(webClientId);
       if (options.hasKey("offlineAccess") && options.getBoolean("offlineAccess")) {
@@ -98,6 +103,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
       }
     } else {
       builder = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
+              .requestProfile()
               .requestEmail();
     }
     
@@ -118,6 +124,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
     }
 
     _apiClient = GoogleSignIn.getClient(getReactApplicationContext(), _options);
+    Log.d(MODULE_NAME, "Google Sign-In configured successfully");
   }
 
   @ReactMethod
@@ -125,6 +132,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
     if (_apiClient == null) {
       rejectWithNullClientError(promise);
       return;
+      
     }
 
     final Activity activity = getCurrentActivity();
@@ -133,6 +141,7 @@ public class RNGoogleSigninModule extends ReactContextBaseJavaModule {
       return;
     }
 
+    Log.d(MODULE_NAME, "Starting sign-in intent");
     _signInPromise = promise;
 
     activity.startActivityForResult(_apiClient.getSignInIntent(), RC_SIGN_IN);
