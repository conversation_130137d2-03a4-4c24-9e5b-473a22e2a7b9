module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      ["module:react-native-dotenv", {
        "moduleName": "@env",
        "path": ".env",
        "blacklist": null,
        "whitelist": null,
        "safe": false,
        "allowUndefined": true
      }],
      // Handle Node.js module resolution
      ["module-resolver", {
        "alias": {
          // Use our shim.js for all Node.js modules
          "net": "./shim.js",
          "tls": "./shim.js",
          "stream": "./shim.js",
          "crypto": "./shim.js",
          "http": "./shim.js",
          "https": "./shim.js",
          "os": "./shim.js",
          "path": "./shim.js",
          "fs": "./shim.js",
          "zlib": "./shim.js",
          "ws": "./shim.js",
          // Handle TypeScript files
          "*.ts": "./ts-shim.js",
          "*.tsx": "./ts-shim.js",
          // Handle specific TypeScript modules that might be causing issues
          "expo-modules-core/src/index.ts": "./ts-shim.js"
        }
      }]
    ]
  };
};
