/**
 * Medication Reminder Screen
 */

import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Button from '../components/Button';
import Input from '../components/Input';
import TimePickerInput from '../components/TimePickerInput';
import CustomAlert from '../components/CustomAlert';
import i18n from '../i18n/i18n';
import NotificationService from '../services/NotificationService';
import MedicationService from '../services/MedicationService';
import { showSuccess, showError } from '../utils/alert';

const MedicationScreen = ({ navigation }) => {
  const [medications, setMedications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Custom alert state
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info',
    showCancel: false,
    confirmText: 'OK',
    cancelText: 'Cancel',
    onConfirm: null,
  });

  // Load medications and request notification permissions when the screen loads
  useEffect(() => {
    const initScreen = async () => {
      try {
        // Request notification permissions with custom alert
        const permissionResult = await NotificationService.requestNotificationPermissions(true);

        // If we got a function to show the alert, use it
        if (permissionResult && permissionResult.showPermissionAlert) {
          await permissionResult.showPermissionAlert(setAlertConfig, setAlertVisible);
        }

        // Load medications from database
        await loadMedications();
      } catch (err) {
        console.error('Error initializing medication screen:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    initScreen();
  }, []);

  // Load medications from the database
  const loadMedications = async () => {
    try {
      setLoading(true);
      const { success, medications: meds, error: loadError } = await MedicationService.getMedications();

      if (!success) {
        throw new Error(loadError || 'Failed to load medications');
      }

      // Transform medications to the format expected by the UI
      const formattedMedications = meds.map(med => {
        // Create schedule object based on the unified table structure
        const schedule = {
          morning: med.morning_enabled ? {
            time: med.morning_time ? med.morning_time.substring(0, 5) : '08:00', // Format as HH:MM
            taken: med.morning_taken || false,
            enabled: true
          } : null,
          afternoon: med.afternoon_enabled ? {
            time: med.afternoon_time ? med.afternoon_time.substring(0, 5) : '14:00',
            taken: med.afternoon_taken || false,
            enabled: true
          } : null,
          evening: med.evening_enabled ? {
            time: med.evening_time ? med.evening_time.substring(0, 5) : '20:00',
            taken: med.evening_taken || false,
            enabled: true
          } : null
        };

        return {
          id: med.id,
          name: med.name,
          dosage: med.dosage,
          type: med.medication_type || 'controller',
          schedule,
          notes: med.notes || ''
        };
      });

      setMedications(formattedMedications);
      setError(null);
    } catch (err) {
      console.error('Error loading medications:', err);
      setError(err.message);

      // If there's an error, use sample data for now
      setMedications([
        {
          id: '1',
          name: 'Salbutamol',
          dosage: '2 puffs',
          schedule: {
            morning: { time: '08:00', taken: false, enabled: true },
            afternoon: { time: '14:00', taken: false, enabled: true },
            evening: { time: '20:00', taken: false, enabled: true },
          },
          type: 'reliever',
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const [modalVisible, setModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentMedicationId, setCurrentMedicationId] = useState(null);
  const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
  const [newMedication, setNewMedication] = useState({
    name: '',
    dosage: '',
    type: 'controller',
    schedule: {
      morning: { time: '08:00', enabled: false },
      afternoon: { time: '14:00', enabled: false },
      evening: { time: '20:00', enabled: false },
    },
  });

  const handleToggleTaken = async (medicationId, timeOfDay) => {
    try {
      // Find the medication and get the current taken status
      const medication = medications.find(med => med.id === medicationId);
      if (!medication || !medication.schedule[timeOfDay]) {
        return;
      }

      // Get the new taken status (toggled)
      const newTakenStatus = !medication.schedule[timeOfDay].taken;

      // Update the UI immediately for better user experience
      setMedications(
        medications.map((med) => {
          if (med.id === medicationId && med.schedule[timeOfDay]) {
            return {
              ...med,
              schedule: {
                ...med.schedule,
                [timeOfDay]: {
                  ...med.schedule[timeOfDay],
                  taken: newTakenStatus,
                },
              },
            };
          }
          return med;
        })
      );

      // Update the database
      const result = await MedicationService.updateMedicationTakenStatus(
        medicationId,
        timeOfDay,
        newTakenStatus
      );

      if (!result.success) {
        console.error('Failed to update medication taken status:', result.error);
        // Revert the UI change if the database update failed
        setMedications(
          medications.map((med) => {
            if (med.id === medicationId) {
              return medication; // Revert to original medication object
            }
            return med;
          })
        );

        // Show error message
        showError(
          i18n.t('error'),
          i18n.t('medication.updateStatusError'),
          [{ text: i18n.t('ok') }]
        );
      } else if (newTakenStatus) {
        // If medication was marked as taken, create a notification in the database
        try {
          // Create a notification for the medication taken
          await NotificationService.createDatabaseNotification({
            title: `${medication.name} ${i18n.t('medication.takenTitle')}`,
            message: `${medication.name} ${medication.dosage} - ${timeOfDay} ${i18n.t('medication.takenMessage')}`,
            notification_type: 'medication',
            is_read: true, // Mark as read since the user just did this action
            is_dismissed: false,
            related_entity_id: medicationId,
            related_entity_type: 'medication',
          });
        } catch (notificationError) {
          console.error('Error creating medication taken notification:', notificationError);
          // Continue even if notification creation fails
        }
      }
    } catch (error) {
      console.error('Error toggling medication taken status:', error);
      showError(
        i18n.t('error'),
        error.message || i18n.t('medication.updateStatusError'),
        [{ text: i18n.t('ok') }]
      );
    }
  };

  const handleSaveMedication = async () => {
    try {
      setLoading(true);

      // Validate
      if (!newMedication.name || !newMedication.dosage) {
        showError(
          i18n.t('error'),
          i18n.t('medication.enterNameDosage'),
          [{ text: i18n.t('ok') }]
        );
        return;
      }

      // Check if at least one time is enabled
      const hasSchedule =
        newMedication.schedule.morning.enabled ||
        newMedication.schedule.afternoon.enabled ||
        newMedication.schedule.evening.enabled;

      if (!hasSchedule) {
        showError(
          i18n.t('error'),
          i18n.t('medication.selectTime'),
          [{ text: i18n.t('ok') }]
        );
        return;
      }

      // Create medication object for database
      const medicationData = {
        name: newMedication.name,
        dosage: newMedication.dosage,
        frequency: newMedication.type,
        notes: newMedication.notes || '',
        schedule: {
          morning: newMedication.schedule.morning.enabled
            ? {
                time: newMedication.schedule.morning.time,
                taken: false,
                enabled: true
              }
            : null,
          afternoon: newMedication.schedule.afternoon.enabled
            ? {
                time: newMedication.schedule.afternoon.time,
                taken: false,
                enabled: true
              }
            : null,
          evening: newMedication.schedule.evening.enabled
            ? {
                time: newMedication.schedule.evening.time,
                taken: false,
                enabled: true
              }
            : null,
        },
      };

      let result;

      if (isEditing) {
        // Cancel existing notifications for this medication
        await NotificationService.cancelAllReminders(currentMedicationId);

        // Update medication in database
        result = await MedicationService.updateMedication(currentMedicationId, medicationData);

        if (!result.success) {
          throw new Error(result.error || 'Failed to update medication');
        }

        // Update local state
        const updatedMedications = medications.map((med) => {
          if (med.id === currentMedicationId) {
            // Preserve the 'taken' status for existing schedules
            const updatedSchedule = {
              morning: medicationData.schedule.morning
                ? {
                    ...medicationData.schedule.morning,
                    taken: med.schedule.morning ? med.schedule.morning.taken : false,
                    enabled: true
                  }
                : null,
              afternoon: medicationData.schedule.afternoon
                ? {
                    ...medicationData.schedule.afternoon,
                    taken: med.schedule.afternoon ? med.schedule.afternoon.taken : false,
                    enabled: true
                  }
                : null,
              evening: medicationData.schedule.evening
                ? {
                    ...medicationData.schedule.evening,
                    taken: med.schedule.evening ? med.schedule.evening.taken : false,
                    enabled: true
                  }
                : null,
            };

            return {
              ...med,
              name: medicationData.name,
              dosage: medicationData.dosage,
              type: medicationData.frequency,
              schedule: updatedSchedule,
              notes: medicationData.notes
            };
          }
          return med;
        });

        setMedications(updatedMedications);

        // Get the updated medication
        const updatedMedication = updatedMedications.find(med => med.id === currentMedicationId);

        // Schedule new notifications
        await scheduleNotificationsForMedication(updatedMedication);

        showSuccess(
          i18n.t('success'),
          i18n.t('medication.updateSuccess'),
          [{ text: i18n.t('ok') }]
        );
      } else {
        // Add new medication to database
        result = await MedicationService.saveMedication(medicationData);

        if (!result.success) {
          throw new Error(result.error || 'Failed to save medication');
        }

        // Create a UI-friendly version of the medication
        const newMed = {
          id: result.medication.id,
          name: result.medication.name,
          dosage: result.medication.dosage,
          type: result.medication.frequency,
          notes: result.medication.notes,
          schedule: {
            morning: medicationData.schedule.morning ? {
              ...medicationData.schedule.morning,
              enabled: true
            } : null,
            afternoon: medicationData.schedule.afternoon ? {
              ...medicationData.schedule.afternoon,
              enabled: true
            } : null,
            evening: medicationData.schedule.evening ? {
              ...medicationData.schedule.evening,
              enabled: true
            } : null,
          },
        };

        setMedications([...medications, newMed]);

        // Schedule notifications for the new medication
        await scheduleNotificationsForMedication(newMed);

        showSuccess(
          i18n.t('success'),
          i18n.t('medication.saveSuccess'),
          [{ text: i18n.t('ok') }]
        );
      }

      // Reset form and close modal
      resetForm();
    } catch (err) {
      console.error('Error saving medication:', err);
      showError(
        i18n.t('error'),
        err.message || i18n.t('medication.saveError'),
        [{ text: i18n.t('ok') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleEditMedication = (medicationId) => {
    const medicationToEdit = medications.find((med) => med.id === medicationId);
    if (!medicationToEdit) return;

    // Convert the medication data to the form format
    setNewMedication({
      name: medicationToEdit.name,
      dosage: medicationToEdit.dosage,
      type: medicationToEdit.type,
      schedule: {
        morning: medicationToEdit.schedule.morning
          ? { time: medicationToEdit.schedule.morning.time, enabled: true }
          : { time: '08:00', enabled: false },
        afternoon: medicationToEdit.schedule.afternoon
          ? { time: medicationToEdit.schedule.afternoon.time, enabled: true }
          : { time: '14:00', enabled: false },
        evening: medicationToEdit.schedule.evening
          ? { time: medicationToEdit.schedule.evening.time, enabled: true }
          : { time: '20:00', enabled: false },
      },
    });

    setIsEditing(true);
    setCurrentMedicationId(medicationId);
    setModalVisible(true);
  };

  const handleDeleteMedication = (medicationId) => {
    setCurrentMedicationId(medicationId);
    setConfirmDeleteVisible(true);
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);

      // Cancel all notifications for this medication
      await NotificationService.cancelAllReminders(currentMedicationId);

      // Delete medication from database
      const result = await MedicationService.deleteMedication(currentMedicationId);

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete medication');
      }

      // Remove the medication from the list
      setMedications(medications.filter((med) => med.id !== currentMedicationId));

      showSuccess(
        i18n.t('success'),
        i18n.t('medication.deleteSuccess'),
        [{ text: i18n.t('ok') }]
      );
    } catch (err) {
      console.error('Error deleting medication:', err);
      showError(
        i18n.t('error'),
        err.message || i18n.t('medication.deleteError'),
        [{ text: i18n.t('ok') }]
      );
    } finally {
      setConfirmDeleteVisible(false);
      setCurrentMedicationId(null);
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewMedication({
      name: '',
      dosage: '',
      type: 'controller',
      schedule: {
        morning: { time: '08:00', enabled: false },
        afternoon: { time: '14:00', enabled: false },
        evening: { time: '20:00', enabled: false },
      },
    });
    setIsEditing(false);
    setCurrentMedicationId(null);
    setModalVisible(false);
  };

  // Schedule notifications for a medication
  const scheduleNotificationsForMedication = async (medication) => {
    try {
      // Schedule notifications for each time of day
      const notificationResults = await NotificationService.scheduleAllReminders(medication);

      // If notifications were scheduled successfully, show a confirmation
      if (Object.keys(notificationResults).length > 0) {
        // Count how many reminders were scheduled
        const reminderCount = Object.keys(notificationResults).length;

        // Show a success message
        showSuccess(
          i18n.t('medication.notificationScheduledTitle'),
          i18n.t('medication.notificationScheduledMessage', { count: reminderCount }),
          [{ text: i18n.t('ok') }]
        );
      }
    } catch (error) {
      console.error('Error scheduling notifications:', error);
      showError(
        i18n.t('medication.notificationErrorTitle'),
        i18n.t('medication.notificationErrorMessage'),
        [{ text: i18n.t('ok') }]
      );
    }
  };

  const handleScheduleToggle = (timeOfDay) => {
    setNewMedication({
      ...newMedication,
      schedule: {
        ...newMedication.schedule,
        [timeOfDay]: {
          ...newMedication.schedule[timeOfDay],
          enabled: !newMedication.schedule[timeOfDay].enabled,
        },
      },
    });
  };

  const handleTimeChange = (timeOfDay, time) => {
    setNewMedication({
      ...newMedication,
      schedule: {
        ...newMedication.schedule,
        [timeOfDay]: {
          ...newMedication.schedule[timeOfDay],
          time,
        },
      },
    });
  };

  const handleTypeChange = (type) => {
    setNewMedication({
      ...newMedication,
      type,
    });
  };

  const renderMedicationItem = (medication) => (
    <Card
      key={medication.id}
      style={[
        styles.medicationCard,
        medication.type === 'reliever'
          ? styles.relieverCard
          : styles.controllerCard,
      ]}
    >
      <View style={styles.medicationHeader}>
        <View>
          <Text style={styles.medicationName}>{medication.name}</Text>
          <Text style={styles.medicationDosage}>{medication.dosage}</Text>
        </View>
        <View style={styles.medicationActions}>
          <View
            style={[
              styles.typeTag,
              medication.type === 'reliever'
                ? styles.relieverTag
                : styles.controllerTag,
            ]}
          >
            <Text style={styles.typeText}>
              {medication.type === 'reliever' ? 'Reliever' : 'Controller'}
            </Text>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEditMedication(medication.id)}
            >
              <Ionicons name="create-outline" size={20} color={COLORS.PRIMARY} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteMedication(medication.id)}
            >
              <Ionicons name="trash-outline" size={20} color={COLORS.DANGER} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={styles.scheduleContainer}>
        {medication.schedule.morning && (
          <TouchableOpacity
            style={[
              styles.scheduleItem,
              medication.schedule.morning.taken && styles.takenItem,
            ]}
            onPress={() => handleToggleTaken(medication.id, 'morning')}
          >
            <View style={styles.timeContainer}>
              <Ionicons name="sunny-outline" size={20} color={COLORS.TEXT} />
              <Text style={styles.timeText}>{medication.schedule.morning.time}</Text>
            </View>
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>
                {medication.schedule.morning.taken
                  ? i18n.t('medication.taken')
                  : i18n.t('medication.missed')}
              </Text>
              <View
                style={[
                  styles.statusIndicator,
                  medication.schedule.morning.taken
                    ? styles.takenIndicator
                    : styles.missedIndicator,
                ]}
              >
                {medication.schedule.morning.taken && (
                  <Ionicons name="checkmark" size={16} color={COLORS.WHITE} />
                )}
              </View>
            </View>
          </TouchableOpacity>
        )}

        {medication.schedule.afternoon && (
          <TouchableOpacity
            style={[
              styles.scheduleItem,
              medication.schedule.afternoon.taken && styles.takenItem,
            ]}
            onPress={() => handleToggleTaken(medication.id, 'afternoon')}
          >
            <View style={styles.timeContainer}>
              <Ionicons name="partly-sunny-outline" size={20} color={COLORS.TEXT} />
              <Text style={styles.timeText}>{medication.schedule.afternoon.time}</Text>
            </View>
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>
                {medication.schedule.afternoon.taken
                  ? i18n.t('medication.taken')
                  : i18n.t('medication.missed')}
              </Text>
              <View
                style={[
                  styles.statusIndicator,
                  medication.schedule.afternoon.taken
                    ? styles.takenIndicator
                    : styles.missedIndicator,
                ]}
              >
                {medication.schedule.afternoon.taken && (
                  <Ionicons name="checkmark" size={16} color={COLORS.WHITE} />
                )}
              </View>
            </View>
          </TouchableOpacity>
        )}

        {medication.schedule.evening && (
          <TouchableOpacity
            style={[
              styles.scheduleItem,
              medication.schedule.evening.taken && styles.takenItem,
            ]}
            onPress={() => handleToggleTaken(medication.id, 'evening')}
          >
            <View style={styles.timeContainer}>
              <Ionicons name="moon-outline" size={20} color={COLORS.TEXT} />
              <Text style={styles.timeText}>{medication.schedule.evening.time}</Text>
            </View>
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>
                {medication.schedule.evening.taken
                  ? i18n.t('medication.taken')
                  : i18n.t('medication.missed')}
              </Text>
              <View
                style={[
                  styles.statusIndicator,
                  medication.schedule.evening.taken
                    ? styles.takenIndicator
                    : styles.missedIndicator,
                ]}
              >
                {medication.schedule.evening.taken && (
                  <Ionicons name="checkmark" size={16} color={COLORS.WHITE} />
                )}
              </View>
            </View>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('medication.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
      />

      {/* Custom Alert for permission requests */}
      <CustomAlert
        visible={alertVisible}
        onClose={() => setAlertVisible(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
        confirmText={alertConfig.confirmText}
        cancelText={alertConfig.cancelText}
        onConfirm={alertConfig.onConfirm}
        showCancel={alertConfig.showCancel}
      />

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}>
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>{new Date().toDateString()}</Text>
        </View>

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>{i18n.t('loading')}</Text>
          </View>
        )}

        {error && (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={24} color={COLORS.DANGER} />
            <Text style={styles.errorText}>{error}</Text>
            <Button
              title={i18n.t('retry')}
              onPress={loadMedications}
              style={styles.retryButton}
            />
          </View>
        )}

        {medications.map(renderMedicationItem)}

        <Button
          title={i18n.t('medication.addMedication')}
          onPress={() => setModalVisible(true)}
          icon={<Ionicons name="add-circle-outline" size={20} color={COLORS.WHITE} />}
          style={styles.addButton}
        />
      </ScrollView>

      {/* Add/Edit Medication Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => resetForm()}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {isEditing ? i18n.t('medication.editMedication') : i18n.t('medication.addMedication')}
              </Text>
              <TouchableOpacity
                onPress={() => resetForm()}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <Input
                label={i18n.t('medication.medicationName')}
                placeholder="e.g., Salbutamol"
                value={newMedication.name}
                onChangeText={(text) =>
                  setNewMedication({ ...newMedication, name: text })
                }
              />

              <Input
                label={i18n.t('medication.dosage')}
                placeholder="e.g., 2 puffs"
                value={newMedication.dosage}
                onChangeText={(text) =>
                  setNewMedication({ ...newMedication, dosage: text })
                }
              />

              <Text style={styles.sectionTitle}>{i18n.t('medication.schedule')}</Text>
              <View style={styles.scheduleOptions}>
                <TouchableOpacity
                  style={[
                    styles.scheduleOption,
                    newMedication.schedule.morning.enabled && styles.scheduleSelected,
                  ]}
                  onPress={() => handleScheduleToggle('morning')}
                >
                  <Ionicons
                    name="sunny-outline"
                    size={24}
                    color={
                      newMedication.schedule.morning.enabled
                        ? COLORS.PRIMARY
                        : COLORS.TEXT
                    }
                  />
                  <Text
                    style={[
                      styles.scheduleText,
                      newMedication.schedule.morning.enabled && styles.scheduleTextSelected,
                    ]}
                  >
                    {i18n.t('medication.morning')}
                  </Text>
                  {newMedication.schedule.morning.enabled && (
                    <TimePickerInput
                      value={newMedication.schedule.morning.time}
                      onChange={(time) => handleTimeChange('morning', time)}
                      style={styles.timeInput}
                    />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.scheduleOption,
                    newMedication.schedule.afternoon.enabled && styles.scheduleSelected,
                  ]}
                  onPress={() => handleScheduleToggle('afternoon')}
                >
                  <Ionicons
                    name="partly-sunny-outline"
                    size={24}
                    color={
                      newMedication.schedule.afternoon.enabled
                        ? COLORS.PRIMARY
                        : COLORS.TEXT
                    }
                  />
                  <Text
                    style={[
                      styles.scheduleText,
                      newMedication.schedule.afternoon.enabled && styles.scheduleTextSelected,
                    ]}
                  >
                    {i18n.t('medication.afternoon')}
                  </Text>
                  {newMedication.schedule.afternoon.enabled && (
                    <TimePickerInput
                      value={newMedication.schedule.afternoon.time}
                      onChange={(time) => handleTimeChange('afternoon', time)}
                      style={styles.timeInput}
                    />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.scheduleOption,
                    newMedication.schedule.evening.enabled && styles.scheduleSelected,
                  ]}
                  onPress={() => handleScheduleToggle('evening')}
                >
                  <Ionicons
                    name="moon-outline"
                    size={24}
                    color={
                      newMedication.schedule.evening.enabled
                        ? COLORS.PRIMARY
                        : COLORS.TEXT
                    }
                  />
                  <Text
                    style={[
                      styles.scheduleText,
                      newMedication.schedule.evening.enabled && styles.scheduleTextSelected,
                    ]}
                  >
                    {i18n.t('medication.evening')}
                  </Text>
                  {newMedication.schedule.evening.enabled && (
                    <TimePickerInput
                      value={newMedication.schedule.evening.time}
                      onChange={(time) => handleTimeChange('evening', time)}
                      style={styles.timeInput}
                    />
                  )}
                </TouchableOpacity>
              </View>

              <Text style={styles.sectionTitle}>Medication Type</Text>
              <View style={styles.typeOptions}>
                <TouchableOpacity
                  style={[
                    styles.typeOption,
                    newMedication.type === 'controller' && styles.typeSelected,
                  ]}
                  onPress={() => handleTypeChange('controller')}
                >
                  <Text
                    style={[
                      styles.typeOptionText,
                      newMedication.type === 'controller' && styles.typeTextSelected,
                    ]}
                  >
                    Controller
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.typeOption,
                    newMedication.type === 'reliever' && styles.typeSelected,
                  ]}
                  onPress={() => handleTypeChange('reliever')}
                >
                  <Text
                    style={[
                      styles.typeOptionText,
                      newMedication.type === 'reliever' && styles.typeTextSelected,
                    ]}
                  >
                    Reliever
                  </Text>
                </TouchableOpacity>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                title={i18n.t('cancel')}
                onPress={() => resetForm()}
                type="outline"
                style={styles.modalButton}
              />
              <Button
                title={i18n.t('save')}
                onPress={handleSaveMedication}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={confirmDeleteVisible}
        animationType="fade"
        transparent={true}
        onRequestClose={() => setConfirmDeleteVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, styles.confirmModalContent]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('medication.confirmDelete')}</Text>
            </View>
            <View style={styles.confirmModalBody}>
              <Text style={styles.confirmText}>
                {i18n.t('medication.deleteConfirmMessage')}
              </Text>
            </View>
            <View style={styles.modalFooter}>
              <Button
                title={i18n.t('cancel')}
                onPress={() => setConfirmDeleteVisible(false)}
                type="outline"
                style={styles.modalButton}
              />
              <Button
                title={i18n.t('delete')}
                onPress={confirmDelete}
                style={[styles.modalButton, styles.deleteButton]}
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
  },
  contentContainer: {
    paddingBottom: 100, // Add extra padding at the bottom to prevent content from being hidden by the tab bar
  },
  dateContainer: {
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  dateText: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  medicationCard: {
    marginBottom: SPACING.medium,
  },
  relieverCard: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.WARNING,
  },
  controllerCard: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY,
  },
  medicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.medium,
  },
  medicationActions: {
    alignItems: 'flex-end',
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: SPACING.small,
  },
  actionButton: {
    padding: SPACING.xs,
    marginLeft: SPACING.small,
  },
  medicationName: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  medicationDosage: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  typeTag: {
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
  },
  relieverTag: {
    backgroundColor: COLORS.WARNING + '30',
  },
  controllerTag: {
    backgroundColor: COLORS.PRIMARY + '30',
  },
  typeText: {
    fontSize: FONTS.SIZES.small,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  scheduleContainer: {
    marginTop: SPACING.small,
  },
  scheduleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.small,
    paddingHorizontal: SPACING.medium,
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.medium,
    marginBottom: SPACING.small,
  },
  takenItem: {
    backgroundColor: COLORS.SUCCESS + '20',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginLeft: SPACING.small,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginRight: SPACING.small,
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  takenIndicator: {
    backgroundColor: COLORS.SUCCESS,
  },
  missedIndicator: {
    backgroundColor: COLORS.DANGER,
  },
  addButton: {
    marginVertical: SPACING.large,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  modalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  modalBody: {
    padding: SPACING.medium,
    maxHeight: 400,
  },
  sectionTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginTop: SPACING.medium,
    marginBottom: SPACING.small,
  },
  scheduleOptions: {
    marginBottom: SPACING.medium,
  },
  scheduleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.small,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.medium,
    marginBottom: SPACING.small,
  },
  scheduleSelected: {
    borderColor: COLORS.PRIMARY,
  },
  scheduleText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginLeft: SPACING.small,
    flex: 1,
  },
  scheduleTextSelected: {
    color: COLORS.PRIMARY,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  timeInput: {
    width: 80,
    marginBottom: 0,
  },
  typeOptions: {
    flexDirection: 'row',
    marginBottom: SPACING.medium,
  },
  typeOption: {
    flex: 1,
    padding: SPACING.small,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.LIGHT_BG,
    marginRight: SPACING.small,
    borderRadius: BORDER_RADIUS.medium,
  },
  typeSelected: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY + '10',
  },
  typeOptionText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  typeTextSelected: {
    color: COLORS.PRIMARY,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: SPACING.medium,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_BG,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  confirmModalContent: {
    maxHeight: 'auto',
  },
  confirmModalBody: {
    padding: SPACING.medium,
  },
  confirmText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  deleteButton: {
    backgroundColor: COLORS.DANGER,
  },
  loadingContainer: {
    padding: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: SPACING.medium,
  },
  loadingText: {
    fontSize: FONTS.SIZES.medium,
    marginTop: SPACING.small,
    color: COLORS.TEXT,
  },
  errorContainer: {
    padding: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.DANGER + '20',
    borderRadius: BORDER_RADIUS.medium,
    marginVertical: SPACING.medium,
  },
  errorText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.DANGER,
    marginVertical: SPACING.small,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: SPACING.small,
    backgroundColor: COLORS.PRIMARY,
  },
});

export default MedicationScreen;
