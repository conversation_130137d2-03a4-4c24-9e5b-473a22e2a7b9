/**
 * TimePickerInput Component
 * A specialized input for selecting time with proper format validation
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';

const TimePickerInput = ({
  label,
  value,
  onChange,
  style,
  labelStyle,
  error,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Parse the time string (HH:MM) to a Date object
  const parseTimeString = (timeStr) => {
    const date = new Date();
    if (timeStr && timeStr.includes(':')) {
      const [hours, minutes] = timeStr.split(':').map(Number);
      date.setHours(hours, minutes, 0, 0);
    }
    return date;
  };

  // Format a Date object to a time string (HH:MM)
  const formatTimeString = (date) => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  const handleTimeChange = (event, selectedDate) => {
    const currentDate = selectedDate || parseTimeString(value);
    
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }
    
    if (selectedDate) {
      const formattedTime = formatTimeString(currentDate);
      onChange(formattedTime);
    }
  };

  const showTimepicker = () => {
    setIsFocused(true);
    setShowPicker(true);
  };

  const hideTimepicker = () => {
    setIsFocused(false);
    setShowPicker(false);
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={[styles.label, labelStyle]}>{label}</Text>}
      
      <TouchableOpacity
        onPress={showTimepicker}
        style={[
          styles.inputContainer,
          isFocused && styles.focused,
          error && styles.error,
        ]}
      >
        <Text style={styles.timeText}>{value}</Text>
        <View style={styles.iconContainer}>
          <Ionicons name="time-outline" size={20} color={COLORS.TEXT} />
        </View>
      </TouchableOpacity>
      
      {error && <Text style={styles.errorText}>{error}</Text>}

      {showPicker && (
        Platform.OS === 'ios' ? (
          <Modal
            animationType="slide"
            transparent={true}
            visible={showPicker}
            onRequestClose={hideTimepicker}
          >
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={hideTimepicker}
            >
              <View style={styles.modalContent}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity onPress={hideTimepicker}>
                    <Text style={styles.cancelButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerTitle}>Select Time</Text>
                  <TouchableOpacity onPress={hideTimepicker}>
                    <Text style={styles.doneButton}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  value={parseTimeString(value)}
                  mode="time"
                  display="spinner"
                  onChange={handleTimeChange}
                  style={styles.picker}
                />
              </View>
            </TouchableOpacity>
          </Modal>
        ) : (
          <DateTimePicker
            value={parseTimeString(value)}
            mode="time"
            is24Hour={true}
            display="default"
            onChange={handleTimeChange}
          />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.medium,
  },
  label: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.SOFT_HIGHLIGHT,
    borderRadius: BORDER_RADIUS.medium,
    backgroundColor: COLORS.WHITE,
    height: 48,
    paddingHorizontal: SPACING.medium,
  },
  timeText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  iconContainer: {
    paddingLeft: SPACING.small,
  },
  focused: {
    borderColor: COLORS.PRIMARY,
    borderWidth: 2,
  },
  error: {
    borderColor: COLORS.DANGER,
  },
  errorText: {
    color: COLORS.DANGER,
    fontSize: FONTS.SIZES.small,
    marginTop: SPACING.xs,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  pickerTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  cancelButton: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  doneButton: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.PRIMARY,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  picker: {
    height: 200,
  },
});

export default TimePickerInput;
