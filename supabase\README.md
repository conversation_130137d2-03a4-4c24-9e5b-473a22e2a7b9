# Supabase Setup for Smart AsthmaCare App

This document provides instructions for setting up Supabase for the Smart AsthmaCare asthma management app.

## Prerequisites

1. Create a Supabase account at [https://supabase.com](https://supabase.com)
2. Create a new Supabase project

## Setup Steps

### 1. Get Your Supabase Credentials

After creating your project, go to the project settings and copy:
- Project URL (under "API" → "URL")
- Project API Key (under "API" → "anon/public")

Update these values in your `.env` file:

```
SUPABASE_URL=https://your-project-url.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

### 2. Set Up Database Tables

#### Option 1: Using the SQL Editor

1. Go to the SQL Editor in your Supabase dashboard
2. Create a new query
3. Copy and paste the contents of `migration.sql` into the query editor
4. Run the query

This will create all the necessary tables with proper Row Level Security (RLS) policies.

#### Option 2: Using the Migration Script

1. Make sure your Supabase credentials are correctly set in the `.env` file
2. Run the migration script:

```bash
npm run migrate
```

This script will:
1. Create new unified tables:
   - `health_reports` (combines peak_flow_readings, symptom_reports, and weather_data)
   - `medications_unified` (combines medications, medication_reminders, and medication_logs)
   - `chat_unified` (combines chat_conversations and chat_messages)
2. Migrate data from the old tables to the new ones
3. Keep the following tables unchanged:
   - `profiles`
   - `user_settings`
   - `action_plans`
   - `notifications`

### 3. Enable Authentication Providers

#### Email/Password Authentication

1. Go to "Authentication" → "Providers"
2. Make sure "Email" is enabled
3. Configure email templates if desired

#### Google Authentication

1. Go to "Authentication" → "Providers"
2. Enable "Google"
3. Create a Google OAuth client:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or use an existing one
   - Go to "APIs & Services" → "Credentials"
   - Create an OAuth client ID
   - Add your Supabase project's OAuth redirect URL (found in Supabase dashboard)
   - Copy the Client ID and Client Secret
4. Enter the Client ID and Client Secret in Supabase
5. Update your `.env` file with the Google Web Client ID:

```
GOOGLE_WEB_CLIENT_ID=your-google-web-client-id.apps.googleusercontent.com
```

### 4. Set Up Storage (Optional)

If you plan to use Supabase Storage for user profile images or other files:

1. Go to "Storage" in your Supabase dashboard
2. Create a new bucket called "profiles"
3. Set the bucket's privacy to "Private"
4. Create RLS policies to allow users to access their own files

Example policy:

```sql
CREATE POLICY "Users can upload their own profile pictures"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'profiles' AND
  auth.uid()::text = (storage.foldername(name))[1]
);
```

## Testing Your Setup

After completing the setup, you should be able to:

1. Sign up with email and password
2. Sign in with Google
3. Create and update user profiles
4. Store and retrieve data from the database

## Troubleshooting

- **Authentication Issues**: Check the RLS policies and make sure they're correctly set up
- **Database Errors**: Check the SQL logs in the Supabase dashboard
- **API Errors**: Verify your API keys and URLs are correct

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript/introduction)
- [Supabase React Native Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-react-native)
