/**
 * mHealth App i18n Configuration
 */

import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';
import translations from './translations';

// Create a new i18n instance
const i18n = new I18n(translations);

// Set English as the default locale
i18n.locale = 'en';

// When a value is missing from a language it'll fallback to another language with the key present
i18n.enableFallback = true;
i18n.defaultLocale = 'en';

// Function to set the language
export const setLanguage = async (languageCode) => {
  try {
    i18n.locale = languageCode;
    await AsyncStorage.setItem('userLanguage', languageCode);
    return true;
  } catch (error) {
    console.error('Error setting language:', error);
    return false;
  }
};

// Function to get the current language
export const getCurrentLanguage = async () => {
  try {
    const userLanguage = await AsyncStorage.getItem('userLanguage');
    return userLanguage || i18n.locale;
  } catch (error) {
    console.error('Error getting language:', error);
    return i18n.locale;
  }
};

// Function to initialize language from storage
export const initLanguage = async () => {
  try {
    const userLanguage = await AsyncStorage.getItem('userLanguage');
    if (userLanguage) {
      i18n.locale = userLanguage;
    } else {
      // If no language is stored, set English as default and save it
      i18n.locale = 'en';
      await AsyncStorage.setItem('userLanguage', 'en');
    }
  } catch (error) {
    console.error('Error initializing language:', error);
    // In case of error, ensure English is set
    i18n.locale = 'en';
  }
};

export default i18n;
