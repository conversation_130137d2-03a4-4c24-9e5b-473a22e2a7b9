/**
 * Clinic Directory Screen
 * Uses location services to find nearby healthcare facilities
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Input from '../components/Input';
import Button from '../components/Button';
import * as Location from 'expo-location';

// Using OpenStreetMap's Overpass API to find real healthcare facilities
// This is a free and open API that doesn't require authentication
const OVERPASS_API_URL = 'https://overpass-api.de/api/interpreter';

const ClinicDirectoryScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [clinicsData, setClinicsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userLocation, setUserLocation] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info',
    showCancel: false,
    confirmText: 'OK',
    cancelText: 'Cancel',
    onConfirm: null
  });

  // Request location permission and get user's location
  useEffect(() => {
    const getLocationAndFetchClinics = async () => {
      try {
        setLoading(true);
        console.log('Starting location detection process');

        // Request location permission
        const { status } = await Location.requestForegroundPermissionsAsync();
        console.log(`Location permission status: ${status}`);

        if (status !== 'granted') {
          console.log('Location permission denied, using default location');
          showAlert({
            title: 'Location Permission Required',
            message: 'Using default location. Please enable location services to find healthcare facilities near you.',
            type: 'warning'
          });

          // Use default location (Kampala, Uganda) if permission denied
          const defaultLocation = {
            latitude: 0.3476,
            longitude: 32.5825
          };

          setUserLocation(defaultLocation);
          await fetchNearbyClinics(defaultLocation.latitude, defaultLocation.longitude);
          return;
        }

        // Try to get last known location first (faster)
        console.log('Trying to get last known location');
        let location;

        try {
          const lastKnownLocation = await Location.getLastKnownPositionAsync();
          if (lastKnownLocation) {
            console.log('Using last known location');
            location = lastKnownLocation;
          }
        } catch (lastKnownError) {
          console.log('Error getting last known location:', lastKnownError);
        }

        // If no last known location, get current position
        if (!location) {
          console.log('Getting current position');
          try {
            location = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.Balanced,
              timeout: 15000 // 15 second timeout
            });
            console.log('Successfully got current position');
          } catch (currentPositionError) {
            console.error('Error getting current position:', currentPositionError);
            throw currentPositionError;
          }
        }

        if (location) {
          console.log(`Location obtained: ${location.coords.latitude}, ${location.coords.longitude}`);
          setUserLocation({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude
          });

          // Fetch nearby healthcare facilities
          await fetchNearbyClinics(location.coords.latitude, location.coords.longitude);
        } else {
          throw new Error('Could not obtain location');
        }
      } catch (error) {
        console.error('Error in location process:', error);
        setErrorMessage('Unable to access your location. Using default location instead.');

        // Use default location (Kampala, Uganda) if location access fails
        const defaultLocation = {
          latitude: 0.3476,
          longitude: 32.5825
        };

        console.log(`Using default location: ${defaultLocation.latitude}, ${defaultLocation.longitude}`);
        setUserLocation(defaultLocation);
        await fetchNearbyClinics(defaultLocation.latitude, defaultLocation.longitude);
      }
    };

    getLocationAndFetchClinics();
  }, []);

  // Function to show custom alert
  const showAlert = (config) => {
    setAlertConfig({
      ...alertConfig,
      ...config
    });
    setAlertVisible(true);
  };

  // Fetch nearby healthcare facilities using OpenStreetMap's Overpass API
  const fetchNearbyClinics = async (latitude, longitude) => {
    try {
      setLoading(true);
      console.log(`Searching for healthcare facilities near: ${latitude}, ${longitude}`);

      // Calculate bounding box (approximately 10km radius)
      const radius = 0.1; // roughly 10km in degrees
      const bbox = `${latitude - radius},${longitude - radius},${latitude + radius},${longitude + radius}`;

      // Overpass query to find healthcare facilities
      // This query looks for hospitals, clinics, doctors, and other healthcare amenities
      const query = `
        [out:json];
        (
          node["amenity"="hospital"](${bbox});
          node["amenity"="clinic"](${bbox});
          node["amenity"="doctors"](${bbox});
          node["healthcare"](${bbox});
          way["amenity"="hospital"](${bbox});
          way["amenity"="clinic"](${bbox});
          way["amenity"="doctors"](${bbox});
          way["healthcare"](${bbox});
          relation["amenity"="hospital"](${bbox});
          relation["amenity"="clinic"](${bbox});
          relation["amenity"="doctors"](${bbox});
          relation["healthcare"](${bbox});
        );
        out body;
        >;
        out skel qt;
      `;

      console.log(`Fetching data from Overpass API with bbox: ${bbox}`);

      const response = await fetch(OVERPASS_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `data=${encodeURIComponent(query)}`
      });

      if (!response.ok) {
        throw new Error(`Overpass API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Overpass API response: Found ${data.elements ? data.elements.length : 0} elements`);

      if (!data.elements || data.elements.length === 0) {
        throw new Error('No healthcare facilities found in your area');
      }

      // Filter out non-healthcare elements and duplicates
      const healthcareFacilities = data.elements.filter(element => {
        // Only include elements with lat/lon coordinates (needed for directions)
        if (!element.lat || !element.lon) return false;

        // Only include elements with tags
        if (!element.tags) return false;

        // Include elements with healthcare-related tags
        return (
          element.tags.amenity === 'hospital' ||
          element.tags.amenity === 'clinic' ||
          element.tags.amenity === 'doctors' ||
          element.tags.healthcare ||
          element.tags.medical_specialty ||
          element.tags.building === 'hospital' ||
          element.tags.name?.toLowerCase().includes('hospital') ||
          element.tags.name?.toLowerCase().includes('clinic') ||
          element.tags.name?.toLowerCase().includes('medical') ||
          element.tags.name?.toLowerCase().includes('health')
        );
      });

      // Remove duplicates (sometimes the same facility appears multiple times)
      const uniqueFacilities = [];
      const seenIds = new Set();

      healthcareFacilities.forEach(facility => {
        if (!seenIds.has(facility.id)) {
          seenIds.add(facility.id);
          uniqueFacilities.push(facility);
        }
      });

      console.log(`Filtered to ${uniqueFacilities.length} unique healthcare facilities`);

      // Process and format the results
      const formattedClinics = uniqueFacilities.map(facility => {
        // Determine facility type based on tags
        let facilityType = 'Healthcare';
        let specializes = false;

        if (facility.tags) {
          if (facility.tags.amenity === 'hospital') {
            facilityType = 'Hospital';
          } else if (facility.tags.amenity === 'clinic') {
            facilityType = 'Clinic';
          } else if (facility.tags.amenity === 'doctors') {
            facilityType = 'Doctor';
          } else if (facility.tags.healthcare === 'hospital') {
            facilityType = 'Hospital';
          } else if (facility.tags.healthcare === 'clinic') {
            facilityType = 'Clinic';
          } else if (facility.tags.healthcare === 'doctor') {
            facilityType = 'Doctor';
          } else if (facility.tags.healthcare) {
            facilityType = facility.tags.healthcare.charAt(0).toUpperCase() + facility.tags.healthcare.slice(1);
          }

          // Check if it specializes in respiratory care
          const tagValues = Object.values(facility.tags).map(v => v.toString().toLowerCase());
          specializes = tagValues.some(value =>
            value.includes('pulmonary') ||
            value.includes('respiratory') ||
            value.includes('asthma') ||
            value.includes('allergy') ||
            value.includes('lung')
          );

          // Also check the name for specialization hints
          if (facility.tags.name) {
            const nameLower = facility.tags.name.toLowerCase();
            if (
              nameLower.includes('pulmonary') ||
              nameLower.includes('respiratory') ||
              nameLower.includes('asthma') ||
              nameLower.includes('allergy') ||
              nameLower.includes('lung')
            ) {
              specializes = true;
            }
          }
        }

        // Calculate distance in kilometers
        const facilityLat = facility.lat;
        const facilityLon = facility.lon;
        const distance = calculateDistance(
          latitude,
          longitude,
          facilityLat,
          facilityLon
        );

        // Generate services based on facility type and tags
        const services = [];
        if (facilityType === 'Hospital') {
          services.push('Hospital Care');
          if (facility.tags.emergency === 'yes') {
            services.push('Emergency Care');
          }
        } else if (facilityType === 'Clinic') {
          services.push('Clinical Care');
        } else if (facilityType === 'Doctor') {
          services.push('Medical Consultation');
        }

        if (facility.tags.healthcare_speciality) {
          const specialities = facility.tags.healthcare_speciality.split(';');
          specialities.forEach(spec => {
            const formattedSpec = spec.trim().charAt(0).toUpperCase() + spec.trim().slice(1);
            services.push(formattedSpec);
          });
        }

        if (specializes) {
          services.push('Respiratory Care');
        }

        if (services.length === 0) {
          services.push('Healthcare Services');
        }

        // Format the address
        let address = 'Address not available';
        if (facility.tags) {
          const addressParts = [];
          if (facility.tags['addr:housenumber']) addressParts.push(facility.tags['addr:housenumber']);
          if (facility.tags['addr:street']) addressParts.push(facility.tags['addr:street']);
          if (facility.tags['addr:city']) addressParts.push(facility.tags['addr:city']);
          if (facility.tags['addr:postcode']) addressParts.push(facility.tags['addr:postcode']);
          if (facility.tags['addr:country']) addressParts.push(facility.tags['addr:country']);

          if (addressParts.length > 0) {
            address = addressParts.join(', ');
          }
        }

        // Determine opening hours
        let openingHours = [];
        let openNow = false;

        if (facility.tags.opening_hours) {
          openingHours = [facility.tags.opening_hours];

          // Simple check if it might be open now (24/7 or similar)
          if (facility.tags.opening_hours.includes('24/7')) {
            openNow = true;
          }
        }

        // Create a more descriptive name if the original is missing
        let facilityName = facility.tags.name;
        if (!facilityName) {
          if (facility.tags.operator) {
            facilityName = facility.tags.operator;
          } else {
            // Generate a name based on available information
            const locationPart = facility.tags['addr:city'] || facility.tags['addr:suburb'] || '';
            facilityName = `${facilityType} ${locationPart}`.trim();
            if (facilityName === facilityType) {
              facilityName = `${facilityType} #${facility.id.toString().slice(-4)}`;
            }
          }
        }

        return {
          id: facility.id.toString(),
          name: facilityName,
          type: facilityType,
          address: address,
          phone: facility.tags.phone || 'Not available',
          website: facility.tags.website || null,
          distance: `${distance.toFixed(1)} km`,
          distanceValue: distance,
          specializes: specializes,
          services: services,
          openNow: openNow,
          openingHours: openingHours,
          // Store the exact coordinates for directions
          coordinates: {
            latitude: facilityLat,
            longitude: facilityLon
          }
        };
      });

      // Sort by distance
      formattedClinics.sort((a, b) => a.distanceValue - b.distanceValue);

      console.log(`Processed ${formattedClinics.length} healthcare facilities`);
      setClinicsData(formattedClinics);

    } catch (error) {
      console.error('Error fetching nearby clinics:', error);
      setErrorMessage(`Unable to fetch nearby healthcare facilities: ${error.message}`);

      // Use mock data as fallback
      const mockData = getMockClinicData();
      console.log(`Using ${mockData.length} mock data entries as fallback`);
      setClinicsData(mockData);

      // Show alert to user
      showAlert({
        title: 'Connection Issue',
        message: `We couldn't find healthcare facilities near you: ${error.message}. Showing sample data instead.`,
        type: 'warning',
        confirmText: 'OK'
      });
    } finally {
      setLoading(false);
    }
  };

  // No longer needed - using real API data instead

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in km
    return distance;
  };

  const deg2rad = (deg) => {
    return deg * (Math.PI/180);
  };

  // This function is used directly in the refresh button's onPress

  // Mock data for clinics (fallback)
  const getMockClinicData = () => {
    // If we have user location, calculate realistic distances
    const calculateMockDistance = (baseDist) => {
      if (!userLocation) return baseDist;

      // Add some randomness to make it look more realistic
      const randomFactor = Math.random() * 0.5 + 0.75; // Between 0.75 and 1.25
      return baseDist * randomFactor;
    };

    const mockData = [
      {
        id: 'mock-1',
        name: 'Kampala Asthma Center',
        type: 'Specialist',
        address: '45 Nakasero Road, Kampala',
        phone: '+256 414 123 456',
        website: 'https://example.com/kampala-asthma',
        specializes: true,
        services: ['Pulmonology', 'Allergy Testing', 'Asthma Education'],
        openNow: true,
        openingHours: [
          'Monday: 8:00 AM – 5:00 PM',
          'Tuesday: 8:00 AM – 5:00 PM',
          'Wednesday: 8:00 AM – 5:00 PM',
          'Thursday: 8:00 AM – 5:00 PM',
          'Friday: 8:00 AM – 5:00 PM',
          'Saturday: 9:00 AM – 1:00 PM',
          'Sunday: Closed'
        ]
      },
      {
        id: 'mock-2',
        name: 'Mulago Respiratory Clinic',
        type: 'Hospital',
        address: 'Upper Mulago Hill Road, Kampala',
        phone: '+256 414 541 884',
        website: null,
        specializes: true,
        services: ['Pulmonology', 'Emergency Care', 'Pediatric Asthma'],
        openNow: true,
        openingHours: [
          'Monday: 24 hours',
          'Tuesday: 24 hours',
          'Wednesday: 24 hours',
          'Thursday: 24 hours',
          'Friday: 24 hours',
          'Saturday: 24 hours',
          'Sunday: 24 hours'
        ]
      },
      {
        id: 'mock-3',
        name: 'Nsambya Family Practice',
        type: 'Primary Care',
        address: 'Nsambya Road, Kampala',
        phone: '+256 414 267 012',
        website: 'https://example.com/nsambya',
        specializes: false,
        services: ['Family Medicine', 'General Checkups'],
        openNow: false,
        openingHours: [
          'Monday: 8:30 AM – 4:30 PM',
          'Tuesday: 8:30 AM – 4:30 PM',
          'Wednesday: 8:30 AM – 4:30 PM',
          'Thursday: 8:30 AM – 4:30 PM',
          'Friday: 8:30 AM – 4:30 PM',
          'Saturday: Closed',
          'Sunday: Closed'
        ]
      },
      {
        id: 'mock-4',
        name: 'Kibuli Medical Center',
        type: 'Hospital',
        address: 'Kibuli Road, Kampala',
        phone: '+256 414 268 222',
        website: 'https://example.com/kibuli',
        specializes: false,
        services: ['General Medicine', 'Emergency Care'],
        openNow: true,
        openingHours: [
          'Monday: 24 hours',
          'Tuesday: 24 hours',
          'Wednesday: 24 hours',
          'Thursday: 24 hours',
          'Friday: 24 hours',
          'Saturday: 24 hours',
          'Sunday: 24 hours'
        ]
      },
      {
        id: 'mock-5',
        name: 'Mengo Hospital',
        type: 'Hospital',
        address: 'Albert Cook Road, Kampala',
        phone: '+256 414 270 222',
        website: 'https://example.com/mengo',
        specializes: false,
        services: ['General Medicine', 'Surgery', 'Pediatrics'],
        openNow: true,
        openingHours: [
          'Monday: 24 hours',
          'Tuesday: 24 hours',
          'Wednesday: 24 hours',
          'Thursday: 24 hours',
          'Friday: 24 hours',
          'Saturday: 24 hours',
          'Sunday: 24 hours'
        ]
      }
    ];

    // Add distances to each mock entry
    const baseDistances = [2.3, 3.1, 4.8, 5.5, 6.2];
    return mockData.map((item, index) => {
      const distanceValue = calculateMockDistance(baseDistances[index]);
      return {
        ...item,
        distance: `${distanceValue.toFixed(1)} km`,
        distanceValue: distanceValue
      };
    });
  };

  // Filter clinics based on search query and selected filter
  const filteredClinics = clinicsData.filter((clinic) => {
    const matchesSearch =
      clinic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      clinic.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      clinic.type.toLowerCase().includes(searchQuery.toLowerCase());

    if (selectedFilter === 'all') {
      return matchesSearch;
    } else if (selectedFilter === 'specialist') {
      return matchesSearch && clinic.specializes;
    } else if (selectedFilter === 'nearby') {
      return matchesSearch && clinic.distanceValue < 5.0;
    } else if (selectedFilter === 'open') {
      return matchesSearch && clinic.openNow;
    }

    return matchesSearch;
  });

  const handleCall = (phoneNumber) => {
    if (phoneNumber && phoneNumber !== 'Not available') {
      Linking.openURL(`tel:${phoneNumber}`);
    } else {
      showAlert({
        title: 'No Phone Number',
        message: 'This healthcare facility does not have a phone number available.',
        type: 'info'
      });
    }
  };

  const handleDirections = (clinic) => {
    // Use exact coordinates if available, otherwise fall back to address
    if (clinic.coordinates && clinic.coordinates.latitude && clinic.coordinates.longitude) {
      const { latitude, longitude } = clinic.coordinates;
      const label = encodeURIComponent(clinic.name);

      // Different URL formats for different platforms
      if (Platform.OS === 'ios') {
        Linking.openURL(`https://maps.apple.com/?ll=${latitude},${longitude}&q=${label}`);
      } else {
        Linking.openURL(`https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}&destination_place_id=${clinic.id}&travelmode=driving`);
      }
    } else {
      // Fallback to address-based directions
      const encodedAddress = encodeURIComponent(clinic.address);
      Linking.openURL(`https://maps.google.com/?q=${encodedAddress}`);
    }
  };

  const handleWebsite = (website) => {
    if (website) {
      Linking.openURL(website);
    } else {
      showAlert({
        title: 'No Website',
        message: 'This healthcare facility does not have a website available.',
        type: 'info'
      });
    }
  };

  const renderOpeningHours = (openingHours) => {
    if (!openingHours || openingHours.length === 0) {
      return (
        <View style={styles.hoursContainer}>
          <Text style={styles.hoursTitle}>Opening Hours</Text>
          <Text style={styles.hoursText}>Information not available</Text>
        </View>
      );
    }

    return (
      <View style={styles.hoursContainer}>
        <Text style={styles.hoursTitle}>Opening Hours</Text>
        {openingHours.map((hours, index) => (
          <Text key={index} style={styles.hoursText}>{hours}</Text>
        ))}
      </View>
    );
  };

  const renderClinicItem = (clinic) => (
    <Card key={clinic.id} style={styles.clinicCard}>
      <View style={styles.clinicHeader}>
        <View style={styles.nameContainer}>
          <Text style={styles.clinicName}>{clinic.name}</Text>
          <Text style={styles.clinicType}>{clinic.type}</Text>
        </View>
        <View style={styles.statusContainer}>
          {clinic.specializes && (
            <View style={styles.specializesTag}>
              <Ionicons name="checkmark-circle" size={16} color={COLORS.PRIMARY} />
              <Text style={styles.specializesText}>Asthma Specialist</Text>
            </View>
          )}
          {clinic.openNow !== undefined && (
            <View style={[styles.openStatusTag, clinic.openNow ? styles.openTag : styles.closedTag]}>
              <Ionicons
                name={clinic.openNow ? "time" : "time-outline"}
                size={16}
                color={clinic.openNow ? COLORS.SUCCESS : COLORS.ERROR}
              />
              <Text style={[styles.openStatusText, clinic.openNow ? styles.openText : styles.closedText]}>
                {clinic.openNow ? 'Open Now' : 'Closed'}
              </Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.clinicDetails}>
        <View style={styles.detailItem}>
          <Ionicons name="location" size={16} color={COLORS.TEXT} />
          <Text style={styles.detailText}>{clinic.address}</Text>
        </View>
        <View style={styles.detailItem}>
          <Ionicons name="call" size={16} color={COLORS.TEXT} />
          <Text style={styles.detailText}>{clinic.phone}</Text>
        </View>
        <View style={styles.detailItem}>
          <Ionicons name="navigate" size={16} color={COLORS.TEXT} />
          <Text style={styles.detailText}>
            Distance: {clinic.distance}
          </Text>
        </View>
        {clinic.website && (
          <View style={styles.detailItem}>
            <Ionicons name="globe" size={16} color={COLORS.TEXT} />
            <Text style={[styles.detailText, styles.websiteText]} numberOfLines={1} ellipsizeMode="tail">
              {clinic.website}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.servicesContainer}>
        {clinic.services.map((service, index) => (
          <View key={index} style={styles.serviceTag}>
            <Text style={styles.serviceText}>{service}</Text>
          </View>
        ))}
      </View>

      {/* Collapsible opening hours section */}
      {renderOpeningHours(clinic.openingHours)}

      <View style={styles.actionsContainer}>
        <Button
          title="Call"
          onPress={() => handleCall(clinic.phone)}
          icon={<Ionicons name="call" size={16} color={COLORS.WHITE} />}
          style={styles.actionButton}
        />
        <Button
          title="Directions"
          onPress={() => handleDirections(clinic)}
          type="outline"
          icon={<Ionicons name="navigate" size={16} color={COLORS.PRIMARY} />}
          style={styles.actionButton}
        />
        {clinic.website && (
          <Button
            title="Website"
            onPress={() => handleWebsite(clinic.website)}
            type="outline"
            icon={<Ionicons name="globe" size={16} color={COLORS.PRIMARY} />}
            style={styles.actionButton}
          />
        )}
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Healthcare Facilities"
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightComponent={
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={() => {
              if (userLocation) {
                // Show a loading indicator and refresh the data
                setLoading(true);

                // Clear any previous error messages
                setErrorMessage(null);

                // Fetch real healthcare facilities using Foursquare API
                fetchNearbyClinics(userLocation.latitude, userLocation.longitude);

                // Show a success message after refresh (only if no errors)
                setTimeout(() => {
                  if (!errorMessage) {
                    showAlert({
                      title: 'Updated',
                      message: 'Healthcare facility information has been refreshed with real data from your location.',
                      type: 'success',
                      confirmText: 'Great!'
                    });
                  }
                }, 2500);
              } else {
                // If no location is available, show an alert
                showAlert({
                  title: 'Location Required',
                  message: 'Please enable location services to find healthcare facilities near you.',
                  type: 'warning',
                  confirmText: 'OK'
                });
              }
            }}
          >
            <Ionicons name="refresh" size={24} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        }
      />

      <View style={styles.searchContainer}>
        <Input
          placeholder="Search by name, address, or type..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          icon={<Ionicons name="search" size={20} color={COLORS.TEXT} />}
          style={styles.searchInput}
        />
      </View>

      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[
            styles.filterOption,
            selectedFilter === 'all' && styles.selectedFilter,
          ]}
          onPress={() => setSelectedFilter('all')}
        >
          <Text
            style={[
              styles.filterText,
              selectedFilter === 'all' && styles.selectedFilterText,
            ]}
          >
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.filterOption,
            selectedFilter === 'specialist' && styles.selectedFilter,
          ]}
          onPress={() => setSelectedFilter('specialist')}
        >
          <Text
            style={[
              styles.filterText,
              selectedFilter === 'specialist' && styles.selectedFilterText,
            ]}
          >
            Specialists
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.filterOption,
            selectedFilter === 'nearby' && styles.selectedFilter,
          ]}
          onPress={() => setSelectedFilter('nearby')}
        >
          <Text
            style={[
              styles.filterText,
              selectedFilter === 'nearby' && styles.selectedFilterText,
            ]}
          >
            Nearby
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.filterOption,
            selectedFilter === 'open' && styles.selectedFilter,
          ]}
          onPress={() => setSelectedFilter('open')}
        >
          <Text
            style={[
              styles.filterText,
              selectedFilter === 'open' && styles.selectedFilterText,
            ]}
          >
            Open Now
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={loading ? styles.loadingContentContainer : null}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>Finding healthcare facilities near you...</Text>
            <Text style={styles.loadingSubText}>This may take a moment</Text>
          </View>
        ) : errorMessage ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={48} color={COLORS.ERROR} />
            <Text style={styles.errorText}>{errorMessage}</Text>
            <Button
              title="Try Again"
              onPress={() => {
                if (userLocation) {
                  fetchNearbyClinics(userLocation.latitude, userLocation.longitude);
                }
              }}
              style={styles.retryButton}
            />
          </View>
        ) : filteredClinics.length > 0 ? (
          filteredClinics.map(renderClinicItem)
        ) : (
          <View style={styles.noResultsContainer}>
            <Ionicons name="search" size={48} color={COLORS.SOFT_HIGHLIGHT} />
            <Text style={styles.noResultsText}>No healthcare facilities found matching your search</Text>
          </View>
        )}
      </ScrollView>

      {/* Custom Alert */}
      {alertVisible && (
        <View style={styles.alertOverlay}>
          <View style={styles.alertContainer}>
            <View style={[styles.alertHeader, styles[`alert${alertConfig.type}Header`]]}>
              <Ionicons
                name={alertConfig.type === 'info' ? 'information-circle' :
                      alertConfig.type === 'warning' ? 'warning' :
                      alertConfig.type === 'success' ? 'checkmark-circle' : 'alert-circle'}
                size={24}
                color={COLORS.WHITE}
              />
              <Text style={styles.alertTitle}>{alertConfig.title}</Text>
            </View>
            <View style={styles.alertBody}>
              <Text style={styles.alertMessage}>{alertConfig.message}</Text>
            </View>
            <View style={styles.alertActions}>
              {alertConfig.showCancel && (
                <TouchableOpacity
                  style={[styles.alertButton, styles.alertCancelButton]}
                  onPress={() => setAlertVisible(false)}
                >
                  <Text style={styles.alertCancelButtonText}>{alertConfig.cancelText}</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={[styles.alertButton, styles.alertConfirmButton]}
                onPress={() => {
                  setAlertVisible(false);
                  if (alertConfig.onConfirm) alertConfig.onConfirm();
                }}
              >
                <Text style={styles.alertConfirmButtonText}>{alertConfig.confirmText}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  searchContainer: {
    padding: SPACING.medium,
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  searchInput: {
    marginBottom: 0,
  },
  filtersContainer: {
    flexDirection: 'row',
    padding: SPACING.small,
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
    flexWrap: 'wrap',
  },
  filterOption: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
    marginRight: SPACING.small,
    marginBottom: SPACING.xs,
  },
  selectedFilter: {
    backgroundColor: COLORS.PRIMARY + '20',
  },
  filterText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  selectedFilterText: {
    color: COLORS.PRIMARY,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
  },
  loadingContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clinicCard: {
    marginBottom: SPACING.medium,
  },
  clinicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.small,
  },
  nameContainer: {
    flex: 1,
    marginRight: SPACING.small,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  clinicName: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: 2,
  },
  clinicType: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  specializesTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.PRIMARY + '15',
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
    marginBottom: SPACING.xs,
  },
  specializesText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.PRIMARY,
    marginLeft: 4,
  },
  openStatusTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
  },
  openTag: {
    backgroundColor: '#E6F4EA', // Light green
  },
  closedTag: {
    backgroundColor: '#FEEAE6', // Light red
  },
  openStatusText: {
    fontSize: FONTS.SIZES.small,
    marginLeft: 4,
  },
  openText: {
    color: '#34A853', // Green
  },
  closedText: {
    color: '#EA4335', // Red
  },
  clinicDetails: {
    marginBottom: SPACING.medium,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  detailText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginLeft: SPACING.small,
    flex: 1,
  },
  websiteText: {
    color: COLORS.PRIMARY,
    textDecorationLine: 'underline',
  },
  servicesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.medium,
  },
  serviceTag: {
    backgroundColor: COLORS.LIGHT_BG,
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
    marginRight: SPACING.small,
    marginBottom: SPACING.xs,
  },
  serviceText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
  },
  hoursContainer: {
    marginBottom: SPACING.medium,
    backgroundColor: COLORS.LIGHT_BG + '80',
    padding: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
  },
  hoursTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  hoursText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xxl,
  },
  noResultsText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginTop: SPACING.medium,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xxl,
  },
  loadingText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    fontWeight: FONTS.WEIGHTS.medium,
    marginTop: SPACING.medium,
    textAlign: 'center',
  },
  loadingSubText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '99',
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xxl,
  },
  errorText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginTop: SPACING.medium,
    marginBottom: SPACING.large,
  },
  retryButton: {
    minWidth: 120,
  },
  refreshButton: {
    padding: SPACING.xs,
  },
  // Custom Alert Styles
  alertOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  alertContainer: {
    width: '80%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    overflow: 'hidden',
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.medium,
  },
  alertinfoHeader: {
    backgroundColor: COLORS.PRIMARY,
  },
  alertwarningHeader: {
    backgroundColor: '#F4B400', // Yellow
  },
  alertsuccessHeader: {
    backgroundColor: '#34A853', // Green
  },
  alerterrorHeader: {
    backgroundColor: '#EA4335', // Red
  },
  alertTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.WHITE,
    marginLeft: SPACING.small,
  },
  alertBody: {
    padding: SPACING.medium,
  },
  alertMessage: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    lineHeight: 22,
  },
  alertActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_BG,
  },
  alertButton: {
    flex: 1,
    padding: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  alertCancelButton: {
    borderRightWidth: 1,
    borderRightColor: COLORS.LIGHT_BG,
  },
  alertCancelButtonText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  alertConfirmButton: {
    backgroundColor: COLORS.PRIMARY + '10',
  },
  alertConfirmButtonText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.PRIMARY,
    fontWeight: FONTS.WEIGHTS.medium,
  },
});

export default ClinicDirectoryScreen;
