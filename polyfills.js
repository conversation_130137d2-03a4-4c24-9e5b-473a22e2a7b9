/**
 * Global polyfills for React Native
 * This file provides polyfills for Node.js functionality that's missing in React Native
 */

// Polyfill for Math.random.seed
if (!Math.random.seed) {
  Math.random.seed = function(s) {
    return function() {
      s = Math.sin(s) * 10000;
      return s - Math.floor(s);
    };
  };
}

// Polyfill for crypto
if (!global.crypto) {
  global.crypto = {
    getRandomValues: function(arr) {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
    subtle: {
      digest: async function(algorithm, data) {
        // This is a very simplified mock implementation
        // In a real app, you'd want to use a proper crypto library
        console.warn('Using mock crypto.subtle.digest');
        return new Uint8Array(32); // Return a dummy hash
      }
    }
  };
}

// Polyfill for Buffer
if (!global.Buffer) {
  global.Buffer = require('buffer').Buffer;
}

// Polyfill for process
if (!global.process) {
  global.process = {
    env: {},
    nextTick: function(callback) {
      setTimeout(callback, 0);
    },
    version: '',
    versions: { node: '0' },
    platform: 'browser'
  };
}

// Polyfill for setImmediate
if (!global.setImmediate) {
  global.setImmediate = function(callback) {
    setTimeout(callback, 0);
  };
}

// Polyfill for clearImmediate
if (!global.clearImmediate) {
  global.clearImmediate = function(id) {
    clearTimeout(id);
  };
}

// Export a function to initialize all polyfills
export default function initPolyfills() {
  console.log('Polyfills initialized');
}
