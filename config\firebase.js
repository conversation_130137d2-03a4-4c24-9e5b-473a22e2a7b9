/**
 * Firebase configuration for Smart AsthmaCare app
 */

// Import the functions you need from the SDKs you need
import { Platform } from 'react-native';
import { initializeApp, getApps, getApp } from 'firebase/app';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import Firebase modules dynamically to prevent initialization errors
let getAuth, connectAuthEmulator, initializeAuth, getReactNativePersistence;
let getFirestore, connectFirestoreEmulator;
let getFunctions, httpsCallable, connectFunctionsEmulator;

try {
  // Try to import Firebase Auth
  const firebaseAuth = require('firebase/auth');
  getAuth = firebaseAuth.getAuth;
  connectAuthEmulator = firebaseAuth.connectAuthEmulator;
  initializeAuth = firebaseAuth.initializeAuth;
  getReactNativePersistence = firebaseAuth.getReactNativePersistence;
} catch (error) {
  console.error('Failed to import firebase/auth:', error);
}

try {
  // Try to import Firebase Firestore
  const firebaseFirestore = require('firebase/firestore');
  getFirestore = firebaseFirestore.getFirestore;
  connectFirestoreEmulator = firebaseFirestore.connectFirestoreEmulator;
} catch (error) {
  console.error('Failed to import firebase/firestore:', error);
}

try {
  // Try to import Firebase Functions
  const firebaseFunctions = require('firebase/functions');
  getFunctions = firebaseFunctions.getFunctions;
  httpsCallable = firebaseFunctions.httpsCallable;
  connectFunctionsEmulator = firebaseFunctions.connectFunctionsEmulator;
} catch (error) {
  console.error('Failed to import firebase/functions:', error);
}

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAhJg8XPcctIi4R5WDDK1jL3k_o2Rz7XPI",
  authDomain: "capable-bliss-459720-t1.firebaseapp.com",
  projectId: "capable-bliss-459720-t1",
  storageBucket: "capable-bliss-459720-t1.appspot.com",
  messagingSenderId: "261917236585",
  appId: "1:261917236585:web:a3b5c7d8e9f0g1h2i3j4k5",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase - only initialize once
let app;
let auth;

// Wrap initialization in a try-catch to prevent crashes
try {
  // Initialize Firebase app
  if (getApps().length === 0) {
    console.log("Initializing new Firebase app");
    app = initializeApp(firebaseConfig);
  } else {
    console.log("Using existing Firebase app");
    app = getApp();
  }

  // Initialize Auth with AsyncStorage persistence if available
  if (initializeAuth && getReactNativePersistence) {
    try {
      console.log("Initializing Auth with AsyncStorage persistence");
      auth = initializeAuth(app, {
        persistence: getReactNativePersistence(AsyncStorage)
      });
      console.log("Auth initialized successfully with persistence");
    } catch (authError) {
      console.error("Error initializing Auth with persistence:", authError);
      // Fall through to next method
    }
  }

  // Try standard auth initialization if first method failed or wasn't available
  if (!auth && getAuth) {
    try {
      console.log("Trying standard Auth initialization as fallback");
      auth = getAuth(app);
      console.log("Standard Auth initialized successfully");
    } catch (standardAuthError) {
      console.error("Error initializing standard Auth:", standardAuthError);
      // Fall through to mock implementation
    }
  }

  // If auth is still not initialized, create a mock auth object
  if (!auth) {
    console.log("Creating mock Auth object");
    auth = {
      currentUser: null,
      onAuthStateChanged: (callback) => {
        console.log("Mock auth: onAuthStateChanged called");
        callback(null);
        return () => {};
      },
      signInWithEmailAndPassword: () => Promise.reject(new Error("Auth not available")),
      createUserWithEmailAndPassword: () => Promise.reject(new Error("Auth not available")),
      signOut: () => Promise.resolve(),
      emulator: { disableWarnings: () => {} }
    };
    console.log("Using mock Auth object");
  }
} catch (error) {
  console.error("Critical Firebase initialization error:", error);

  // Create mock objects to prevent crashes
  app = {};
  auth = {
    currentUser: null,
    onAuthStateChanged: (callback) => {
      console.log("Mock auth (fallback): onAuthStateChanged called");
      callback(null);
      return () => {};
    },
    signInWithEmailAndPassword: () => Promise.reject(new Error("Auth not available")),
    createUserWithEmailAndPassword: () => Promise.reject(new Error("Auth not available")),
    signOut: () => Promise.resolve(),
    emulator: () => {
      console.log("Mock auth emulator called");
      return null;
    }
  };
  console.log("Using mock Firebase objects due to initialization failure");
}

// Initialize other Firebase services
let db = null;
let functions = null;

// Initialize Firestore if available
if (getFirestore) {
  try {
    db = getFirestore(app);
    console.log('Firestore initialized successfully');
  } catch (firestoreError) {
    console.error('Failed to initialize Firestore:', firestoreError);
    // Create a mock db object
    db = {
      collection: () => ({
        doc: () => ({
          get: () => Promise.resolve({ exists: false, data: () => null }),
          set: () => Promise.resolve(),
          update: () => Promise.resolve(),
          delete: () => Promise.resolve()
        }),
        add: () => Promise.resolve({ id: 'mock-id' }),
        where: () => ({ get: () => Promise.resolve({ docs: [] }) })
      })
    };
    console.log('Using mock Firestore object');
  }
}

// Initialize Functions if available
if (getFunctions) {
  try {
    functions = getFunctions(app);
    console.log('Functions initialized successfully');
  } catch (functionsError) {
    console.error('Failed to initialize Functions:', functionsError);
    // Create a mock functions object
    functions = {};
    console.log('Using mock Functions object');
  }
}

// Connect to local emulator when in development
if (__DEV__) {
  try {
    // Use localhost for emulators and ******** for Android emulators
    // This ensures the emulator can be reached from both emulators and physical devices
    const emulatorHost = Platform.OS === 'android' ? '********' : 'localhost';

    console.log(`Using emulator host: ${emulatorHost}`);

    // Connect to Auth emulator
    try {
      if (auth && typeof auth.emulator === 'function') {
        connectAuthEmulator(auth, `http://${emulatorHost}:9099`, { disableWarnings: true });
        console.log(`Connected to Firebase Auth emulator at ${emulatorHost}:9099`);
      } else {
        console.log('Auth emulator connection skipped - auth object does not support emulator');
      }
    } catch (authError) {
      console.error('Failed to connect to Auth emulator:', authError);
    }

    // Connect to Firestore emulator
    try {
      connectFirestoreEmulator(db, emulatorHost, 8080);
      console.log(`Connected to Firebase Firestore emulator at ${emulatorHost}:8080`);
    } catch (firestoreError) {
      console.error('Failed to connect to Firestore emulator:', firestoreError);
    }

    // Connect to Functions emulator
    try {
      connectFunctionsEmulator(functions, emulatorHost, 5001);
      console.log(`Connected to Firebase Functions emulator at ${emulatorHost}:5001`);
    } catch (functionsError) {
      console.error('Failed to connect to Functions emulator:', functionsError);
    }

  } catch (error) {
    console.error('Failed to connect to Firebase emulators:', error);
  }
}

// Create callable functions
let chatWithBot, analyzePeakFlow, recommendMedication;

// Initialize callable functions if available
if (httpsCallable && functions) {
  try {
    chatWithBot = httpsCallable(functions, 'chat');
    analyzePeakFlow = httpsCallable(functions, 'analyzePeakFlow');
    recommendMedication = httpsCallable(functions, 'recommendMedication');
    console.log('Callable functions initialized successfully');
  } catch (error) {
    console.error('Failed to initialize callable functions:', error);
    // Create mock functions
    const mockCallable = () => Promise.resolve({ data: { message: 'This is a mock response. Firebase Functions are not available.' } });
    chatWithBot = mockCallable;
    analyzePeakFlow = mockCallable;
    recommendMedication = mockCallable;
    console.log('Using mock callable functions');
  }
} else {
  // Create mock functions if httpsCallable is not available
  const mockCallable = () => Promise.resolve({ data: { message: 'This is a mock response. Firebase Functions are not available.' } });
  chatWithBot = mockCallable;
  analyzePeakFlow = mockCallable;
  recommendMedication = mockCallable;
  console.log('Using mock callable functions (httpsCallable not available)');
}

// Export initialized services
export {
  app,
  auth,
  db,
  functions,
  chatWithBot,
  analyzePeakFlow,
  recommendMedication
};
