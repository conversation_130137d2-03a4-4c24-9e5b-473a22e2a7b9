/**
 * Google Sign-In Initialization Utility
 * This file ensures Google Sign-In is properly initialized early in the app lifecycle
 */

import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { Platform } from 'react-native';
import { GOOGLE_WEB_CLIENT_ID } from '@env';

// Initialize Google Sign-In
export const initializeGoogleSignIn = () => {
  try {
    console.log('Initializing Google Sign-In with client ID:', GOOGLE_WEB_CLIENT_ID);
    
    // Configure Google Sign-In
    GoogleSignin.configure({
      webClientId: GOOGLE_WEB_CLIENT_ID,
      offlineAccess: true,
      forceCodeForRefreshToken: true,
      // iOS specific config
      iosClientId: Platform.OS === 'ios' ? GOOGLE_WEB_CLIENT_ID : undefined,
    });
    
    console.log('Google Sign-In initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing Google Sign-In:', error);
    return false;
  }
};

// Export default for convenience
export default initializeGoogleSignIn;
