/**
 * Emergency Support Screen
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Linking, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Button from '../components/Button';
import i18n from '../i18n/i18n';

const EmergencyScreen = ({ navigation }) => {
  // Mock data for nearby hospitals
  const nearbyHospitals = [
    {
      id: '1',
      name: 'Mulago National Referral Hospital',
      distance: '2.5 km',
      address: 'Upper Mulago Hill Road, Kampala',
      phone: '+256 414 541 884',
      isOpen: true,
    },
    {
      id: '2',
      name: 'Nsambya Hospital',
      distance: '4.8 km',
      address: 'Nsambya Road, Kampala',
      phone: '+256 414 267 012',
      isOpen: true,
    },
    {
      id: '3',
      name: 'Mengo Hospital',
      distance: '5.3 km',
      address: 'Albert Cook Road, Kampala',
      phone: '+256 414 270 222',
      isOpen: true,
    },
    {
      id: '4',
      name: 'Kibuli Hospital',
      distance: '6.1 km',
      address: 'Kibuli Road, Kampala',
      phone: '+256 414 268 222',
      isOpen: false,
    },
  ];

  // Mock emergency contacts
  const emergencyContacts = {
    emergency: '999',
    doctor: '+256 712 345 678',
    caregiver: '+256 774 567 890',
  };

  const handleEmergencyCall = () => {
    Alert.alert(
      'Emergency Call',
      'This will call emergency services (999). Continue?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Call',
          onPress: () => Linking.openURL(`tel:${emergencyContacts.emergency}`),
        },
      ],
      { cancelable: true }
    );
  };

  const handleCall = (phoneNumber) => {
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const renderHospitalItem = (hospital) => (
    <Card key={hospital.id} style={styles.hospitalCard}>
      <View style={styles.hospitalHeader}>
        <View>
          <Text style={styles.hospitalName}>{hospital.name}</Text>
          <Text style={styles.hospitalAddress}>{hospital.address}</Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            hospital.isOpen ? styles.openBadge : styles.closedBadge,
          ]}
        >
          <Text style={styles.statusText}>
            {hospital.isOpen ? 'Open' : 'Closed'}
          </Text>
        </View>
      </View>
      <View style={styles.hospitalDetails}>
        <View style={styles.detailItem}>
          <Ionicons name="location" size={16} color={COLORS.PRIMARY} />
          <Text style={styles.detailText}>{hospital.distance}</Text>
        </View>
        <View style={styles.detailItem}>
          <Ionicons name="call" size={16} color={COLORS.PRIMARY} />
          <Text style={styles.detailText}>{hospital.phone}</Text>
        </View>
      </View>
      <Button
        title={i18n.t('emergency.callNow')}
        onPress={() => handleCall(hospital.phone)}
        type={hospital.isOpen ? 'primary' : 'outline'}
        style={styles.callButton}
        disabled={!hospital.isOpen}
      />
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('emergency.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
        backgroundColor={COLORS.DANGER}
      />

      <ScrollView style={styles.content}>
        {/* Emergency Call Button */}
        <TouchableOpacity
          style={styles.emergencyButton}
          onPress={handleEmergencyCall}
          activeOpacity={0.8}
        >
          <View style={styles.emergencyButtonContent}>
            <Ionicons name="call" size={32} color={COLORS.WHITE} />
            <Text style={styles.emergencyButtonText}>
              {i18n.t('emergency.callEmergency')}
            </Text>
          </View>
          <Text style={styles.emergencyNumber}>{emergencyContacts.emergency}</Text>
        </TouchableOpacity>

        {/* First Aid Steps */}
        <Card title={i18n.t('emergency.firstAid')} style={styles.firstAidCard}>
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <Text style={styles.stepText}>{i18n.t('emergency.step1')}</Text>
          </View>
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <Text style={styles.stepText}>{i18n.t('emergency.step2')}</Text>
          </View>
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <Text style={styles.stepText}>{i18n.t('emergency.step3')}</Text>
          </View>
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>4</Text>
            </View>
            <Text style={styles.stepText}>{i18n.t('emergency.step4')}</Text>
          </View>
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>5</Text>
            </View>
            <Text style={styles.stepText}>{i18n.t('emergency.step5')}</Text>
          </View>
        </Card>

        {/* Contact Doctor */}
        <Card title={i18n.t('emergency.contactDoctor')} style={styles.contactCard}>
          <TouchableOpacity
            style={styles.contactItem}
            onPress={() => handleCall(emergencyContacts.doctor)}
          >
            <View style={styles.contactInfo}>
              <Text style={styles.contactName}>Dr. Sarah Nakato</Text>
              <Text style={styles.contactRole}>Primary Care Physician</Text>
            </View>
            <View style={styles.contactButton}>
              <Ionicons name="call" size={24} color={COLORS.PRIMARY} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.contactItem}
            onPress={() => handleCall(emergencyContacts.caregiver)}
          >
            <View style={styles.contactInfo}>
              <Text style={styles.contactName}>John Mukasa</Text>
              <Text style={styles.contactRole}>Caregiver</Text>
            </View>
            <View style={styles.contactButton}>
              <Ionicons name="call" size={24} color={COLORS.PRIMARY} />
            </View>
          </TouchableOpacity>
        </Card>

        {/* Nearby Hospitals */}
        <Text style={styles.sectionTitle}>{i18n.t('emergency.nearbyHospitals')}</Text>
        {nearbyHospitals.map(renderHospitalItem)}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
  },
  emergencyButton: {
    backgroundColor: COLORS.DANGER,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.large,
    alignItems: 'center',
    shadowColor: COLORS.DANGER,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  emergencyButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.small,
  },
  emergencyButtonText: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.WHITE,
    marginLeft: SPACING.medium,
  },
  emergencyNumber: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.WHITE,
  },
  firstAidCard: {
    marginBottom: SPACING.medium,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.DANGER,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  stepNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: COLORS.DANGER,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.small,
  },
  stepNumberText: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.WHITE,
  },
  stepText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    flex: 1,
  },
  contactCard: {
    marginBottom: SPACING.medium,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.small,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: 2,
  },
  contactRole: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  contactButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_BG,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sectionTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
  },
  hospitalCard: {
    marginBottom: SPACING.medium,
  },
  hospitalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.small,
  },
  hospitalName: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: 2,
  },
  hospitalAddress: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  statusBadge: {
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
  },
  openBadge: {
    backgroundColor: COLORS.SUCCESS + '30',
  },
  closedBadge: {
    backgroundColor: COLORS.DANGER + '30',
  },
  statusText: {
    fontSize: FONTS.SIZES.small,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  hospitalDetails: {
    flexDirection: 'row',
    marginBottom: SPACING.medium,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.large,
  },
  detailText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginLeft: SPACING.xs,
  },
  callButton: {
    alignSelf: 'flex-end',
  },
});

export default EmergencyScreen;
