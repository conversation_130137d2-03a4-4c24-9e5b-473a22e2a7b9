/**
 * Google Sign-In service for Smart AsthmaCare app
 */

import { Platform } from 'react-native';
import { GOOGLE_WEB_CLIENT_ID } from '@env';
import supabase from '../config/supabase';

// Try to import the Google Sign-In module, fall back to mock if it fails
let GoogleSignin, statusCodes;
try {
  const GoogleSignInModule = require('@react-native-google-signin/google-signin');
  GoogleSignin = GoogleSignInModule.GoogleSignin;
  statusCodes = GoogleSignInModule.statusCodes;
  console.log('Successfully imported Google Sign-In module');
} catch (error) {
  console.warn('Error importing Google Sign-In module, using fallback:', error.message);
  const FallbackGoogleSignIn = require('./GoogleSignInFallback').default;
  GoogleSignin = FallbackGoogleSignIn;
  statusCodes = FallbackGoogleSignIn.statusCodes;
}

// Initialize Google Sign-In
const configureGoogleSignIn = () => {
  try {
    console.log('Configuring Google Sign-In with client ID:', GOOGLE_WEB_CLIENT_ID);

    GoogleSignin.configure({
      webClientId: GOOGLE_WEB_CLIENT_ID,
      offlineAccess: true,
      forceCodeForRefreshToken: true,
      // iOS specific config
      iosClientId: Platform.OS === 'ios' ? GOOGLE_WEB_CLIENT_ID : undefined,
    });

    console.log('Google Sign-In configured successfully');
    return true;
  } catch (error) {
    console.error('Error configuring Google Sign-In:', error);
    return false;
  }
};

// Sign in with Google
const signInWithGoogle = async () => {
  try {
    // Configure Google Sign-In if not already configured
    configureGoogleSignIn();

    // Check if user is already signed in
    const isSignedIn = await GoogleSignin.isSignedIn();
    if (isSignedIn) {
      // Sign out first to ensure a fresh sign-in
      await GoogleSignin.signOut();
    }

    // Start the sign-in flow
    console.log('Starting Google Sign-In flow');
    await GoogleSignin.hasPlayServices();

    // Get user info and ID token
    const userInfo = await GoogleSignin.signIn();
    console.log('Google Sign-In successful, getting ID token');

    // Get ID token
    const { idToken } = await GoogleSignin.getTokens();
    if (!idToken) {
      throw new Error('No ID token returned from Google Sign-In');
    }

    console.log('Got Google ID token, signing in with Supabase');

    // Sign in with Supabase using Google token
    const { data, error } = await supabase.auth.signInWithIdToken({
      provider: 'google',
      token: idToken,
    });

    if (error) {
      console.error('Supabase Google sign-in error:', error);
      throw error;
    }

    console.log('Supabase Google sign-in successful');
    return { user: data.user, userInfo };
  } catch (error) {
    console.error('Google Sign-In error:', error);

    // Handle specific error cases
    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      throw new Error('Google Sign-In was cancelled');
    } else if (error.code === statusCodes.IN_PROGRESS) {
      throw new Error('Google Sign-In is already in progress');
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      throw new Error('Google Play Services not available or outdated');
    } else {
      throw error;
    }
  }
};

// Sign out from Google
const signOutFromGoogle = async () => {
  try {
    const isSignedIn = await GoogleSignin.isSignedIn();
    if (isSignedIn) {
      await GoogleSignin.signOut();
      console.log('Google Sign-Out successful');
    }
    return true;
  } catch (error) {
    console.error('Google Sign-Out error:', error);
    return false;
  }
};

// Get current user info
const getCurrentUserInfo = async () => {
  try {
    const userInfo = await GoogleSignin.getCurrentUser();
    return userInfo;
  } catch (error) {
    console.error('Error getting current user info:', error);
    return null;
  }
};

// Check if user is signed in
const isUserSignedIn = async () => {
  try {
    const isSignedIn = await GoogleSignin.isSignedIn();
    return isSignedIn;
  } catch (error) {
    console.error('Error checking if user is signed in:', error);
    return false;
  }
};

export default {
  configureGoogleSignIn,
  signInWithGoogle,
  signOutFromGoogle,
  getCurrentUserInfo,
  isUserSignedIn,
};
