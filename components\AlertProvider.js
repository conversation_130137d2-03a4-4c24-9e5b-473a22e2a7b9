/**
 * Alert Provider Component
 * Provides a context for showing styled alerts throughout the app
 */

import React, { createContext, useRef, useContext, useState } from 'react';
import AlertModal from './AlertModal';
import { setAlertRef } from '../utils/alert';

// Create a context for the alert
export const AlertContext = createContext();

export const AlertProvider = ({ children }) => {
  const alertRef = useRef();
  const [alertState, setAlertState] = useState({
    visible: false,
    title: '',
    message: '',
    buttons: [{ text: 'OK' }],
    type: 'info',
  });

  // Set the alert reference for the utility functions
  React.useEffect(() => {
    setAlertRef({ 
      current: {
        show: (config) => {
          setAlertState({
            ...config,
            visible: true,
          });
        }
      }
    });
  }, []);

  // Handle closing the alert
  const handleClose = () => {
    setAlertState(prev => ({
      ...prev,
      visible: false,
    }));
  };

  return (
    <AlertContext.Provider value={{ showAlert: alertRef.current?.show }}>
      {children}
      <AlertModal
        visible={alertState.visible}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons}
        type={alertState.type}
        onClose={handleClose}
      />
    </AlertContext.Provider>
  );
};

// Custom hook to use the alert context
export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

export default AlertProvider;
