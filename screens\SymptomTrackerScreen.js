/**
 * Symptom Tracker Screen
 */

import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Input from '../components/Input';
import Button from '../components/Button';
import SymptomCheckbox from '../components/SymptomCheckbox';
import CustomAlert from '../components/CustomAlert';
import i18n from '../i18n/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DatabaseService from '../services/DatabaseService';
import { supabase } from '../config/supabase-client';
import * as Location from 'expo-location';

// Initial chart data (will be replaced with stored data)
const initialChartData = [
  { date: 'Mon', peakFlow: 350, symptoms: 1, fullDate: new Date(Date.now() - 6 * 86400000).toISOString() },
  { date: 'Tue', peakFlow: 380, symptoms: 0, fullDate: new Date(Date.now() - 5 * 86400000).toISOString() },
  { date: 'Wed', peakFlow: 360, symptoms: 2, fullDate: new Date(Date.now() - 4 * 86400000).toISOString() },
  { date: 'Thu', peakFlow: 340, symptoms: 3, fullDate: new Date(Date.now() - 3 * 86400000).toISOString() },
  { date: 'Fri', peakFlow: 320, symptoms: 2, fullDate: new Date(Date.now() - 2 * 86400000).toISOString() },
  { date: 'Sat', peakFlow: 350, symptoms: 1, fullDate: new Date(Date.now() - 1 * 86400000).toISOString() },
  { date: 'Sun', peakFlow: 370, symptoms: 0, fullDate: new Date().toISOString() },
];

const SymptomTrackerScreen = ({ navigation }) => {
  // No longer using auth context directly here

  const [activeTab, setActiveTab] = useState('today');
  const [peakFlow, setPeakFlow] = useState('');
  const [notes, setNotes] = useState('');
  const [chartData, setChartData] = useState(initialChartData);
  const [historyData, setHistoryData] = useState([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedDay, setSelectedDay] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [userLocation, setUserLocation] = useState(null);

  // Custom alert state
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info',
    showCancel: false,
    confirmText: 'OK',
    cancelText: 'Cancel',
    onConfirm: null,
    confirmType: 'primary',
    autoClose: false,
    autoCloseTimeout: 3000
  });
  const [reportToDelete, setReportToDelete] = useState(null);

  const [symptoms, setSymptoms] = useState({
    coughing: { checked: false, severity: 'none' },
    wheezing: { checked: false, severity: 'none' },
    chestTightness: { checked: false, severity: 'none' },
    shortnessOfBreath: { checked: false, severity: 'none' },
    nighttimeAwakening: { checked: false, severity: 'none' },
    limitedActivity: { checked: false, severity: 'none' },
  });

  // Create database tables if they don't exist
  const createTablesIfNeeded = async () => {
    try {
      console.log('Checking if tables exist and creating them if needed...');

      // Check if health_reports table exists (new unified table)
      const { error: healthReportsError } = await supabase
        .from('health_reports')
        .select('id')
        .limit(1);

      if (healthReportsError && healthReportsError.code === '42P01') {
        console.log('health_reports table does not exist. Please run the migration script.');

        // Show an alert to the user
        setAlertConfig({
          title: 'Database Migration Required',
          message: 'The application needs to be updated with the latest database structure. Please contact the administrator.',
          type: 'warning',
          showCancel: false,
          confirmText: 'OK'
        });
        setAlertVisible(true);
      } else {
        console.log('health_reports table exists');
      }

      // No need to check old tables anymore, they've been replaced by health_reports
    } catch (error) {
      console.error('Error checking/creating tables:', error);
    }
  };

  // Request location permissions
  const requestLocationPermission = async () => {
    try {
      console.log('Requesting location permission...');

      // Default location (Kampala, Uganda) if location access is denied
      const defaultLocation = {
        latitude: 0.3476,
        longitude: 32.5825,
      };

      // Check if we already have permission
      const { status: existingStatus } = await Location.getForegroundPermissionsAsync();

      if (existingStatus === 'granted') {
        // Get current location
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });

        const userCoords = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };

        console.log('Got user location:', userCoords);
        setUserLocation(userCoords);

        return userCoords;
      }

      // Show styled alert before requesting permission
      return new Promise((resolve) => {
        setAlertConfig({
          title: i18n.t('permissions.locationTitle'),
          message: i18n.t('permissions.locationMessage'),
          type: 'info',
          showCancel: true,
          confirmText: i18n.t('permissions.allowAccess'),
          cancelText: i18n.t('permissions.notNow'),
          onConfirm: async () => {
            setAlertVisible(false);

            const { status } = await Location.requestForegroundPermissionsAsync();

            if (status !== 'granted') {
              // Show denied message
              setTimeout(() => {
                setAlertConfig({
                  title: i18n.t('permissions.locationDeniedTitle'),
                  message: i18n.t('permissions.locationDeniedMessage'),
                  type: 'warning',
                  showCancel: false,
                });
                setAlertVisible(true);
              }, 500);

              resolve(defaultLocation);
              return;
            }

            try {
              // Get current location
              const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Balanced,
              });

              const userCoords = {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              };

              console.log('Got user location:', userCoords);
              setUserLocation(userCoords);

              resolve(userCoords);
            } catch (error) {
              console.error('Error getting location:', error);
              resolve(defaultLocation);
            }
          },
          onClose: () => {
            resolve(defaultLocation);
          }
        });
        setAlertVisible(true);
      });
    } catch (error) {
      console.error('Error getting location permission:', error);

      // Return default location on error
      return {
        latitude: 0.3476,
        longitude: 32.5825,
      };
    }
  };

  // Load saved data when component mounts
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Request location permission
        await requestLocationPermission();

        // Check if tables exist and create them if they don't
        console.log('Initializing database...');
        await createTablesIfNeeded();

        // Load symptom data
        await loadSymptomData();
      } catch (error) {
        console.error('Error initializing data:', error);
      }
    };

    initializeData();
  }, []);

  // Load symptom data from AsyncStorage and Supabase
  const loadSymptomData = async () => {
    try {
      console.log('Loading symptom data...');
      setIsLoading(true);

      // First, try to load from AsyncStorage for quick display
      const savedChartData = await AsyncStorage.getItem('symptomChartData');
      if (savedChartData) {
        console.log('Found saved chart data in AsyncStorage');
        setChartData(JSON.parse(savedChartData));
      }

      const savedHistoryData = await AsyncStorage.getItem('symptomHistoryData');
      if (savedHistoryData) {
        console.log('Found saved history data in AsyncStorage');
        setHistoryData(JSON.parse(savedHistoryData));
      }

      // Then, load from Supabase database
      let combinedHistory = [];

      // Load peak flow readings from database (using health_reports table)
      console.log('Fetching peak flow readings from database...');
      const peakFlowResult = await DatabaseService.getPeakFlowReadings();
      console.log('Peak flow result:', peakFlowResult);

      if (peakFlowResult.success && peakFlowResult.data) {
        console.log(`Found ${peakFlowResult.data.length} peak flow readings in database`);

        // Convert database format to app format
        const peakFlowHistory = peakFlowResult.data.map(reading => {
          const readingDate = new Date(reading.report_date);
          return {
            id: reading.id,
            date: readingDate.toLocaleDateString(),
            time: readingDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            fullDate: reading.report_date,
            peakFlow: reading.peak_flow_reading,
            hasPeakFlowMeter: true,
            symptoms: Object.entries(reading.symptom_severity || {}).map(([name, severity]) => ({
              name,
              severity
            })),
            notes: reading.notes || '',
            weather: {
              condition: reading.weather_condition || 'Unknown',
              description: 'Weather data',
              temperature: reading.temperature || 0,
              humidity: reading.humidity || 0,
              pressure: 1013, // Default
              windSpeed: 0, // Default
              location: reading.location?.city || 'Unknown',
              pollen: 'Medium', // Default
              timestamp: reading.report_date
            }
          };
        });

        console.log('Converted peak flow readings to app format');

        // Log the IDs of the peak flow readings
        peakFlowHistory.forEach(reading => {
          console.log('Peak flow reading ID:', reading.id, 'Type:', typeof reading.id);
        });

        combinedHistory = [...combinedHistory, ...peakFlowHistory];
      } else {
        console.log('No peak flow readings found in database or error occurred');
      }

      // Load symptom reports from database (using health_reports table)
      console.log('Fetching symptom reports from database...');
      const symptomResult = await DatabaseService.getSymptomReports();
      console.log('Symptom reports result:', symptomResult);

      if (symptomResult.success && symptomResult.data) {
        console.log(`Found ${symptomResult.data.length} symptom reports in database`);

        // Convert database format to app format
        const symptomHistory = symptomResult.data.map(report => {
          const reportDate = new Date(report.report_date);
          return {
            id: report.id,
            date: reportDate.toLocaleDateString(),
            time: reportDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            fullDate: report.report_date,
            peakFlow: null, // No peak flow for symptom reports
            hasPeakFlowMeter: false,
            symptoms: Object.entries(report.symptom_severity || {}).map(([name, severity]) => ({
              name,
              severity
            })),
            notes: report.notes || '',
            weather: {
              condition: report.weather_condition || 'Unknown',
              description: 'Weather data',
              temperature: report.temperature || 0,
              humidity: report.humidity || 0,
              pressure: 1013, // Default
              windSpeed: 0, // Default
              location: report.location?.city || 'Unknown',
              pollen: 'Medium', // Default
              timestamp: report.report_date
            }
          };
        });

        console.log('Converted symptom reports to app format');

        // Log the IDs of the symptom reports
        symptomHistory.forEach(report => {
          console.log('Symptom report ID:', report.id, 'Type:', typeof report.id);
        });

        combinedHistory = [...combinedHistory, ...symptomHistory];
      } else {
        console.log('No symptom reports found in database or error occurred');
      }

      // If we got data from the database, use it
      if (combinedHistory.length > 0) {
        // Sort by date, newest first
        combinedHistory.sort((a, b) => new Date(b.fullDate) - new Date(a.fullDate));

        // Update state and AsyncStorage
        setHistoryData(combinedHistory);
        await AsyncStorage.setItem('symptomHistoryData', JSON.stringify(combinedHistory));

        // Update chart data based on the last 7 days
        const last7Days = combinedHistory.filter(item => {
          const itemDate = new Date(item.fullDate);
          const today = new Date();
          const dayDiff = Math.floor((today - itemDate) / (1000 * 60 * 60 * 24));
          return dayDiff < 7;
        });

        if (last7Days.length > 0) {
          // Create a new chart data array with the last 7 days
          const newChartData = [...initialChartData]; // Start with the initial data

          // Update with real data
          last7Days.forEach(item => {
            const itemDate = new Date(item.fullDate);
            const dayOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][itemDate.getDay()];
            const dayIndex = newChartData.findIndex(d => d.date === dayOfWeek);

            if (dayIndex >= 0) {
              // Count symptoms to determine severity level (0-3)
              const symptomLevel = Math.min(item.symptoms.length, 3);

              newChartData[dayIndex] = {
                date: dayOfWeek,
                peakFlow: item.peakFlow || 0,
                symptoms: symptomLevel,
                fullDate: item.fullDate,
                hasPeakFlowMeter: item.hasPeakFlowMeter
              };
            }
          });

          setChartData(newChartData);
          await AsyncStorage.setItem('symptomChartData', JSON.stringify(newChartData));
        }
      } else {
        // If no data in database, use empty arrays (no mock data)
        console.log('No history data found in database, using empty arrays');
        setHistoryData([]);
        await AsyncStorage.setItem('symptomHistoryData', JSON.stringify([]));

        // Set empty chart data
        const emptyChartData = initialChartData.map(item => ({
          ...item,
          peakFlow: 0,
          symptoms: 0
        }));
        setChartData(emptyChartData);
        await AsyncStorage.setItem('symptomChartData', JSON.stringify(emptyChartData));
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading symptom data:', error);
      setIsLoading(false);

      // If there's an error, try to use cached data from AsyncStorage
      try {
        const savedHistoryData = await AsyncStorage.getItem('symptomHistoryData');
        if (savedHistoryData) {
          setHistoryData(JSON.parse(savedHistoryData));
        } else {
          // Use empty arrays if nothing else works
          setHistoryData([]);
          await AsyncStorage.setItem('symptomHistoryData', JSON.stringify([]));

          // Set empty chart data
          const emptyChartData = initialChartData.map(item => ({
            ...item,
            peakFlow: 0,
            symptoms: 0
          }));
          setChartData(emptyChartData);
          await AsyncStorage.setItem('symptomChartData', JSON.stringify(emptyChartData));
        }
      } catch (asyncError) {
        console.error('Error loading from AsyncStorage fallback:', asyncError);
      }
    }
  };

  // No mock data generation needed anymore

  // Save symptom data to AsyncStorage and Supabase
  const saveSymptomData = async (newEntry) => {
    try {
      console.log('Saving new entry with ID:', newEntry.id);

      // Store the original entry ID (the custom one)
      const originalEntryId = newEntry.id;
      console.log('Original entry ID (custom):', originalEntryId);

      // First, create updated history but don't set it yet
      // We'll set it after saving to the database to ensure we have the correct UUID
      const updatedHistory = [newEntry, ...historyData];

      // Update chart data
      const today = new Date();
      const dayOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][today.getDay()];

      // Count active symptoms to determine severity level (0-3)
      const symptomCount = Object.values(newEntry.symptoms).filter(s => s.name).length;
      const symptomLevel = Math.min(symptomCount, 3);

      // Create new chart entry
      const newChartEntry = {
        date: dayOfWeek,
        peakFlow: newEntry.peakFlow ? parseInt(newEntry.peakFlow) : 0, // Use 0 for chart if no peak flow
        symptoms: symptomLevel,
        fullDate: today.toISOString(),
        hasPeakFlowMeter: newEntry.hasPeakFlowMeter
      };

      // Update chart data by replacing today's entry
      const updatedChartData = [...chartData];
      const todayIndex = updatedChartData.findIndex(item =>
        new Date(item.fullDate).toDateString() === today.toDateString()
      );

      if (todayIndex >= 0) {
        updatedChartData[todayIndex] = newChartEntry;
      } else {
        // If today's entry doesn't exist, add it and remove the oldest entry
        updatedChartData.push(newChartEntry);
        updatedChartData.shift(); // Remove oldest entry
      }

      setChartData(updatedChartData);
      await AsyncStorage.setItem('symptomChartData', JSON.stringify(updatedChartData));

      // Now, save to Supabase database
      try {
        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();
        console.log('Current user ID for saving report:', user?.id);

        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Store the original entry ID (the custom one)
        const originalEntryId = newEntry.id;
        console.log('Original entry ID (custom):', originalEntryId);

        // Create a single health report that includes both peak flow and weather data
        const reportData = {
          // Common fields
          report_date: new Date(newEntry.fullDate),
          notes: newEntry.notes,

          // Determine report type based on whether peak flow is present
          report_type: newEntry.peakFlow ? 'peak_flow' : 'symptom',

          // Symptom data
          symptoms: newEntry.symptoms.map(s => s.name),
          symptom_severity: newEntry.symptoms.reduce((acc, s) => {
            acc[s.name] = s.severity;
            return acc;
          }, {}),

          // Activity limitation and sleep disturbance (for symptom reports)
          activity_limitation: newEntry.symptoms.some(s => s.name === 'limitedActivity') ?
            newEntry.symptoms.find(s => s.name === 'limitedActivity').severity : 'none',
          sleep_disturbance: newEntry.symptoms.some(s => s.name === 'nighttimeAwakening') ?
            newEntry.symptoms.find(s => s.name === 'nighttimeAwakening').severity : 'none',

          // Peak flow specific data (if available)
          peak_flow_reading: newEntry.peakFlow || null,
          reading_time_of_day: new Date(newEntry.fullDate).getHours() < 12 ? 'morning' :
                              new Date(newEntry.fullDate).getHours() < 18 ? 'afternoon' : 'evening',

          // Weather data
          weather_condition: newEntry.weather.condition,
          weather_description: newEntry.weather.description,
          temperature: newEntry.weather.temperature,
          humidity: newEntry.weather.humidity,
          pressure: newEntry.weather.pressure,
          wind_speed: newEntry.weather.windSpeed,
          pollen_count: newEntry.weather.pollen,
          asthma_risk_level: newEntry.weather.pollen === 'High' ? 'high' :
                            newEntry.weather.pollen === 'Medium' ? 'moderate' : 'low',

          // Location data
          location: {
            city: newEntry.weather.location,
            country: 'Uganda', // Default for now
            coordinates: newEntry.weather.coordinates || {
              latitude: 0.3476,
              longitude: 32.5825
            }
          }
        };

        console.log('Saving unified health report to database using DatabaseService');

        // Use the DatabaseService to save the health report
        const result = await DatabaseService.addHealthReport(reportData);

        if (!result.success) {
          console.error('Error saving health report to database:', result.error);
          throw new Error(result.error);
        }

        console.log('Health report saved successfully:', result.data);

        // Update the entry ID with the database ID for future reference
        newEntry.id = result.data.id;
        console.log('Saved health report with ID:', newEntry.id);

        // Update the history data with the new ID
        const updatedHistoryWithId = updatedHistory.map(item =>
          item.id === originalEntryId ? {...item, id: result.data.id} : item
        );

        // Now set the history data with the correct UUID
        setHistoryData(updatedHistoryWithId);
        await AsyncStorage.setItem('symptomHistoryData', JSON.stringify(updatedHistoryWithId));

        // Also update the reportToDelete if it's the same entry
        if (reportToDelete && reportToDelete.id === originalEntryId) {
          setReportToDelete({...reportToDelete, id: result.data.id});
        }
      } catch (saveError) {
        console.error('Error saving to database:', saveError);
        // If database save fails, still update local state with the original entry
        setHistoryData(updatedHistory);
        await AsyncStorage.setItem('symptomHistoryData', JSON.stringify(updatedHistory));

        // Show a warning message that the data was saved locally but not to the database
        // But don't show it immediately - wait until the main function completes
        setTimeout(() => {
          setAlertConfig({
            title: 'Warning',
            message: 'There was an issue saving to the database. Please check your connection.',
            type: 'warning'
          });
          setAlertVisible(true);
        }, 1000);
      }

    } catch (error) {
      console.error('Error saving symptom data:', error);
      throw error; // Re-throw to handle in the calling function
    }
  };

  const handleSymptomToggle = (symptom) => {
    setSymptoms({
      ...symptoms,
      [symptom]: {
        ...symptoms[symptom],
        checked: !symptoms[symptom].checked,
        severity: symptoms[symptom].checked ? 'none' : 'mild',
      },
    });
  };

  const handleSeverityChange = (symptom, severity) => {
    setSymptoms({
      ...symptoms,
      [symptom]: {
        ...symptoms[symptom],
        severity,
      },
    });
  };

  const handleSubmit = async () => {
    // Check if user has entered peak flow or selected symptoms
    const hasSymptoms = Object.values(symptoms).some(symptom => symptom.checked);

    // If no peak flow and no symptoms, show error
    if ((!peakFlow || isNaN(parseInt(peakFlow))) && !hasSymptoms) {
      setAlertConfig({
        title: 'Input Required',
        message: 'Please either enter a peak flow reading or select at least one symptom',
        type: 'warning'
      });
      setAlertVisible(true);
      return;
    }

    // Create a new entry with current symptoms
    const activeSymptoms = [];
    Object.entries(symptoms).forEach(([name, data]) => {
      if (data.checked) {
        activeSymptoms.push({
          name,
          severity: data.severity
        });
      }
    });

    // Get current date and time
    const now = new Date();
    const dateStr = now.toLocaleDateString();
    const timeStr = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    // Note: The ID is already generated in the newEntry object as `entry-${Date.now()}`

    // Get default weather data
    const getDefaultWeatherData = () => {
      // Use consistent weather data for testing instead of random values
      // This ensures the weather conditions in history reports match what was shown when submitted

      // Default coordinates (Kampala, Uganda)
      const defaultCoordinates = {
        latitude: 0.3476,
        longitude: 32.5825
      };

      return {
        temperature: 25,
        humidity: 65,
        condition: 'Partly Cloudy',
        description: 'Partly cloudy skies',
        windSpeed: 8,
        pressure: 1013,
        location: 'Your Location', // Generic location name instead of hardcoded city
        icon: '02d',
        coordinates: userLocation || defaultCoordinates, // Use user location if available
        pollen: 'Medium',
        // Store the timestamp when this weather data was captured
        timestamp: new Date().toISOString()
      };
    };

    // Fetch real weather data
    const fetchWeatherData = async () => {
      try {
        // Get the user's current location
        let coordinates;

        if (userLocation) {
          // Use the stored location if available
          coordinates = userLocation;
          console.log('Using stored user location:', coordinates);
        } else {
          // Otherwise request location permission and get current location
          coordinates = await requestLocationPermission();
          console.log('Got new user location:', coordinates);
        }

        // OpenWeatherMap API key
        const apiKey = '********************************';

        console.log(`Fetching weather data for coordinates: ${coordinates.latitude}, ${coordinates.longitude}`);

        // Try to fetch current weather data with a timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

        try {
          const response = await fetch(
            `https://api.openweathermap.org/data/2.5/weather?lat=${coordinates.latitude}&lon=${coordinates.longitude}&units=metric&appid=${apiKey}`,
            { signal: controller.signal }
          );

          clearTimeout(timeoutId);

          if (!response.ok) {
            console.error('Weather API response not OK:', response.status, response.statusText);
            throw new Error('Weather data fetch failed with status: ' + response.status);
          }

          const data = await response.json();
          console.log('Weather data fetched successfully:', data);

          // Extract relevant weather information
          return {
            temperature: Math.round(data.main.temp),
            humidity: Math.round(data.main.humidity),
            condition: data.weather[0].main,
            description: data.weather[0].description,
            windSpeed: data.wind.speed,
            pressure: data.main.pressure,
            location: data.name || 'Your Location', // Use the location name from the API or fallback
            icon: data.weather[0].icon,
            coordinates: {
              latitude: coordinates.latitude,
              longitude: coordinates.longitude
            },
            // Pollen data would typically come from a different API
            // For now, we'll use a random value but with a fixed seed based on the date
            // This ensures consistent pollen data for the same day
            pollen: ['Low', 'Medium', 'High'][Math.floor((new Date().getDate() % 3))],
            // Store the timestamp when this weather data was captured
            timestamp: new Date().toISOString()
          };
        } catch (fetchError) {
          clearTimeout(timeoutId);
          console.error('Fetch error:', fetchError);
          throw fetchError;
        }
      } catch (error) {
        console.error('Error fetching weather data:', error);
        console.log('Falling back to default weather data');
        return getDefaultWeatherData();
      }
    };

    // Show loading indicator
    setIsLoading(true);

    try {
      // Show loading indicator with weather message
      setAlertConfig({
        title: 'Fetching Weather',
        message: 'Getting current weather conditions...',
        type: 'info',
        showCancel: false,
        autoClose: true,
        autoCloseTimeout: 2000
      });
      setAlertVisible(true);

      // Get weather data
      let weatherData;
      try {
        weatherData = await fetchWeatherData();
        console.log('Weather data fetched:', weatherData);
      } catch (weatherError) {
        console.error('Weather fetch failed, using default:', weatherError);
        weatherData = getDefaultWeatherData();
      }

      // Create new entry with time and real weather data
      const newEntry = {
        id: `entry-${Date.now()}`,
        date: dateStr,
        time: timeStr,
        fullDate: now.toISOString(),
        peakFlow: peakFlow ? parseInt(peakFlow) : null,
        hasPeakFlowMeter: !!peakFlow,
        symptoms: activeSymptoms,
        notes: notes,
        weather: weatherData
      };

      console.log('Created new entry with ID:', newEntry.id);

      // Save data
      await saveSymptomData(newEntry);

      // Hide loading indicator
      setIsLoading(false);

      // Show success message with custom alert
      setAlertConfig({
        title: i18n.t('success'),
        message: i18n.t('symptoms.recordedSuccessfully'),
        type: 'success'
      });
      setAlertVisible(true);

      // Reset form
      setPeakFlow('');
      setNotes('');
      setSymptoms({
        coughing: { checked: false, severity: 'none' },
        wheezing: { checked: false, severity: 'none' },
        chestTightness: { checked: false, severity: 'none' },
        shortnessOfBreath: { checked: false, severity: 'none' },
        nighttimeAwakening: { checked: false, severity: 'none' },
        limitedActivity: { checked: false, severity: 'none' },
      });
    } catch (error) {
      // Hide loading indicator
      setIsLoading(false);

      // Show error message with custom alert
      setAlertConfig({
        title: 'Error',
        message: 'There was a problem saving your data. Please try again.',
        type: 'danger'
      });
      setAlertVisible(true);
      console.error('Error in handleSubmit:', error);
    }
  };

  const renderTodayTab = () => (
    <ScrollView style={styles.tabContent}>
      {isLoading && (
        <View style={styles.loadingContainer}>
          <View style={styles.loadingIndicator}>
            <Ionicons name="cloud-download" size={24} color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>Fetching weather data...</Text>
          </View>
        </View>
      )}

      {/* Peak Flow Section */}
      <Card style={styles.section}>
        <View style={styles.cardTitleContainer}>
          <Text style={styles.cardTitle}>{i18n.t('symptoms.peakFlow')}</Text>
          <TouchableOpacity
            onPress={() => {
              setAlertConfig({
                title: "Peak Flow Meter",
                message: "A peak flow meter is a device that measures how well air moves out of your lungs. It helps monitor your asthma control. If you don't have a peak flow meter, you can still track your symptoms below.",
                type: "info"
              });
              setAlertVisible(true);
            }}
            style={styles.infoButton}
          >
            <Ionicons name="information-circle" size={22} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        </View>
        <Input
          label={i18n.t('symptoms.enterValue')}
          placeholder="e.g., 350 L/min"
          value={peakFlow}
          onChangeText={setPeakFlow}
          keyboardType="numeric"
        />
        <Text style={styles.infoText}>
          Don't have a peak flow meter? No problem! You can still track your symptoms below.
        </Text>
      </Card>

      {/* Symptoms Section */}
      <Card title={i18n.t('symptoms.title')} style={styles.section}>
        <SymptomCheckbox
          label={i18n.t('symptoms.coughing')}
          checked={symptoms.coughing.checked}
          onToggle={() => handleSymptomToggle('coughing')}
          severity={symptoms.coughing.severity}
        />

        {symptoms.coughing.checked && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View style={styles.severityOptions}>
              {['mild', 'moderate', 'severe'].map((severity) => (
                <TouchableOpacity
                  key={severity}
                  style={[
                    styles.severityOption,
                    symptoms.coughing.severity === severity && styles.selectedSeverity,
                    { backgroundColor: getSeverityColor(severity) },
                  ]}
                  onPress={() => handleSeverityChange('coughing', severity)}
                >
                  <Text style={styles.severityText}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        <SymptomCheckbox
          label={i18n.t('symptoms.wheezing')}
          checked={symptoms.wheezing.checked}
          onToggle={() => handleSymptomToggle('wheezing')}
          severity={symptoms.wheezing.severity}
        />

        {symptoms.wheezing.checked && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View style={styles.severityOptions}>
              {['mild', 'moderate', 'severe'].map((severity) => (
                <TouchableOpacity
                  key={severity}
                  style={[
                    styles.severityOption,
                    symptoms.wheezing.severity === severity && styles.selectedSeverity,
                    { backgroundColor: getSeverityColor(severity) },
                  ]}
                  onPress={() => handleSeverityChange('wheezing', severity)}
                >
                  <Text style={styles.severityText}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        <SymptomCheckbox
          label={i18n.t('symptoms.chestTightness')}
          checked={symptoms.chestTightness.checked}
          onToggle={() => handleSymptomToggle('chestTightness')}
          severity={symptoms.chestTightness.severity}
        />

        {symptoms.chestTightness.checked && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View style={styles.severityOptions}>
              {['mild', 'moderate', 'severe'].map((severity) => (
                <TouchableOpacity
                  key={severity}
                  style={[
                    styles.severityOption,
                    symptoms.chestTightness.severity === severity && styles.selectedSeverity,
                    { backgroundColor: getSeverityColor(severity) },
                  ]}
                  onPress={() => handleSeverityChange('chestTightness', severity)}
                >
                  <Text style={styles.severityText}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        <SymptomCheckbox
          label={i18n.t('symptoms.shortnessOfBreath')}
          checked={symptoms.shortnessOfBreath.checked}
          onToggle={() => handleSymptomToggle('shortnessOfBreath')}
          severity={symptoms.shortnessOfBreath.severity}
        />

        {symptoms.shortnessOfBreath.checked && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View style={styles.severityOptions}>
              {['mild', 'moderate', 'severe'].map((severity) => (
                <TouchableOpacity
                  key={severity}
                  style={[
                    styles.severityOption,
                    symptoms.shortnessOfBreath.severity === severity && styles.selectedSeverity,
                    { backgroundColor: getSeverityColor(severity) },
                  ]}
                  onPress={() => handleSeverityChange('shortnessOfBreath', severity)}
                >
                  <Text style={styles.severityText}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        <SymptomCheckbox
          label={i18n.t('symptoms.nighttimeAwakening')}
          checked={symptoms.nighttimeAwakening.checked}
          onToggle={() => handleSymptomToggle('nighttimeAwakening')}
          severity={symptoms.nighttimeAwakening.severity}
        />

        {symptoms.nighttimeAwakening.checked && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View style={styles.severityOptions}>
              {['mild', 'moderate', 'severe'].map((severity) => (
                <TouchableOpacity
                  key={severity}
                  style={[
                    styles.severityOption,
                    symptoms.nighttimeAwakening.severity === severity && styles.selectedSeverity,
                    { backgroundColor: getSeverityColor(severity) },
                  ]}
                  onPress={() => handleSeverityChange('nighttimeAwakening', severity)}
                >
                  <Text style={styles.severityText}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        <SymptomCheckbox
          label={i18n.t('symptoms.limitedActivity')}
          checked={symptoms.limitedActivity.checked}
          onToggle={() => handleSymptomToggle('limitedActivity')}
          severity={symptoms.limitedActivity.severity}
        />

        {symptoms.limitedActivity.checked && (
          <View style={styles.severityContainer}>
            <Text style={styles.severityLabel}>Severity:</Text>
            <View style={styles.severityOptions}>
              {['mild', 'moderate', 'severe'].map((severity) => (
                <TouchableOpacity
                  key={severity}
                  style={[
                    styles.severityOption,
                    symptoms.limitedActivity.severity === severity && styles.selectedSeverity,
                    { backgroundColor: getSeverityColor(severity) },
                  ]}
                  onPress={() => handleSeverityChange('limitedActivity', severity)}
                >
                  <Text style={styles.severityText}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </Card>

      {/* Notes Section */}
      <Card title={i18n.t('symptoms.notes')} style={styles.section}>
        <Input
          placeholder="Any additional notes about your symptoms today..."
          value={notes}
          onChangeText={setNotes}
          multiline
          numberOfLines={4}
        />
      </Card>

      <Button
        title={i18n.t('save')}
        onPress={handleSubmit}
        style={styles.submitButton}
      />
    </ScrollView>
  );

  // Handle selecting a history item to view details
  const handleHistoryItemPress = (item) => {
    setSelectedDay(item);
    setDetailModalVisible(true);
  };

  // Handle deleting a report - completely new implementation
  const handleDeleteReport = async () => {
    if (!reportToDelete) return;

    try {
      // Show loading indicator
      setIsLoading(true);

      console.log('NEW DELETE FUNCTION - Attempting to delete report:', JSON.stringify(reportToDelete, null, 2));

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      console.log('Current user ID:', user.id);
      console.log('Report ID:', reportToDelete.id);

      // Check if the ID is a custom entry ID (starts with "entry-")
      const isCustomEntryId = (id) => {
        return typeof id === 'string' && id.startsWith('entry-');
      };

      // Log the ID format
      console.log('Report ID format check:', {
        id: reportToDelete.id,
        isString: typeof reportToDelete.id === 'string',
        length: typeof reportToDelete.id === 'string' ? reportToDelete.id.length : 'N/A',
        isCustomEntryId: isCustomEntryId(reportToDelete.id)
      });

      let deletedFromDatabase = false;

      // If it's a custom entry ID, we need to handle it differently
      if (isCustomEntryId(reportToDelete.id)) {
        console.log('This is a custom entry ID, trying to delete the most recent report');

        try {
          // For custom IDs, we'll delete the most recent report for this user
          // This is a workaround since we can't match by the custom ID

          // Try to find the most recent peak flow reading in health_reports table
          console.log('Attempting to delete most recent peak flow reading');
          const { data: peakFlowData, error: peakFlowError } = await supabase
            .from('health_reports')
            .select('id')
            .eq('user_id', user.id)
            .eq('report_type', 'peak_flow')
            .order('created_at', { ascending: false })
            .limit(1);

          if (peakFlowError) {
            console.log('Error getting most recent peak flow reading:', peakFlowError);
          } else if (peakFlowData && peakFlowData.length > 0) {
            console.log('Found most recent peak flow reading with ID:', peakFlowData[0].id);

            // Delete this reading using DatabaseService
            const result = await DatabaseService.deleteHealthReport(peakFlowData[0].id);

            if (!result.success) {
              console.log('Error deleting most recent peak flow reading:', result.error);
            } else {
              console.log('Successfully deleted most recent peak flow reading');
              deletedFromDatabase = true;
            }
          } else {
            console.log('No peak flow readings found');

            // Try symptom reports instead using the new health_reports table
            const { data: healthReports, error: healthError } = await supabase
              .from('health_reports')
              .select('id')
              .eq('user_id', user.id)
              .eq('report_type', 'symptom')
              .order('created_at', { ascending: false })
              .limit(1);

            if (healthError) {
              console.log('Error getting most recent symptom report:', healthError);
            } else if (healthReports && healthReports.length > 0) {
              console.log('Found most recent symptom report with ID:', healthReports[0].id);

              // Delete this report using DatabaseService
              const result = await DatabaseService.deleteHealthReport(healthReports[0].id);

              if (!result.success) {
                console.log('Error deleting most recent symptom report:', result.error);
              } else {
                console.log('Successfully deleted most recent symptom report');
                deletedFromDatabase = true;
              }
            } else {
              console.log('No symptom reports found');
            }
          }
        } catch (error) {
          console.log('Exception when deleting most recent report:', error);
        }
      } else {
        // For real UUIDs, use the normal delete process
        // Delete from health_reports table using DatabaseService
        try {
          console.log('Attempting to delete from health_reports table');
          const result = await DatabaseService.deleteHealthReport(reportToDelete.id);

          if (!result.success) {
            console.log('Error deleting from health_reports:', result.error);
          } else {
            console.log('Successfully deleted from health_reports table');
            deletedFromDatabase = true;
          }
        } catch (error) {
          console.log('Exception when deleting from health_reports:', error);
        }
      }

      // If we couldn't delete from the database, show an error
      if (!deletedFromDatabase) {
        console.error('Failed to delete report from database');
        setIsLoading(false);
        setAlertConfig({
          title: 'Error',
          message: 'Failed to delete report from database. Please try again.',
          type: 'danger'
        });
        setAlertVisible(true);
        return;
      }

      // Now that we've successfully deleted from the database, update the local state
      console.log('Updating local state to remove the report');

      // Remove from historyData
      const updatedHistory = historyData.filter(item => item.id !== reportToDelete.id);
      setHistoryData(updatedHistory);

      // Save to AsyncStorage
      await AsyncStorage.setItem('symptomHistoryData', JSON.stringify(updatedHistory));
      console.log('Updated history saved to AsyncStorage');

      // Update chart data if the deleted report was from the current week
      const today = new Date();
      const reportDate = new Date(reportToDelete.fullDate);
      const dayDiff = Math.floor((today - reportDate) / (1000 * 60 * 60 * 24));

      if (dayDiff < 7) {
        // Find the day of week for the deleted report
        const dayOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][reportDate.getDay()];

        // Update chart data by setting the deleted day to default values
        const updatedChartData = chartData.map(item => {
          if (item.date === dayOfWeek) {
            return {
              ...item,
              peakFlow: 0,
              symptoms: 0
            };
          }
          return item;
        });

        setChartData(updatedChartData);
        await AsyncStorage.setItem('symptomChartData', JSON.stringify(updatedChartData));
      }

      // Reset report to delete
      setReportToDelete(null);

      // If we were viewing the detail of the deleted report, close that modal too
      if (selectedDay && selectedDay.id === reportToDelete.id) {
        setDetailModalVisible(false);
        setSelectedDay(null);
      }

      // Reload data from database to ensure we have the latest state
      console.log('Reloading data after delete...');
      await loadSymptomData();

      // Hide loading indicator
      setIsLoading(false);

      // Show success message
      setAlertConfig({
        title: i18n.t('success'),
        message: i18n.t('symptoms.deleteSuccess') || 'Report deleted successfully',
        type: 'success'
      });
      setAlertVisible(true);
    } catch (error) {
      console.error('Error deleting report:', error);

      // Hide loading indicator
      setIsLoading(false);

      setAlertConfig({
        title: 'Error',
        message: error.message || 'There was a problem deleting the report. Please try again.',
        type: 'danger'
      });
      setAlertVisible(true);
    }
  };

  // Show delete confirmation modal
  const confirmDeleteReport = (item) => {
    console.log('confirmDeleteReport called with item:', item);

    setReportToDelete(item);

    // Use custom alert for confirmation
    setAlertConfig({
      title: i18n.t('symptoms.confirmDelete'),
      message: i18n.t('symptoms.deleteConfirmMessage'),
      type: 'danger',
      showCancel: true,
      confirmText: i18n.t('delete'),
      cancelText: i18n.t('cancel'),
      confirmType: 'danger',
      onConfirm: () => {
        // Close the alert first
        setAlertVisible(false);
        // Then handle the delete
        console.log('Delete confirmed, calling handleDeleteReport');
        setTimeout(handleDeleteReport, 300);
      }
    });
    setAlertVisible(true);
  };

  // Handle deleting all reports - completely new implementation
  const handleDeleteAllReports = async () => {
    try {
      // Show loading indicator
      setIsLoading(true);

      console.log('NEW DELETE ALL FUNCTION - Attempting to delete all reports');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      console.log('Current user ID:', user.id);

      // Check if there are any real reports in the database
      let hasRealReports = false;
      let deletedFromDatabase = false;

      // Check peak flow readings in health_reports table
      try {
        const { data: healthReports, error: healthError } = await supabase
          .from('health_reports')
          .select('id')
          .eq('user_id', user.id)
          .eq('report_type', 'peak_flow')
          .limit(1);

        if (!healthError && healthReports && healthReports.length > 0) {
          hasRealReports = true;
          console.log('Found peak flow readings in the database');
        }
      } catch (error) {
        console.error('Error checking peak flow readings:', error);
      }

      // Check symptom reports in health_reports table
      try {
        const { data: healthReports, error: healthError } = await supabase
          .from('health_reports')
          .select('id')
          .eq('user_id', user.id)
          .eq('report_type', 'symptom')
          .limit(1);

        if (!healthError && healthReports && healthReports.length > 0) {
          hasRealReports = true;
          console.log('Found symptom reports in the database');
        }
      } catch (error) {
        console.error('Error checking symptom reports:', error);
      }

      if (hasRealReports) {
        // All health reports (including peak flow readings) will be deleted by DatabaseService.deleteAllHealthReports() below

        // Then, try to delete all health reports (which includes symptom reports)
        try {
          console.log('Attempting to delete all health reports');

          // Use the DatabaseService to delete all health reports
          const result = await DatabaseService.deleteAllHealthReports();

          if (!result.success) {
            console.error('Error deleting all health reports:', result.error);
          } else {
            console.log('Successfully deleted all health reports');
            deletedFromDatabase = true;
          }
        } catch (error) {
          console.error('Exception when deleting all health reports:', error);
        }

        // If we couldn't delete from the database, show an error
        if (!deletedFromDatabase) {
          console.error('Failed to delete reports from database');
          setIsLoading(false);
          setAlertConfig({
            title: 'Error',
            message: 'Failed to delete reports from database. Please try again.',
            type: 'danger'
          });
          setAlertVisible(true);
          return;
        }
      } else {
        console.log('No real reports found in the database, only clearing local state');
        // Even if there are no real reports, we'll consider this a success
        deletedFromDatabase = true;
      }

      // Now that we've successfully deleted from the database, update the local state
      // Clear local state
      setHistoryData([]);
      console.log('History data cleared');

      // Save empty history to AsyncStorage
      await AsyncStorage.setItem('symptomHistoryData', JSON.stringify([]));
      console.log('AsyncStorage history cleared');

      // Reset chart data to default values
      const resetChartData = chartData.map(item => ({
        ...item,
        peakFlow: 0,
        symptoms: 0
      }));

      setChartData(resetChartData);
      await AsyncStorage.setItem('symptomChartData', JSON.stringify(resetChartData));
      console.log('Chart data reset');

      // Reload data from database to ensure we have the latest state
      console.log('Reloading data after delete all...');
      await loadSymptomData();

      // Hide loading indicator
      setIsLoading(false);

      // Show success message
      setAlertConfig({
        title: i18n.t('success'),
        message: 'All reports deleted successfully',
        type: 'success'
      });
      setAlertVisible(true);
    } catch (error) {
      console.error('Error deleting all reports:', error);

      // Hide loading indicator
      setIsLoading(false);

      setAlertConfig({
        title: 'Error',
        message: error.message || 'There was a problem deleting all reports. Please try again.',
        type: 'danger'
      });
      setAlertVisible(true);
    }
  };

  // Show delete all confirmation modal
  const confirmDeleteAllReports = () => {
    // Use custom alert for confirmation
    setAlertConfig({
      title: 'Delete All Reports',
      message: 'Are you sure you want to delete ALL your reports? This action cannot be undone.',
      type: 'danger',
      showCancel: true,
      confirmText: 'Delete All',
      cancelText: i18n.t('cancel'),
      confirmType: 'danger',
      onConfirm: () => {
        // Close the alert first
        setAlertVisible(false);
        // Then handle the delete all
        setTimeout(handleDeleteAllReports, 300);
      }
    });
    setAlertVisible(true);
  };

  const renderHistoryTab = () => (
    <ScrollView style={styles.tabContent}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <View style={styles.loadingIndicator}>
            <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>Loading health reports...</Text>
            <Text style={styles.loadingSubText}>Please wait while we fetch your data</Text>
          </View>
        </View>
      ) : (
        <Card title={i18n.t('symptoms.history')} style={styles.section}>
          {/* Delete All button */}
          {historyData.length > 0 && (
            <TouchableOpacity
              style={styles.deleteAllButton}
              onPress={confirmDeleteAllReports}
            >
              <Ionicons name="trash-outline" size={18} color={COLORS.WHITE} />
              <Text style={styles.deleteAllButtonText}>Delete All Reports</Text>
            </TouchableOpacity>
          )}

          {historyData.length === 0 ? (
            <Text style={styles.emptyMessage}>No history data available yet.</Text>
          ) : (
            historyData.map((item, index) => (
              <View key={item.id || index} style={styles.historyItemContainer}>
                <TouchableOpacity
                  style={styles.historyItem}
                  onPress={() => handleHistoryItemPress(item)}
                >
                  <View style={styles.historyHeader}>
                    <View style={styles.dateTimeContainer}>
                      <Text style={styles.historyDate}>{item.date}</Text>
                      {item.time && <Text style={styles.historyTime}>{item.time}</Text>}
                    </View>
                    <View
                      style={[
                        styles.statusIndicator,
                        {
                          backgroundColor:
                            item.symptoms.length === 0
                              ? COLORS.SUCCESS
                              : item.symptoms.some(s => s.severity === 'severe')
                                ? COLORS.DANGER
                                : COLORS.WARNING,
                        },
                      ]}
                    />
                  </View>
                  <View style={styles.historyDetails}>
                    <View style={styles.historyDetail}>
                      <Text style={styles.detailLabel}>Peak Flow:</Text>
                      <Text style={styles.detailValue}>
                        {item.peakFlow ? `${item.peakFlow} L/min` : 'Not measured'}
                      </Text>
                    </View>
                    <View style={styles.historyDetail}>
                      <Text style={styles.detailLabel}>Symptoms:</Text>
                      <Text style={styles.detailValue}>
                        {item.symptoms.length === 0
                          ? 'None'
                          : item.symptoms.map(s =>
                              `${s.severity} ${s.name}`
                            ).join(', ')}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => confirmDeleteReport(item)}
                  disabled={isLoading}
                >
                  <Ionicons name="trash-outline" size={22} color={COLORS.DANGER} />
                </TouchableOpacity>
              </View>
            ))
          )}
        </Card>
      )}
    </ScrollView>
  );

  const renderTrendsTab = () => (
    <ScrollView style={styles.tabContent}>
      <Card title={i18n.t('symptoms.trends')} style={styles.section}>
        <Text style={styles.chartTitle}>Weekly Peak Flow and Symptom Tracking</Text>

        {/* Chart description */}
        <Text style={styles.chartDescription}>
          This chart shows your peak flow readings (blue bars) and symptom severity (colored dots) for the past week.
          Higher peak flow values indicate better lung function.
        </Text>

        {/* Improved chart */}
        <View style={styles.chartContainer}>
          {/* Y-axis with labels and grid lines */}
          <View style={styles.chartYAxis}>
            <Text style={styles.chartLabel}>400 L/min</Text>
            <View style={styles.gridLine} />
            <Text style={styles.chartLabel}>350 L/min</Text>
            <View style={styles.gridLine} />
            <Text style={styles.chartLabel}>300 L/min</Text>
            <View style={styles.gridLine} />
            <Text style={styles.chartLabel}>250 L/min</Text>
            <View style={styles.gridLine} />
          </View>

          {/* Chart content */}
          <View style={styles.chart}>
            {chartData.map((item, index) => (
              <View key={index} style={styles.chartBar}>
                {/* Peak flow bar */}
                <View style={styles.barContainer}>
                  <View
                    style={[
                      styles.peakFlowBar,
                      {
                        height: `${(item.peakFlow - 250) / 150 * 100}%`,
                        backgroundColor: COLORS.PRIMARY,
                      },
                    ]}
                  />

                  {/* Symptom indicator dot */}
                  <View style={styles.symptomIndicatorContainer}>
                    <View
                      style={[
                        styles.symptomIndicator,
                        {
                          backgroundColor:
                            item.symptoms === 0
                              ? COLORS.SUCCESS
                              : item.symptoms === 1
                              ? COLORS.WARNING
                              : COLORS.DANGER,
                          opacity: item.symptoms > 0 ? 1 : 0.3,
                        },
                      ]}
                    >
                      {item.symptoms > 0 && (
                        <Text style={styles.symptomIndicatorText}>{item.symptoms}</Text>
                      )}
                    </View>
                  </View>
                </View>

                {/* X-axis label */}
                <Text style={styles.chartXLabel}>{item.date}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Enhanced legend */}
        <View style={styles.chartLegendContainer}>
          <Text style={styles.legendTitle}>Legend:</Text>
          <View style={styles.chartLegend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: COLORS.PRIMARY }]} />
              <Text style={styles.legendText}>Peak Flow (L/min)</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: COLORS.SUCCESS, borderRadius: 10 }]} />
              <Text style={styles.legendText}>No Symptoms</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: COLORS.WARNING, borderRadius: 10 }]} />
              <Text style={styles.legendText}>Mild Symptoms</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: COLORS.DANGER, borderRadius: 10 }]} />
              <Text style={styles.legendText}>Severe Symptoms</Text>
            </View>
          </View>
        </View>

        {/* Interpretation guide */}
        <View style={styles.interpretationContainer}>
          <Text style={styles.interpretationTitle}>How to interpret:</Text>
          <Text style={styles.interpretationText}>
            • Higher blue bars indicate better lung function{'\n'}
            • Green dots mean no symptoms were reported{'\n'}
            • Yellow dots indicate mild symptoms{'\n'}
            • Red dots show days with more severe symptoms{'\n'}
            • The number in the dot shows symptom severity (1-3)
          </Text>
        </View>
      </Card>
    </ScrollView>
  );

  // Helper function to get color based on severity
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'mild':
        return COLORS.SUCCESS;
      case 'moderate':
        return COLORS.WARNING;
      case 'severe':
        return COLORS.DANGER;
      default:
        return COLORS.LIGHT_BG;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('symptoms.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightIcon={<Ionicons name="person-circle" size={24} color={COLORS.WHITE} />}
        onRightPress={() => navigation.navigate('Profile')}
      />

      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'today' && styles.activeTab]}
          onPress={() => setActiveTab('today')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'today' && styles.activeTabText,
            ]}
          >
            {i18n.t('symptoms.today')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => {
            setActiveTab('history');
            // Show loading indicator and reload data when switching to history tab
            setIsLoading(true);
            loadSymptomData();
          }}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'history' && styles.activeTabText,
            ]}
          >
            {i18n.t('symptoms.history')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'trends' && styles.activeTab]}
          onPress={() => setActiveTab('trends')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'trends' && styles.activeTabText,
            ]}
          >
            {i18n.t('symptoms.trends')}
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'today' && renderTodayTab()}
      {activeTab === 'history' && renderHistoryTab()}
      {activeTab === 'trends' && renderTrendsTab()}





      {/* Detail Modal */}
      <Modal
        visible={detailModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setDetailModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.detailModalContainer}>
            <View style={styles.detailModalHeader}>
              <Text style={styles.detailModalTitle}>Daily Report</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setDetailModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
            </View>

            {selectedDay && (
              <ScrollView style={styles.detailModalContent}>
                <View style={styles.detailSection}>
                  <Text style={styles.detailSectionTitle}>Date & Time</Text>
                  <Text style={styles.detailSectionContent}>
                    {selectedDay.date} {selectedDay.time ? `at ${selectedDay.time}` : ''}
                  </Text>
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.detailSectionTitle}>Peak Flow</Text>
                  {selectedDay.peakFlow ? (
                    <>
                      <View style={styles.peakFlowIndicator}>
                        <View
                          style={[
                            styles.peakFlowLevel,
                            {
                              width: `${Math.min(100, (selectedDay.peakFlow / 400) * 100)}%`,
                              backgroundColor:
                                selectedDay.peakFlow > 350 ? COLORS.SUCCESS :
                                selectedDay.peakFlow > 300 ? COLORS.WARNING :
                                COLORS.DANGER
                            }
                          ]}
                        />
                      </View>
                      <Text style={styles.detailSectionContent}>
                        {selectedDay.peakFlow} L/min
                        {selectedDay.peakFlow > 350 ? ' (Good)' :
                         selectedDay.peakFlow > 300 ? ' (Caution)' :
                         ' (Danger)'}
                      </Text>
                    </>
                  ) : (
                    <Text style={styles.detailSectionContent}>
                      Not measured (Symptom-based tracking)
                    </Text>
                  )}
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.detailSectionTitle}>Symptoms</Text>
                  {selectedDay.symptoms.length === 0 ? (
                    <Text style={styles.detailSectionContent}>No symptoms reported</Text>
                  ) : (
                    selectedDay.symptoms.map((symptom, index) => (
                      <View key={index} style={styles.symptomItem}>
                        <View
                          style={[
                            styles.severityDot,
                            {
                              backgroundColor:
                                symptom.severity === 'mild' ? COLORS.SUCCESS :
                                symptom.severity === 'moderate' ? COLORS.WARNING :
                                COLORS.DANGER
                            }
                          ]}
                        />
                        <Text style={styles.symptomText}>
                          {symptom.name.charAt(0).toUpperCase() + symptom.name.slice(1)} - {symptom.severity}
                        </Text>
                      </View>
                    ))
                  )}
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.detailSectionTitle}>Weather Conditions</Text>

                  <View style={styles.weatherHeader}>
                    <View style={styles.weatherIconContainer}>
                      <Ionicons
                        name={
                          selectedDay.weather.condition === 'Clear' ? 'sunny' :
                          selectedDay.weather.condition === 'Clouds' ? 'cloud' :
                          selectedDay.weather.condition === 'Rain' ? 'rainy' :
                          selectedDay.weather.condition === 'Thunderstorm' ? 'thunderstorm' :
                          selectedDay.weather.condition === 'Mist' || selectedDay.weather.condition === 'Fog' ? 'water' :
                          selectedDay.weather.condition === 'Snow' ? 'snow' :
                          selectedDay.weather.condition === 'Partly Cloudy' ? 'partly-sunny' :
                          'sunny'
                        }
                        size={40}
                        color={COLORS.PRIMARY}
                      />
                    </View>
                    <View style={styles.weatherMainInfo}>
                      <Text style={styles.weatherCondition}>{selectedDay.weather.condition}</Text>
                      <Text style={styles.weatherDescription}>{selectedDay.weather.description}</Text>
                      <Text style={styles.weatherLocation}>{selectedDay.weather.location}</Text>
                    </View>
                    <View style={styles.weatherTemperature}>
                      <Text style={styles.temperatureValue}>{selectedDay.weather.temperature}°C</Text>
                    </View>
                  </View>

                  <View style={styles.weatherDetailsContainer}>
                    <View style={styles.weatherDetailRow}>
                      <View style={styles.weatherDetailItem}>
                        <Ionicons name="water" size={20} color={COLORS.PRIMARY} />
                        <View style={styles.weatherDetailText}>
                          <Text style={styles.weatherDetailLabel}>Humidity</Text>
                          <Text style={styles.weatherDetailValue}>{selectedDay.weather.humidity}%</Text>
                        </View>
                      </View>

                      <View style={styles.weatherDetailItem}>
                        <Ionicons name="speedometer" size={20} color={COLORS.PRIMARY} />
                        <View style={styles.weatherDetailText}>
                          <Text style={styles.weatherDetailLabel}>Pressure</Text>
                          <Text style={styles.weatherDetailValue}>{selectedDay.weather.pressure} hPa</Text>
                        </View>
                      </View>
                    </View>

                    <View style={styles.weatherDetailRow}>
                      <View style={styles.weatherDetailItem}>
                        <Ionicons name="navigate" size={20} color={COLORS.PRIMARY} />
                        <View style={styles.weatherDetailText}>
                          <Text style={styles.weatherDetailLabel}>Wind Speed</Text>
                          <Text style={styles.weatherDetailValue}>{selectedDay.weather.windSpeed} m/s</Text>
                        </View>
                      </View>

                      <View style={styles.weatherDetailItem}>
                        <Ionicons name="flower" size={20} color={COLORS.PRIMARY} />
                        <View style={styles.weatherDetailText}>
                          <Text style={styles.weatherDetailLabel}>Pollen</Text>
                          <Text style={styles.weatherDetailValue}>{selectedDay.weather.pollen}</Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  <View style={styles.weatherImpactContainer}>
                    <Text style={styles.weatherImpactTitle}>Potential Impact on Asthma:</Text>
                    <Text style={styles.weatherImpactText}>
                      {selectedDay.weather.humidity > 70 ?
                        "High humidity can increase mold growth and may trigger asthma symptoms." :
                        selectedDay.weather.condition === 'Rain' || selectedDay.weather.condition === 'Thunderstorm' ?
                        "Rain can wash away pollen, but thunderstorms can increase asthma triggers." :
                        selectedDay.weather.condition === 'Clear' && selectedDay.weather.pollen === 'High' ?
                        "Clear weather with high pollen counts can trigger asthma symptoms in sensitive individuals." :
                        "Current weather conditions are generally favorable for asthma management."}
                    </Text>
                  </View>
                </View>

                {selectedDay.notes && (
                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Notes</Text>
                    <Text style={styles.detailSectionContent}>{selectedDay.notes}</Text>
                  </View>
                )}

                <View style={styles.detailSection}>
                  <Text style={styles.detailSectionTitle}>Recommendations</Text>
                  <View style={styles.recommendationContainer}>
                    {selectedDay.peakFlow ? (
                      // Recommendations based on peak flow readings
                      selectedDay.peakFlow < 300 || selectedDay.symptoms.some(s => s.severity === 'severe') ? (
                        <>
                          <View style={[styles.recommendationItem, styles.dangerRecommendation]}>
                            <Ionicons name="warning" size={24} color={COLORS.WHITE} />
                            <Text style={styles.recommendationText}>
                              Your symptoms indicate your asthma may not be well controlled. Consider using your rescue inhaler and contact your doctor.
                            </Text>
                          </View>
                        </>
                      ) : selectedDay.peakFlow < 350 || selectedDay.symptoms.length > 0 ? (
                        <>
                          <View style={[styles.recommendationItem, styles.warningRecommendation]}>
                            <Ionicons name="alert-circle" size={24} color={COLORS.WARNING} />
                            <Text style={[styles.recommendationText, { color: COLORS.TEXT }]}>
                              Monitor your symptoms closely. Make sure to take your controller medications as prescribed.
                            </Text>
                          </View>
                        </>
                      ) : (
                        <>
                          <View style={[styles.recommendationItem, styles.goodRecommendation]}>
                            <Ionicons name="checkmark-circle" size={24} color={COLORS.WHITE} />
                            <Text style={styles.recommendationText}>
                              Your asthma appears to be well controlled. Continue your current management plan.
                            </Text>
                          </View>
                        </>
                      )
                    ) : (
                      // Recommendations based on symptoms only
                      selectedDay.symptoms.some(s => s.severity === 'severe') ? (
                        <>
                          <View style={[styles.recommendationItem, styles.dangerRecommendation]}>
                            <Ionicons name="warning" size={24} color={COLORS.WHITE} />
                            <Text style={styles.recommendationText}>
                              Your severe symptoms indicate your asthma may not be well controlled. Consider using your rescue inhaler and contact your doctor.
                            </Text>
                          </View>
                        </>
                      ) : selectedDay.symptoms.some(s => s.severity === 'moderate') ? (
                        <>
                          <View style={[styles.recommendationItem, styles.warningRecommendation]}>
                            <Ionicons name="alert-circle" size={24} color={COLORS.WARNING} />
                            <Text style={[styles.recommendationText, { color: COLORS.TEXT }]}>
                              Your moderate symptoms suggest you should monitor your condition closely. Make sure to take your controller medications as prescribed.
                            </Text>
                          </View>
                        </>
                      ) : selectedDay.symptoms.length > 0 ? (
                        <>
                          <View style={[styles.recommendationItem, styles.warningRecommendation]}>
                            <Ionicons name="alert-circle" size={24} color={COLORS.WARNING} />
                            <Text style={[styles.recommendationText, { color: COLORS.TEXT }]}>
                              You have mild symptoms. Continue to monitor and take your medications as prescribed.
                            </Text>
                          </View>
                        </>
                      ) : (
                        <>
                          <View style={[styles.recommendationItem, styles.goodRecommendation]}>
                            <Ionicons name="checkmark-circle" size={24} color={COLORS.WHITE} />
                            <Text style={styles.recommendationText}>
                              No symptoms reported. Your asthma appears to be well controlled. Continue your current management plan.
                            </Text>
                          </View>
                        </>
                      )
                    )}
                  </View>
                </View>

                <View style={styles.detailButtonsContainer}>
                  <Button
                    title={i18n.t('close')}
                    onPress={() => setDetailModalVisible(false)}
                    style={styles.detailButton}
                  />
                  <Button
                    title={i18n.t('delete')}
                    onPress={() => {
                      setDetailModalVisible(false);
                      confirmDeleteReport(selectedDay);
                    }}
                    type="outline"
                    style={[styles.detailButton, styles.deleteDetailButton]}
                    textStyle={{color: COLORS.DANGER}}
                  />
                </View>
              </ScrollView>
            )}
          </View>
        </View>
      </Modal>

      {/* Custom Alert */}
      <CustomAlert
        visible={alertVisible}
        onClose={() => setAlertVisible(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
        showCancel={alertConfig.showCancel}
        confirmText={alertConfig.confirmText}
        cancelText={alertConfig.cancelText}
        onConfirm={alertConfig.onConfirm}
        confirmType={alertConfig.confirmType}
        autoClose={alertConfig.autoClose}
        autoCloseTimeout={alertConfig.autoCloseTimeout}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.small,
  },
  cardTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  infoButton: {
    padding: 4,
  },
  infoText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.PRIMARY,
    fontStyle: 'italic',
    marginTop: SPACING.small,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  tab: {
    flex: 1,
    paddingVertical: SPACING.medium,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.PRIMARY,
  },
  tabText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  activeTabText: {
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.PRIMARY,
  },
  tabContent: {
    flex: 1,
    padding: SPACING.medium,
    paddingBottom: 120, // Increased padding at the bottom (90 + 30 extra)
  },
  section: {
    marginBottom: SPACING.medium,
  },
  severityContainer: {
    marginLeft: SPACING.large,
    marginBottom: SPACING.medium,
  },
  severityLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  severityOptions: {
    flexDirection: 'row',
  },
  severityOption: {
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
    marginRight: SPACING.small,
  },
  selectedSeverity: {
    borderWidth: 2,
    borderColor: COLORS.TEXT,
  },
  severityText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.WHITE,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  submitButton: {
    marginBottom: SPACING.large,
  },
  historyItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
    paddingVertical: SPACING.medium,
  },
  historyItem: {
    flex: 1,
  },
  deleteButton: {
    padding: SPACING.small,
    marginLeft: SPACING.small,
  },
  deleteAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.DANGER,
    padding: SPACING.small,
    borderRadius: BORDER_RADIUS.medium,
    marginBottom: SPACING.medium,
  },
  deleteAllButtonText: {
    color: COLORS.WHITE,
    fontWeight: FONTS.WEIGHTS.medium,
    marginLeft: SPACING.xs,
  },
  loadingContainer: {
    padding: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_BACKGROUND,
    padding: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
    shadowColor: COLORS.SHADOW,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingText: {
    marginLeft: SPACING.small,
    color: COLORS.TEXT,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  dateTimeContainer: {
    flexDirection: 'column',
  },
  historyDate: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  historyTime: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT_LIGHT || COLORS.TEXT + '80',
    marginTop: 2,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  historyDetails: {
    marginLeft: SPACING.small,
  },
  historyDetail: {
    flexDirection: 'row',
    marginBottom: SPACING.xs,
  },
  detailLabel: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    opacity: 0.7,
    width: 80,
  },
  detailValue: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    flex: 1,
  },
  chartTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
    textAlign: 'center',
  },
  chartDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
    textAlign: 'center',
    lineHeight: 20,
  },
  chartContainer: {
    flexDirection: 'row',
    height: 220,
    marginVertical: SPACING.medium,
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.small,
    padding: SPACING.small,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  chartYAxis: {
    width: 70,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: SPACING.xs,
  },
  gridLine: {
    position: 'absolute',
    right: -SPACING.xs,
    width: '1000%',
    height: 1,
    backgroundColor: COLORS.TEXT + '15',
  },
  chartLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
    marginRight: SPACING.small,
    zIndex: 1,
  },
  chart: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: '100%',
    borderLeftWidth: 1,
    borderBottomWidth: 1,
    borderColor: COLORS.TEXT + '30',
    paddingBottom: SPACING.medium,
  },
  chartBar: {
    flex: 1,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  barContainer: {
    width: 30,
    height: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
    position: 'relative',
  },
  peakFlowBar: {
    width: 16,
    borderTopLeftRadius: BORDER_RADIUS.small,
    borderTopRightRadius: BORDER_RADIUS.small,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  symptomIndicatorContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  symptomIndicator: {
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.WHITE,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
  symptomIndicatorText: {
    fontSize: FONTS.SIZES.xs,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.WHITE,
  },
  chartXLabel: {
    fontSize: FONTS.SIZES.small,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginTop: SPACING.xs,
  },
  chartLegendContainer: {
    marginTop: SPACING.medium,
    backgroundColor: COLORS.LIGHT_BG + '50',
    padding: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
  },
  legendTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  chartLegend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.medium,
    marginBottom: SPACING.small,
    width: '45%',
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 2,
    marginRight: SPACING.xs,
  },
  legendText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
  },
  interpretationContainer: {
    marginTop: SPACING.medium,
    backgroundColor: COLORS.PRIMARY + '10',
    padding: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.PRIMARY,
  },
  interpretationTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  interpretationText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    lineHeight: 22,
  },
  emptyMessage: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    padding: SPACING.large,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailModalContainer: {
    width: '90%',
    maxHeight: '90%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.large,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  detailModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
    backgroundColor: COLORS.PRIMARY,
  },
  detailModalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.WHITE,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailModalContent: {
    padding: SPACING.medium,
  },
  detailSection: {
    marginBottom: SPACING.large,
  },
  detailSectionTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  detailSectionContent: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    lineHeight: 22,
  },
  peakFlowIndicator: {
    height: 20,
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.small,
    marginBottom: SPACING.small,
    overflow: 'hidden',
  },
  peakFlowLevel: {
    height: '100%',
    borderRadius: BORDER_RADIUS.small,
  },
  symptomItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  severityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: SPACING.small,
  },
  symptomText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.medium,
    backgroundColor: COLORS.PRIMARY + '10',
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
  },
  weatherIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  weatherMainInfo: {
    flex: 1,
    marginLeft: SPACING.medium,
  },
  weatherCondition: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  weatherDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT + 'CC',
    marginBottom: SPACING.xs,
  },
  weatherLocation: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '99',
  },
  weatherTemperature: {
    alignItems: 'flex-end',
  },
  temperatureValue: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
  },
  weatherDetailsContainer: {
    backgroundColor: COLORS.LIGHT_BG + '50',
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
  },
  weatherDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.small,
  },
  weatherDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
  },
  weatherDetailText: {
    marginLeft: SPACING.small,
  },
  weatherDetailLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '99',
  },
  weatherDetailValue: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  weatherImpactContainer: {
    backgroundColor: COLORS.PRIMARY + '10',
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.PRIMARY,
  },
  weatherImpactTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  weatherImpactText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    lineHeight: 22,
  },
  weatherContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: SPACING.small,
  },
  weatherItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: SPACING.medium,
  },
  weatherText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginLeft: SPACING.small,
  },
  recommendationContainer: {
    marginTop: SPACING.small,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
    marginBottom: SPACING.small,
  },
  goodRecommendation: {
    backgroundColor: COLORS.SUCCESS,
  },
  warningRecommendation: {
    backgroundColor: COLORS.WARNING + '30',
  },
  dangerRecommendation: {
    backgroundColor: COLORS.DANGER,
  },
  recommendationText: {
    flex: 1,
    fontSize: FONTS.SIZES.medium,
    color: COLORS.WHITE,
    marginLeft: SPACING.small,
    lineHeight: 20,
  },
  detailButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.medium,
    marginBottom: SPACING.large,
  },
  detailButton: {
    flex: 0.48,
  },
  deleteDetailButton: {
    borderColor: COLORS.DANGER,
  },
  deleteIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.DANGER + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: SPACING.large * 2,
  },
  loadingIndicator: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.PRIMARY + '10',
    padding: SPACING.large,
    borderRadius: BORDER_RADIUS.medium,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY + '30',
    width: '90%',
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  loadingText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginTop: SPACING.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    textAlign: 'center',
  },
  loadingSubText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '99',
    marginTop: SPACING.tiny,
    textAlign: 'center',
  },
  // Success modal styles
  modalContainer: {
    width: '85%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.large,
    padding: SPACING.large,
    alignItems: 'center',
    elevation: 5,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  successIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.SUCCESS + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  modalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
    textAlign: 'center',
  },
  modalDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT + 'CC',
    marginBottom: SPACING.large,
    textAlign: 'center',
    lineHeight: 22,
  },
  modalButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    marginTop: 0,
  },
  modalButtonHalf: {
    flex: 0.48,
  },
});

export default SymptomTrackerScreen;
