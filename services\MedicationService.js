/**
 * Medication Service
 * Provides methods for interacting with medication data in Supabase
 */

import supabase from '../config/supabase-client';
import { getCurrentUser } from './SupabaseService';

/**
 * Save medication
 * @param {Object} medicationData - The medication data
 * @returns {Promise<Object>} The result of the operation
 */
export const saveMedication = async (medicationData) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Create medication object for medications_unified table
    const medication = {
      user_id: user.id,
      name: medicationData.name,
      dosage: medicationData.dosage,
      medication_type: medicationData.frequency || medicationData.type,
      notes: medicationData.notes || '',

      // Add schedule information directly to the medication
      morning_time: medicationData.schedule?.morning?.enabled ? medicationData.schedule.morning.time : null,
      morning_enabled: medicationData.schedule?.morning?.enabled || false,
      afternoon_time: medicationData.schedule?.afternoon?.enabled ? medicationData.schedule.afternoon.time : null,
      afternoon_enabled: medicationData.schedule?.afternoon?.enabled || false,
      evening_time: medicationData.schedule?.evening?.enabled ? medicationData.schedule.evening.time : null,
      evening_enabled: medicationData.schedule?.evening?.enabled || false,
      days_of_week: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],

      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to medications_unified table
    const { data, error } = await supabase
      .from('medications_unified')
      .insert([medication])
      .select();

    if (error) throw error;

    return {
      success: true,
      medication: data[0]
    };
  } catch (error) {
    console.error('Error saving medication:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Note: Reminders are now stored directly in the medications_unified table

/**
 * Get all medications for the current user
 * @returns {Promise<Object>} The medications
 */
export const getMedications = async () => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Query medications for the current user from the unified table
    const { data, error } = await supabase
      .from('medications_unified')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return {
      success: true,
      medications: data
    };
  } catch (error) {
    console.error('Error getting medications:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update a medication
 * @param {string} id - The medication ID
 * @param {Object} medicationData - The updated medication data
 * @returns {Promise<Object>} The result of the operation
 */
export const updateMedication = async (id, medicationData) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Create medication object for medications_unified table
    const medication = {
      name: medicationData.name,
      dosage: medicationData.dosage,
      medication_type: medicationData.frequency || medicationData.type,
      notes: medicationData.notes || '',

      // Add schedule information directly to the medication
      morning_time: medicationData.schedule?.morning?.enabled ? medicationData.schedule.morning.time : null,
      morning_enabled: medicationData.schedule?.morning?.enabled || false,
      afternoon_time: medicationData.schedule?.afternoon?.enabled ? medicationData.schedule.afternoon.time : null,
      afternoon_enabled: medicationData.schedule?.afternoon?.enabled || false,
      evening_time: medicationData.schedule?.evening?.enabled ? medicationData.schedule.evening.time : null,
      evening_enabled: medicationData.schedule?.evening?.enabled || false,
      days_of_week: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],

      updated_at: new Date().toISOString()
    };

    // Update the medication in the unified table
    const { data, error } = await supabase
      .from('medications_unified')
      .update(medication)
      .eq('id', id)
      .eq('user_id', user.id)
      .select();

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error('Error updating medication:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete a medication
 * @param {string} id - The medication ID
 * @returns {Promise<Object>} The result of the operation
 */
export const deleteMedication = async (id) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Delete the medication from the unified table
    const { error } = await supabase
      .from('medications_unified')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error('Error deleting medication:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Update medication taken status
 * @param {string} id - The medication ID
 * @param {string} timeOfDay - The time of day (morning, afternoon, evening)
 * @param {boolean} taken - Whether the medication was taken
 * @returns {Promise<Object>} The result of the operation
 */
export const updateMedicationTakenStatus = async (id, timeOfDay, taken) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Create update object based on time of day
    const updateData = {};
    const timestamp = new Date().toISOString();

    if (timeOfDay === 'morning') {
      updateData.morning_taken = taken;
      if (taken) {
        updateData.morning_taken_at = timestamp;
      } else {
        updateData.morning_taken_at = null;
      }
    } else if (timeOfDay === 'afternoon') {
      updateData.afternoon_taken = taken;
      if (taken) {
        updateData.afternoon_taken_at = timestamp;
      } else {
        updateData.afternoon_taken_at = null;
      }
    } else if (timeOfDay === 'evening') {
      updateData.evening_taken = taken;
      if (taken) {
        updateData.evening_taken_at = timestamp;
      } else {
        updateData.evening_taken_at = null;
      }
    } else {
      throw new Error('Invalid time of day');
    }

    // Update the medication in the database
    const { data, error } = await supabase
      .from('medications_unified')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select();

    if (error) throw error;

    return {
      success: true,
      data: data[0]
    };
  } catch (error) {
    console.error('Error updating medication taken status:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default {
  saveMedication,
  getMedications,
  updateMedication,
  deleteMedication,
  updateMedicationTakenStatus
};
