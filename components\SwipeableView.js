/**
 * SwipeableView Component
 * Enhances swipe gestures between screens with smooth transitions
 */

import React, { useRef } from 'react';
import { View, StyleSheet, Dimensions, Animated } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { useNavigation, CommonActions } from '@react-navigation/native';

const { width } = Dimensions.get('window');

const SwipeableView = ({
  children,
  nextScreen,
  prevScreen,
  disableLeftSwipe = false,
  disableRightSwipe = false,
  style
}) => {
  const navigation = useNavigation();
  const translateX = useRef(new Animated.Value(0)).current;

  // Handle gesture state changes
  const handleGestureStateChange = ({ nativeEvent }) => {
    const { state, translationX, velocityX } = nativeEvent;

    // When gesture begins, reset animation
    if (state === State.BEGAN) {
      translateX.setValue(0);
    }

    // During the gesture, update the animation value
    else if (state === State.ACTIVE) {
      // Limit the translation based on direction and disabled states
      if ((translationX > 0 && disableRightSwipe) || (translationX < 0 && disableLeftSwipe)) {
        // Reduce the effect for disabled directions (resistance)
        translateX.setValue(translationX * 0.2);
      } else {
        translateX.setValue(translationX);
      }
    }

    // When gesture ends, determine if we should navigate
    else if (state === State.END) {
      const swipeThreshold = width * 0.15; // 15% of screen width

      // Animate back to center
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
        bounciness: 8,
        speed: 12,
      }).start();

      // Swipe right (to go to previous screen)
      if (translationX > swipeThreshold || velocityX > 500) {
        if (!disableRightSwipe && prevScreen) {
          // Use a slight delay to allow animation to start
          setTimeout(() => {
            navigation.dispatch(
              CommonActions.navigate({
                name: prevScreen,
              })
            );
          }, 100);
        }
      }
      // Swipe left (to go to next screen)
      else if (translationX < -swipeThreshold || velocityX < -500) {
        if (!disableLeftSwipe && nextScreen) {
          // Use a slight delay to allow animation to start
          setTimeout(() => {
            navigation.dispatch(
              CommonActions.navigate({
                name: nextScreen,
              })
            );
          }, 100);
        }
      }
    }
  };

  return (
    <PanGestureHandler
      onHandlerStateChange={handleGestureStateChange}
      onGestureEvent={Animated.event(
        [{ nativeEvent: { translationX: translateX } }],
        { useNativeDriver: true }
      )}
      activeOffsetX={[-10, 10]} // Start recognizing after 10px movement (more sensitive)
      failOffsetY={[-20, 20]} // Fail if vertical movement exceeds 20px
    >
      <Animated.View
        style={[
          styles.container,
          style,
          { transform: [{ translateX }] }
        ]}
      >
        {children}
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SwipeableView;
