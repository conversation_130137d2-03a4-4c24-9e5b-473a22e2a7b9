# Google Sign-In Setup for Smart AsthmaCare App

This document provides instructions for setting up Google Sign-In for the Smart AsthmaCare asthma management app.

## Prerequisites

1. A Google Cloud Platform account
2. A Supabase project

## Step 1: Configure Google OAuth in Google Cloud Platform

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" → "Credentials"
4. Click "Create Credentials" → "OAuth client ID"
5. Select "Web application" as the application type
6. Add a name for your OAuth client (e.g., "Smart AsthmaCare App Web")
7. Add authorized JavaScript origins:
   - `https://zwinrrbdlecsbuogvzky.supabase.co`
8. Add authorized redirect URIs:
   - `https://zwinrrbdlecsbuogvzky.supabase.co/auth/v1/callback`
9. Click "Create"
10. Note your Client ID and Client Secret

## Step 2: Configure Google OAuth in Supabase

1. Go to your [Supabase Dashboard](https://app.supabase.com/project/zwinrrbdlecsbuogvzky)
2. Navigate to "Authentication" → "Providers"
3. Find "Google" in the list of providers and click on it
4. Toggle the "Enabled" switch to enable Google authentication
5. Enter your Google Client ID and Client Secret from Step 1
6. Save the changes

## Step 3: Configure Google Sign-In in Your App

1. Update your `.env` file with your Google Web Client ID:
   ```
   GOOGLE_WEB_CLIENT_ID=************-8sjbb0o2209djig0dfb74k50jf4bo8g3.apps.googleusercontent.com
   ```

2. Make sure the Google Sign-In configuration in your app is correct:
   ```javascript
   GoogleSignin.configure({
     webClientId: GOOGLE_WEB_CLIENT_ID,
     offlineAccess: true,
   });
   ```

## Step 4: Test Google Sign-In

1. Run your app
2. Navigate to the login screen
3. Tap the "Sign in with Google" button
4. Select a Google account to sign in with
5. Verify that you are successfully signed in

## Troubleshooting

### Common Issues

1. **"The client ID provided is not valid"**
   - Make sure the Client ID in your `.env` file matches the one in Google Cloud Console
   - Ensure you're using the Web Client ID, not the Android or iOS Client ID

2. **"Google Sign-In is not properly configured in Supabase"**
   - Make sure you've enabled Google authentication in your Supabase project
   - Verify that the Client ID and Client Secret in Supabase match the ones in Google Cloud Console

3. **"Google Play Services are not available"**
   - Make sure Google Play Services are installed and up to date on your device
   - If testing on an emulator, make sure it has Google Play Services installed

4. **"Failed to get Google ID token"**
   - This could be due to network issues
   - Try signing in again

### Debugging

Add these console logs to help debug Google Sign-In issues:

```javascript
// Before signing in
console.log('Attempting Google Sign-In with Client ID:', GOOGLE_WEB_CLIENT_ID);

// After successful sign-in
console.log('Google Sign-In successful:', userInfo);

// After Supabase auth
console.log('Supabase auth successful:', data);
```

## Additional Resources

- [Google Sign-In for React Native Documentation](https://github.com/react-native-google-signin/google-signin)
- [Supabase OAuth Documentation](https://supabase.com/docs/guides/auth/social-login/auth-google)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
