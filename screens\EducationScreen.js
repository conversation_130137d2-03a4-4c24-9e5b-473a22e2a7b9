/**
 * Educational Resources Screen
 * Enhanced with HTML rendering and improved content
 */

import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, Modal, useWindowDimensions, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Button from '../components/Button';
import RenderHtml from 'react-native-render-html';
import i18n from '../i18n/i18n';

const EducationScreen = ({ navigation }) => {
  const { width } = useWindowDimensions();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedResource, setSelectedResource] = useState(null);

  // Educational resources data
  const resources = [
    {
      id: '1',
      title: 'Understanding Asthma',
      description: 'Learn about what asthma is, how it affects your lungs, and what happens during an asthma attack.',
      icon: 'medical',
      image: require('../assets/understanding asthma.png'),
      content: `
        <img src="understanding_asthma" alt="Asthma airways comparison" />
        <h2>What is Asthma?</h2>
        <p>Asthma is a chronic disease that affects your airways, which are the tubes that carry air in and out of your lungs. If you have asthma, the inside walls of your airways are inflamed (swollen). The inflammation makes the airways very sensitive, and they tend to react strongly to things that you are allergic to or find irritating.</p>

        <h2>What Happens During an Asthma Attack?</h2>
        <p>When the airways react, they get narrower, and less air flows through to your lung tissue. This causes symptoms like wheezing, coughing, chest tightness, and trouble breathing. Some people with asthma may go for extended periods without having any symptoms, interrupted by periodic worsening of their symptoms called asthma attacks.</p>

        <h2>Common Symptoms</h2>
        <ul>
          <li>Shortness of breath</li>
          <li>Chest tightness or pain</li>
          <li>Wheezing when exhaling</li>
          <li>Trouble sleeping caused by shortness of breath, coughing or wheezing</li>
          <li>Coughing or wheezing attacks that are worsened by a respiratory virus</li>
        </ul>
      `,
    },
    {
      id: '2',
      title: 'Common Asthma Triggers',
      description: 'Identify and learn about common triggers that can cause asthma symptoms or attacks.',
      icon: 'alert-circle',
      image: require('../assets/common asthma triggers.png'),
      content: `
        <img src="common_asthma_triggers" alt="Common asthma triggers" />
        <h2>Common Asthma Triggers</h2>
        <p>Asthma triggers are things that can cause asthma symptoms or make them worse. Knowing your triggers can help you avoid them and prevent asthma attacks.</p>

        <h2>Environmental Triggers</h2>
        <ul>
          <li><strong>Dust mites:</strong> Tiny bugs that live in dust, mattresses, furniture, and carpets</li>
          <li><strong>Pollen:</strong> From trees, grasses, and weeds</li>
          <li><strong>Mold:</strong> Grows in damp places like bathrooms and kitchens</li>
          <li><strong>Pet dander:</strong> Tiny flakes of skin from animals with fur or feathers</li>
          <li><strong>Cockroaches:</strong> Their droppings and body parts</li>
        </ul>

        <h2>Other Common Triggers</h2>
        <ul>
          <li><strong>Tobacco smoke:</strong> From cigarettes, pipes, or cigars</li>
          <li><strong>Air pollution:</strong> From factories, cars, and other sources</li>
          <li><strong>Strong odors:</strong> From perfumes, cleaning products, or paint</li>
          <li><strong>Weather changes:</strong> Cold air, humidity, or sudden temperature changes</li>
          <li><strong>Exercise:</strong> Physical activity can trigger symptoms in some people</li>
          <li><strong>Respiratory infections:</strong> Colds, flu, and other infections</li>
          <li><strong>Strong emotions:</strong> Stress, anxiety, or excitement</li>
        </ul>
      `,
    },
    {
      id: '3',
      title: 'How to Use Your Inhaler',
      description: 'Step-by-step instructions on how to properly use different types of inhalers.',
      icon: 'fitness',
      image: require('../assets/how to use your inhaler.png'),
      content: `
        <img src="how_to_use_your_inhaler" alt="How to use an inhaler" />
        <h2>Using a Metered-Dose Inhaler (MDI)</h2>
        <p>Follow these steps to use your MDI correctly:</p>
        <ol>
          <li>Remove the cap and shake the inhaler well</li>
          <li>Stand or sit upright</li>
          <li>Breathe out completely</li>
          <li>Position the inhaler in one of these ways:
            <ul>
              <li>Place the mouthpiece 1-2 inches from your open mouth</li>
              <li>Or use a spacer (recommended)</li>
              <li>Or place the mouthpiece directly in your mouth</li>
            </ul>
          </li>
          <li>Start to breathe in slowly, then press down on the inhaler once</li>
          <li>Continue to breathe in slowly and deeply</li>
          <li>Hold your breath for 10 seconds, then breathe out slowly</li>
          <li>If you need a second puff, wait 30-60 seconds and repeat steps 2-7</li>
        </ol>

        <h2>Using a Dry Powder Inhaler (DPI)</h2>
        <p>DPIs are different from MDIs. Follow these steps:</p>
        <ol>
          <li>Remove the cap and load a dose according to your inhaler's instructions</li>
          <li>Stand or sit upright</li>
          <li>Breathe out completely away from the inhaler</li>
          <li>Place the mouthpiece in your mouth and close your lips around it</li>
          <li>Breathe in quickly and deeply</li>
          <li>Remove the inhaler from your mouth</li>
          <li>Hold your breath for 10 seconds, then breathe out slowly</li>
        </ol>
      `,
    },
    {
      id: '4',
      title: 'Emergency Response',
      description: 'What to do during an asthma attack and when to seek emergency medical help.',
      icon: 'warning',
      image: require('../assets/emergency response.png'),
      content: `
        <img src="emergency_response" alt="Asthma emergency response" />
        <h2>What to Do During an Asthma Attack</h2>
        <p>Follow these steps if you or someone else is having an asthma attack:</p>
        <ol>
          <li>Sit upright and try to remain calm</li>
          <li>Take one puff of your reliever inhaler (usually blue) every 30-60 seconds, up to a maximum of 10 puffs</li>
          <li>If you feel worse or do not improve after 10 puffs, call emergency services</li>
          <li>If emergency services have not arrived within 10 minutes and symptoms are not improving, repeat step 2</li>
        </ol>

        <h2>When to Seek Emergency Help</h2>
        <p>Call emergency services immediately if:</p>
        <ul>
          <li>The person's symptoms do not improve after using the reliever inhaler</li>
          <li>The person is too breathless to speak, eat, or sleep</li>
          <li>The person's breathing is getting faster and they feel they cannot catch their breath</li>
          <li>The person's peak flow reading is less than 50% of their personal best</li>
          <li>The person is exhausted from the effort of breathing</li>
          <li>The person's lips or fingernails are turning blue</li>
          <li>The person loses consciousness</li>
        </ul>
      `,
    },
    {
      id: '5',
      title: 'Living with Asthma',
      description: 'Tips for managing asthma in daily life and maintaining good overall health.',
      icon: 'heart',
      image: require('../assets/living with asthma.png'),
      content: `
        <img src="living_with_asthma" alt="Living with asthma" />
        <h2>Tips for Living Well with Asthma</h2>
        <p>With proper management, most people with asthma can live normal, active lives. Here are some tips:</p>

        <h3>Follow Your Asthma Action Plan</h3>
        <ul>
          <li>Take your medications as prescribed</li>
          <li>Monitor your symptoms and peak flow readings</li>
          <li>Know what to do when symptoms worsen</li>
          <li>Keep regular appointments with your healthcare provider</li>
        </ul>

        <h3>Avoid Your Triggers</h3>
        <ul>
          <li>Keep your home clean and free of dust, mold, and pests</li>
          <li>Use allergen-proof covers on mattresses and pillows</li>
          <li>Stay indoors when pollen or air pollution levels are high</li>
          <li>Avoid smoking and secondhand smoke</li>
        </ul>

        <h3>Stay Active</h3>
        <ul>
          <li>Regular exercise can strengthen your lungs and improve overall health</li>
          <li>Use your reliever inhaler before exercise if recommended</li>
          <li>Warm up before and cool down after exercise</li>
          <li>Choose activities that are less likely to trigger symptoms</li>
        </ul>

        <h3>Maintain Good Overall Health</h3>
        <ul>
          <li>Eat a balanced diet</li>
          <li>Maintain a healthy weight</li>
          <li>Get enough sleep</li>
          <li>Manage stress through relaxation techniques</li>
          <li>Get vaccinated against flu and pneumonia</li>
        </ul>
      `,
    },
  ];

  const handleResourcePress = (resource) => {
    setSelectedResource(resource);
    setModalVisible(true);
  };

  const renderResourceItem = (resource) => (
    <Card
      key={resource.id}
      style={styles.resourceCard}
      onPress={() => handleResourcePress(resource)}
    >
      <View style={styles.resourceHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name={resource.icon} size={24} color={COLORS.WHITE} />
        </View>
        <View style={styles.resourceInfo}>
          <Text style={styles.resourceTitle}>{resource.title}</Text>
          <Text style={styles.resourceDescription} numberOfLines={2}>
            {resource.description}
          </Text>
        </View>
      </View>
      <Button
        title={i18n.t('education.readMore')}
        onPress={() => handleResourcePress(resource)}
        type="outline"
        size="small"
        style={styles.readMoreButton}
      />
    </Card>
  );

  const renderResourceModal = () => {
    if (!selectedResource) return null;

    return (
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{selectedResource.title}</Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalBody}>
              <View style={styles.contentContainer}>
                <RenderHtml
                  contentWidth={width - SPACING.medium * 2}
                  source={{ html: selectedResource.content }}
                  tagsStyles={{
                    h2: {
                      color: COLORS.ACCENT,
                      fontSize: FONTS.SIZES.xl,
                      marginBottom: SPACING.medium,
                      marginTop: SPACING.medium,
                      fontWeight: FONTS.WEIGHTS.semibold
                    },
                    h3: {
                      color: COLORS.PRIMARY,
                      fontSize: FONTS.SIZES.large,
                      marginBottom: SPACING.small,
                      marginTop: SPACING.medium,
                      fontWeight: FONTS.WEIGHTS.medium
                    },
                    p: {
                      color: COLORS.TEXT,
                      fontSize: FONTS.SIZES.medium,
                      lineHeight: 24,
                      marginBottom: SPACING.medium
                    },
                    ul: { marginBottom: SPACING.medium },
                    ol: { marginBottom: SPACING.medium },
                    li: {
                      color: COLORS.TEXT,
                      fontSize: FONTS.SIZES.medium,
                      lineHeight: 22,
                      marginBottom: SPACING.small
                    },
                    img: {
                      borderRadius: BORDER_RADIUS.medium,
                      marginVertical: SPACING.medium,
                      width: '100%',
                      height: 'auto'
                    },
                    // Hide any unwanted elements that might be causing the box
                    div: {
                      padding: 0,
                      margin: 0,
                      backgroundColor: 'transparent'
                    }
                  }}
                  // Customize rendering to remove any unwanted elements
                  renderers={{
                    img: (props) => {
                      // Custom image renderer to handle local images
                      const src = props.tnode.attributes.src;
                      let imageSource;

                      // Check if this is a local image reference
                      switch(src) {
                        case 'understanding_asthma':
                          imageSource = require('../assets/understanding asthma.png');
                          break;
                        case 'common_asthma_triggers':
                          imageSource = require('../assets/common asthma triggers.png');
                          break;
                        case 'how_to_use_your_inhaler':
                          imageSource = require('../assets/how to use your inhaler.png');
                          break;
                        case 'emergency_response':
                          imageSource = require('../assets/emergency response.png');
                          break;
                        case 'living_with_asthma':
                          imageSource = require('../assets/living with asthma.png');
                          break;
                        default:
                          // If not a local reference, use the URI
                          imageSource = { uri: src };
                      }

                      return (
                        <Image
                          source={imageSource}
                          style={{
                            width: '100%',
                            height: 200,
                            borderRadius: BORDER_RADIUS.medium,
                            marginVertical: SPACING.medium
                          }}
                          resizeMode="cover"
                        />
                      );
                    }
                  }}
                />
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('education.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content}>
        <Text style={styles.introText}>
          Learn more about asthma, its triggers, and how to manage it effectively.
        </Text>

        {resources.map(renderResourceItem)}
      </ScrollView>

      {renderResourceModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
    paddingBottom: SPACING.xxl, // Add extra padding at the bottom
  },
  introText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.large,
    textAlign: 'center',
  },
  resourceCard: {
    marginBottom: SPACING.medium,
  },
  resourceHeader: {
    flexDirection: 'row',
    marginBottom: SPACING.medium,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.medium,
  },
  resourceInfo: {
    flex: 1,
  },
  resourceTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  resourceDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  readMoreButton: {
    alignSelf: 'flex-end',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    height: '80%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  modalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    flex: 1,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  modalBody: {
    flex: 1,
  },
  contentContainer: {
    padding: SPACING.medium,
  },
});

export default EducationScreen;
