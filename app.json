{"expo": {"name": "Smart AsthmaCare", "slug": "smart-asthmacare", "version": "1.0.0", "jsEngine": "hermes", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "plugins": ["expo-location"], "scheme": "smartasthmacare", "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Smart AsthmaCare needs access to your location to provide weather information relevant to your asthma condition."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION"], "package": "com.smartasthmacare.app", "googleServicesFile": "./google-services.json"}, "web": {"favicon": "./assets/favicon.png"}}}