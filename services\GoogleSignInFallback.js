/**
 * Fallback Google Sign-In service for Smart AsthmaCare app
 * This is used when the native Google Sign-In module is not available
 */

import { Platform } from 'react-native';
import { GOOGLE_WEB_CLIENT_ID } from '@env';
import supabase from '../config/supabase';

// Mock status codes to match the real GoogleSignin module
const statusCodes = {
  SIGN_IN_CANCELLED: 'SIGN_IN_CANCELLED',
  IN_PROGRESS: 'IN_PROGRESS',
  PLAY_SERVICES_NOT_AVAILABLE: 'PLAY_SERVICES_NOT_AVAILABLE',
};

// Initialize Google Sign-In
const configureGoogleSignIn = () => {
  try {
    console.log('Configuring Fallback Google Sign-In with client ID:', GOOGLE_WEB_CLIENT_ID);
    console.log('Fallback Google Sign-In configured successfully');
    return true;
  } catch (error) {
    console.error('Error configuring Fallback Google Sign-In:', error);
    return false;
  }
};

// Sign in with Google (fallback implementation)
const signInWithGoogle = async () => {
  try {
    console.log('Using Fallback Google Sign-In implementation');
    
    // Create a mock user for testing
    const mockUser = {
      id: 'google-mock-user-id',
      email: '<EMAIL>',
      name: 'Mock Google User',
      photo: 'https://via.placeholder.com/150',
    };
    
    // Sign in with Supabase using email/password as a fallback
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password',
    });
    
    if (error) {
      console.error('Supabase fallback sign-in error:', error);
      throw error;
    }
    
    console.log('Fallback Google sign-in successful');
    return { 
      user: data.user || {
        id: 'mock-user-id',
        email: '<EMAIL>',
        user_metadata: { name: 'Test User' }
      }, 
      userInfo: mockUser 
    };
  } catch (error) {
    console.error('Fallback Google Sign-In error:', error);
    throw new Error('Fallback Google Sign-In failed: ' + error.message);
  }
};

// Sign out from Google
const signOutFromGoogle = async () => {
  try {
    console.log('Fallback Google Sign-Out successful');
    return true;
  } catch (error) {
    console.error('Fallback Google Sign-Out error:', error);
    return false;
  }
};

// Get current user info
const getCurrentUserInfo = async () => {
  try {
    const mockUser = {
      id: 'google-mock-user-id',
      email: '<EMAIL>',
      name: 'Mock Google User',
      photo: 'https://via.placeholder.com/150',
    };
    return mockUser;
  } catch (error) {
    console.error('Error getting current user info:', error);
    return null;
  }
};

// Check if user is signed in
const isUserSignedIn = async () => {
  try {
    return false;
  } catch (error) {
    console.error('Error checking if user is signed in:', error);
    return false;
  }
};

export default {
  configureGoogleSignIn,
  signInWithGoogle,
  signOutFromGoogle,
  getCurrentUserInfo,
  isUserSignedIn,
  statusCodes,
};
