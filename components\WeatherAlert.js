/**
 * Weather Alert Component
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';
import i18n from '../i18n/i18n';

const WeatherAlert = ({ riskLevel, message, temperature, humidity, onPress }) => {
  // Determine background color based on risk level
  const getBackgroundColor = () => {
    switch (riskLevel) {
      case 'high':
        return COLORS.DANGER;
      case 'moderate':
      case 'medium': // For backward compatibility
        return COLORS.WARNING;
      case 'low':
      default:
        return COLORS.SUCCESS;
    }
  };

  // Determine icon based on risk level
  const getIcon = () => {
    switch (riskLevel) {
      case 'high':
        return 'warning';
      case 'moderate':
      case 'medium': // For backward compatibility
        return 'alert-circle';
      case 'low':
      default:
        return 'checkmark-circle';
    }
  };

  // Determine risk text based on risk level
  const getRiskText = () => {
    switch (riskLevel) {
      case 'high':
        return i18n.t('home.highRisk');
      case 'moderate':
      case 'medium': // For backward compatibility
        return i18n.t('home.mediumRisk');
      case 'low':
      default:
        return i18n.t('home.lowRisk');
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: getBackgroundColor() },
      ]}
    >
      <View style={styles.header}>
        <Ionicons name={getIcon()} size={24} color={COLORS.WHITE} />
        <Text style={styles.riskText}>{getRiskText()}</Text>
      </View>

      <Text style={styles.message}>{message}</Text>

      <View style={styles.weatherInfo}>
        <View style={styles.weatherItem}>
          <Ionicons name="thermometer-outline" size={18} color={COLORS.WHITE} />
          <Text style={styles.weatherText}>{temperature}°C</Text>
        </View>
        <View style={styles.weatherItem}>
          <Ionicons name="water-outline" size={18} color={COLORS.WHITE} />
          <Text style={styles.weatherText}>{humidity}%</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  riskText: {
    color: COLORS.WHITE,
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    marginLeft: SPACING.small,
  },
  message: {
    color: COLORS.WHITE,
    fontSize: FONTS.SIZES.medium,
    marginBottom: SPACING.small,
  },
  weatherInfo: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  weatherItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.large,
  },
  weatherText: {
    color: COLORS.WHITE,
    fontSize: FONTS.SIZES.medium,
    marginLeft: SPACING.xs,
  },
});

export default WeatherAlert;
