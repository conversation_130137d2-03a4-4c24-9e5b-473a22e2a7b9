/**
 * Database Service
 * Handles all interactions with the Supabase database
 * Updated to work with the new unified table structure
 */

import supabase from '../config/supabase-client';
import { v4 as uuidv4 } from 'uuid';

class DatabaseService {
  // PROFILE METHODS

  /**
   * Get the current user's profile
   */
  async getUserProfile() {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .single();

      if (error) throw error;
      return { success: true, data: profile };
    } catch (error) {
      console.error('Error getting user profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update the current user's profile
   */
  async updateUserProfile(profileData) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', user.id)
        .select();

      if (error) throw error;
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error updating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // USER SETTINGS METHODS

  /**
   * Get the current user's settings
   */
  async getUserSettings() {
    try {
      const { data: settings, error } = await supabase
        .from('user_settings')
        .select('*')
        .single();

      if (error) throw error;
      return { success: true, data: settings };
    } catch (error) {
      console.error('Error getting user settings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update the current user's settings
   */
  async updateUserSettings(settingsData) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('user_settings')
        .update(settingsData)
        .eq('id', user.id);

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error updating user settings:', error);
      return { success: false, error: error.message };
    }
  }

  // MEDICATION METHODS (USING UNIFIED TABLE)

  /**
   * Get all medications for the current user
   */
  async getMedications() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('medications_unified')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting medications:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a new medication for the current user
   */
  async addMedication(medicationData) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Add user_id to the medication data
      const medicationWithUserId = {
        ...medicationData,
        user_id: user.id
      };

      const { data, error } = await supabase
        .from('medications_unified')
        .insert([medicationWithUserId])
        .select();

      if (error) throw error;
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error adding medication:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update a medication
   */
  async updateMedication(medicationId, medicationData) {
    try {
      const { data, error } = await supabase
        .from('medications_unified')
        .update(medicationData)
        .eq('id', medicationId)
        .select();

      if (error) throw error;
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error updating medication:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a medication
   */
  async deleteMedication(medicationId) {
    try {
      console.log('DatabaseService: Deleting medication with ID:', medicationId);

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // First check if the medication exists
      const { data: existingData, error: checkError } = await supabase
        .from('medications_unified')
        .select('id')
        .eq('id', medicationId)
        .eq('user_id', user.id)
        .single();

      console.log('Existing medication:', existingData);

      if (checkError) {
        console.error('Error checking medication:', checkError);
        if (checkError.code === 'PGRST116') {
          return { success: false, error: 'Medication not found' };
        }
        throw checkError;
      }

      const { error } = await supabase
        .from('medications_unified')
        .delete()
        .eq('id', medicationId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error in delete operation:', error);
        throw error;
      }

      console.log('Medication deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting medication:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete all medications for the current user
   */
  async deleteAllMedications() {
    try {
      console.log('DatabaseService: Deleting all medications');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // First check how many medications exist
      const { data: medications, error: countError } = await supabase
        .from('medications_unified')
        .select('id')
        .eq('user_id', user.id);

      console.log(`Found ${medications?.length || 0} medications to delete`);

      if (countError) {
        console.error('Error counting medications:', countError);
      }

      // Delete all medications for this user
      const { error } = await supabase
        .from('medications_unified')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting all medications:', error);
        throw error;
      }

      console.log('All medications deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting all medications:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Log medication as taken
   */
  async logMedicationTaken(medicationId, timeOfDay, takenAt = new Date()) {
    try {
      // Prepare update data based on time of day
      const updateData = {};
      if (timeOfDay === 'morning') {
        updateData.morning_taken = true;
        updateData.morning_taken_at = takenAt;
      } else if (timeOfDay === 'afternoon') {
        updateData.afternoon_taken = true;
        updateData.afternoon_taken_at = takenAt;
      } else if (timeOfDay === 'evening') {
        updateData.evening_taken = true;
        updateData.evening_taken_at = takenAt;
      }

      const { data, error } = await supabase
        .from('medications_unified')
        .update(updateData)
        .eq('id', medicationId)
        .select();

      if (error) throw error;
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error logging medication as taken:', error);
      return { success: false, error: error.message };
    }
  }

  // Note: Medication reminder and log methods have been integrated into the medications_unified table

  // HEALTH REPORTS METHODS (UNIFIED TABLE FOR PEAK FLOW, SYMPTOMS, WEATHER)

  /**
   * Get all health reports for the current user
   */
  async getHealthReports() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('health_reports')
        .select('*')
        .eq('user_id', user.id)
        .order('report_date', { ascending: false });

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting health reports:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get peak flow readings (filtered from health reports)
   */
  async getPeakFlowReadings() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('health_reports')
        .select('*')
        .eq('user_id', user.id)
        .eq('report_type', 'peak_flow')
        .order('report_date', { ascending: false });

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting peak flow readings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a new health report (peak flow, symptom, or weather)
   */
  async addHealthReport(reportData) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Add user_id to the report data
      const reportWithUserId = {
        ...reportData,
        user_id: user.id
      };

      const { data, error } = await supabase
        .from('health_reports')
        .insert([reportWithUserId])
        .select();

      if (error) throw error;
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error adding health report:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a new peak flow reading (wrapper for addHealthReport)
   */
  async addPeakFlowReading(readingData) {
    try {
      // Format the data for the health_reports table
      const healthReportData = {
        report_type: 'peak_flow',
        report_date: readingData.reading_date || new Date(),
        peak_flow_reading: readingData.reading_value,
        reading_time_of_day: readingData.reading_time_of_day,
        predicted_peak_flow: readingData.predicted_peak_flow,
        percent_of_personal_best: readingData.percent_of_personal_best,
        zone: readingData.zone,
        symptoms: readingData.symptoms,
        symptom_severity: readingData.symptom_severity,
        medication_used: readingData.medication_used,
        trigger_factors: readingData.trigger_factors,
        notes: readingData.notes,
        weather_condition: readingData.weather_condition,
        temperature: readingData.temperature,
        humidity: readingData.humidity,
        air_quality_index: readingData.air_quality_index,
        pollen_count: readingData.pollen_count,
        location: readingData.location
      };

      return await this.addHealthReport(healthReportData);
    } catch (error) {
      console.error('Error adding peak flow reading:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if the required tables exist
   */
  async checkAndCreateTables() {
    try {
      console.log('Checking if required tables exist...');

      // Check if health_reports table exists
      const { error: healthReportsError } = await supabase
        .from('health_reports')
        .select('id')
        .limit(1);

      if (healthReportsError && healthReportsError.code === '42P01') {
        console.log('health_reports table does not exist. Please run the migration script.');
        return {
          success: false,
          error: 'Database tables not found. Please run the migration script.'
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Error checking tables:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a health report
   */
  async deleteHealthReport(reportId) {
    try {
      console.log('DatabaseService: Deleting health report with ID:', reportId);

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // First check if the report exists
      const { data: existingData, error: checkError } = await supabase
        .from('health_reports')
        .select('id')
        .eq('id', reportId)
        .eq('user_id', user.id)
        .single();

      console.log('Existing health report:', existingData);

      if (checkError) {
        console.error('Error checking health report:', checkError);
        if (checkError.code === 'PGRST116') {
          return { success: false, error: 'Report not found' };
        }
        throw checkError;
      }

      // Delete the report
      const { error } = await supabase
        .from('health_reports')
        .delete()
        .eq('id', reportId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error in delete operation:', error);
        throw error;
      }

      console.log('Health report deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting health report:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get symptom reports (filtered from health reports)
   */
  async getSymptomReports() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('health_reports')
        .select('*')
        .eq('user_id', user.id)
        .eq('report_type', 'symptom')
        .order('report_date', { ascending: false });

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting symptom reports:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a new symptom report (wrapper for addHealthReport)
   */
  async addSymptomReport(reportData) {
    try {
      // Format the data for the health_reports table
      const healthReportData = {
        report_type: 'symptom',
        report_date: reportData.report_date || new Date(),
        symptoms: reportData.symptoms,
        symptom_severity: reportData.symptom_severity,
        activity_limitation: reportData.activity_limitation,
        sleep_disturbance: reportData.sleep_disturbance,
        medication_used: reportData.medication_used,
        trigger_factors: reportData.trigger_factors,
        notes: reportData.notes,
        weather_condition: reportData.weather_condition,
        temperature: reportData.temperature,
        humidity: reportData.humidity,
        location: reportData.location
      };

      return await this.addHealthReport(healthReportData);
    } catch (error) {
      console.error('Error adding symptom report:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete all health reports for the current user
   */
  async deleteAllHealthReports() {
    try {
      console.log('DatabaseService: Deleting all health reports');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // First check how many reports exist
      const { data: healthReports, error: countError } = await supabase
        .from('health_reports')
        .select('id')
        .eq('user_id', user.id);

      console.log(`Found ${healthReports?.length || 0} health reports to delete`);

      if (countError) {
        console.error('Error counting health reports:', countError);
      }

      // Delete all health reports for this user
      const { error } = await supabase
        .from('health_reports')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting all health reports:', error);
        throw error;
      }

      console.log('All health reports deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting all health reports:', error);
      return { success: false, error: error.message };
    }
  }

  // For backward compatibility
  async deleteAllSymptomReports() {
    return await this.deleteAllHealthReports();
  }

  // ACTION PLAN METHODS

  /**
   * Get the action plan for the current user
   */
  async getActionPlan() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Getting action plan for user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('action_plans')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "no rows returned" error

      console.log('Action plan found:', !!data);
      return {
        success: true,
        data: data || null,
        exists: !!data
      };
    } catch (error) {
      console.error('Error getting action plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create or update an action plan
   */
  async saveActionPlan(planData) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Check if plan exists
      const { exists } = await this.getActionPlan();

      let result;
      if (exists) {
        // Update existing plan
        result = await supabase
          .from('action_plans')
          .update(planData)
          .eq('user_id', user.id)
          .select();
      } else {
        // Create new plan with user_id
        const planWithUserId = {
          ...planData,
          user_id: user.id
        };

        result = await supabase
          .from('action_plans')
          .insert([planWithUserId])
          .select();
      }

      const { data, error } = result;
      if (error) throw error;

      return { success: true, data: exists ? data[0] : data[0] };
    } catch (error) {
      console.error('Error saving action plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete an action plan
   */
  async deleteActionPlan() {
    try {
      console.log('DatabaseService: Deleting action plan');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Delete the action plan for this user
      const { error } = await supabase
        .from('action_plans')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting action plan:', error);
        throw error;
      }

      console.log('Action plan deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting action plan:', error);
      return { success: false, error: error.message };
    }
  }

  // CHAT METHODS (USING UNIFIED TABLE)

  /**
   * Get all chat conversations for the current user
   */
  async getChatConversations() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Get distinct conversation IDs and their latest message time
      const { data, error } = await supabase
        .from('chat_unified')
        .select('conversation_id, conversation_title, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Get unique conversations (remove duplicates)
      const uniqueConversations = [];
      const conversationIds = new Set();

      data.forEach(item => {
        if (!conversationIds.has(item.conversation_id)) {
          conversationIds.add(item.conversation_id);
          uniqueConversations.push({
            id: item.conversation_id,
            title: item.conversation_title || 'Conversation',
            updated_at: item.created_at
          });
        }
      });

      return { success: true, data: uniqueConversations };
    } catch (error) {
      console.error('Error getting chat conversations:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new chat conversation
   */
  async createChatConversation(title = 'New Conversation') {
    try {
      // Generate a new UUID for the conversation
      const conversationId = uuidv4();

      // Return the conversation ID (no need to insert anything in the unified table)
      return {
        success: true,
        data: {
          id: conversationId,
          title: title,
          created_at: new Date(),
          updated_at: new Date()
        }
      };
    } catch (error) {
      console.error('Error creating chat conversation:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get messages for a specific conversation
   */
  async getChatMessages(conversationId) {
    try {
      const { data, error } = await supabase
        .from('chat_unified')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting chat messages:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send a message in a conversation
   */
  async sendChatMessage(messageData) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Add user_id to the message data
      const messageWithUserId = {
        ...messageData,
        user_id: user.id
      };

      const { data, error } = await supabase
        .from('chat_unified')
        .insert([messageWithUserId])
        .select();

      if (error) throw error;
      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error sending chat message:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a chat message
   */
  async deleteChatMessage(messageId) {
    try {
      console.log('DatabaseService: Deleting chat message with ID:', messageId);

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Delete the message
      const { error } = await supabase
        .from('chat_unified')
        .delete()
        .eq('id', messageId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting chat message:', error);
        throw error;
      }

      console.log('Chat message deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting chat message:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete an entire chat conversation
   */
  async deleteChatConversation(conversationId) {
    try {
      console.log('DatabaseService: Deleting chat conversation with ID:', conversationId);

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Delete all messages in the conversation
      const { error } = await supabase
        .from('chat_unified')
        .delete()
        .eq('conversation_id', conversationId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting chat conversation:', error);
        throw error;
      }

      console.log('Chat conversation deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting chat conversation:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete all chat data for the current user
   */
  async deleteAllChatData() {
    try {
      console.log('DatabaseService: Deleting all chat data');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // First check how many chat messages exist
      const { data: chatMessages, error: countError } = await supabase
        .from('chat_unified')
        .select('id')
        .eq('user_id', user.id);

      console.log(`Found ${chatMessages?.length || 0} chat messages to delete`);

      if (countError) {
        console.error('Error counting chat messages:', countError);
      }

      // Delete all chat messages for this user
      const { error } = await supabase
        .from('chat_unified')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting all chat data:', error);
        throw error;
      }

      console.log('All chat data deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting all chat data:', error);
      return { success: false, error: error.message };
    }
  }

  // NOTIFICATION METHODS

  /**
   * Get all notifications for the current user
   */
  async getNotifications() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting notifications:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get unread notifications count
   */
  async getUnreadNotificationsCount() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) throw error;
      return { success: true, count };
    } catch (error) {
      console.error('Error getting unread notifications count:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Mark a notification as read
   */
  async markNotificationAsRead(notificationId) {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsAsRead() {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId) {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('Error deleting notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete all notifications for the current user
   */
  async deleteAllNotifications() {
    try {
      console.log('DatabaseService: Deleting all notifications');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // First check how many notifications exist
      const { data: notifications, error: countError } = await supabase
        .from('notifications')
        .select('id')
        .eq('user_id', user.id);

      console.log(`Found ${notifications?.length || 0} notifications to delete`);

      if (countError) {
        console.error('Error counting notifications:', countError);
      }

      // Delete all notifications for this user
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting all notifications:', error);
        throw error;
      }

      console.log('All notifications deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting all notifications:', error);
      return { success: false, error: error.message };
    }
  }

  // WEATHER DATA METHODS (USING HEALTH_REPORTS TABLE)

  /**
   * Save weather data (wrapper for addHealthReport)
   */
  async saveWeatherData(weatherData) {
    try {
      // Format the data for the health_reports table
      const healthReportData = {
        report_type: 'weather',
        report_date: weatherData.date || new Date(),
        weather_condition: weatherData.condition,
        weather_description: weatherData.description,
        temperature: weatherData.temperature,
        humidity: weatherData.humidity,
        pressure: weatherData.pressure,
        wind_speed: weatherData.wind_speed,
        wind_direction: weatherData.wind_direction,
        air_quality_index: weatherData.air_quality_index,
        pollen_count: weatherData.pollen_count,
        pollen_types: weatherData.pollen_types,
        asthma_risk_level: weatherData.asthma_risk_level,
        location: weatherData.location
      };

      return await this.addHealthReport(healthReportData);
    } catch (error) {
      console.error('Error saving weather data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get weather data for a specific date range
   */
  async getWeatherData(startDate, endDate) {
    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      let query = supabase
        .from('health_reports')
        .select('*')
        .eq('user_id', user.id)
        .eq('report_type', 'weather');

      if (startDate) {
        query = query.gte('report_date', startDate);
      }

      if (endDate) {
        query = query.lte('report_date', endDate);
      }

      query = query.order('report_date', { ascending: true });

      const { data, error } = await query;

      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      console.error('Error getting weather data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Save weather data (wrapper for addHealthReport)
   */
  async saveWeatherData(weatherData) {
    try {
      // Format the data for the health_reports table
      const healthReportData = {
        report_type: 'weather',
        report_date: weatherData.date || new Date(),
        weather_condition: weatherData.condition || weatherData.weather_condition,
        weather_description: weatherData.description || weatherData.weather_description,
        temperature: weatherData.temperature,
        humidity: weatherData.humidity,
        pressure: weatherData.pressure,
        wind_speed: weatherData.wind_speed,
        pollen_count: weatherData.pollen_count,
        pollen_types: weatherData.pollen_types,
        asthma_risk_level: weatherData.asthma_risk_level,
        location: weatherData.location
      };

      return await this.addHealthReport(healthReportData);
    } catch (error) {
      console.error('Error saving weather data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete all user data (except profile and settings)
   * This deletes all health reports, medications, chat data, notifications, and action plans
   */
  async deleteAllUserData() {
    try {
      console.log('DatabaseService: Deleting all user data');

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user ID:', user?.id);

      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Delete all health reports
      await this.deleteAllHealthReports();

      // Delete all medications
      await this.deleteAllMedications();

      // Delete all chat data
      await this.deleteAllChatData();

      // Delete all notifications
      await this.deleteAllNotifications();

      // Delete action plan
      await this.deleteActionPlan();

      console.log('All user data deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Error deleting all user data:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new DatabaseService();
