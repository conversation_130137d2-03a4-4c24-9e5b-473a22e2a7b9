/**
 * Symptom Checkbox Component
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';

const SymptomCheckbox = ({ label, checked, onToggle, severity = 'none' }) => {
  // Get color based on severity
  const getSeverityColor = () => {
    switch (severity) {
      case 'mild':
        return COLORS.SUCCESS;
      case 'moderate':
        return COLORS.WARNING;
      case 'severe':
        return COLORS.DANGER;
      default:
        return COLORS.PRIMARY;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        checked && { borderColor: getSeverityColor() },
      ]}
      onPress={onToggle}
      activeOpacity={0.7}
    >
      <View
        style={[
          styles.checkbox,
          checked && { backgroundColor: getSeverityColor(), borderColor: getSeverityColor() },
        ]}
      >
        {checked && <Ionicons name="checkmark" size={16} color={COLORS.WHITE} />}
      </View>
      <Text style={styles.label}>{label}</Text>
      
      {checked && severity !== 'none' && (
        <View style={[styles.severityIndicator, { backgroundColor: getSeverityColor() }]}>
          <Text style={styles.severityText}>
            {severity.charAt(0).toUpperCase() + severity.slice(1)}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.small,
    borderRadius: BORDER_RADIUS.medium,
    borderWidth: 1,
    borderColor: COLORS.SOFT_HIGHLIGHT,
    backgroundColor: COLORS.WHITE,
    marginBottom: SPACING.small,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: BORDER_RADIUS.small,
    borderWidth: 2,
    borderColor: COLORS.SOFT_HIGHLIGHT,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.small,
  },
  label: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    flex: 1,
  },
  severityIndicator: {
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
    backgroundColor: COLORS.PRIMARY,
  },
  severityText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.WHITE,
    fontWeight: FONTS.WEIGHTS.medium,
  },
});

export default SymptomCheckbox;
