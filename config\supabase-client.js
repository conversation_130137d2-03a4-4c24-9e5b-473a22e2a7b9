/**
 * Official Supabase client for Smart AsthmaCare app
 * Configured for React Native environment
 */

import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import polyfills
import '../shim'; // Import all polyfills

// Supabase configuration
// Get values from global ENV object if available, otherwise use hardcoded values
let supabaseUrl, supabaseAnonKey;

// Check if global ENV object exists (created in App.js)
if (global.ENV && global.ENV.SUPABASE_URL && global.ENV.SUPABASE_ANON_KEY) {
  supabaseUrl = global.ENV.SUPABASE_URL;
  supabaseAnonKey = global.ENV.SUPABASE_ANON_KEY;
  console.log('Using Supabase credentials from global ENV object');
} else {
  // Fallback to hardcoded values
  supabaseUrl = 'https://zwinrrbdlecsbuogvzky.supabase.co';
  supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp3aW5ycmJkbGVjc2J1b2d2emt5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczMjYzNTQsImV4cCI6MjA2MjkwMjM1NH0.YdtUNNnyWpzZvzcLLfrEV6Y-q6Tpouakm0E6klALhVM';
  console.log('Could not load environment variables, using hardcoded values');
}

// Add debug logs
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Anon Key length:', supabaseAnonKey ? supabaseAnonKey.length : 0);

// Create the Supabase client with AsyncStorage for session persistence
// Use a more React Native friendly configuration
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  // Disable realtime subscriptions in React Native to avoid WebSocket issues
  realtime: {
    params: {
      eventsPerSecond: 0, // Disable realtime
    },
  },
  global: {
    // Use fetch instead of node-fetch
    fetch: fetch,
  },
  // Disable WebSocket connections
  db: {
    schema: 'public',
  },
});

// Add debug method to check if client is properly initialized
supabase.debug = {
  checkAuth: async () => {
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Auth check error:', error);
        return { success: false, error };
      }

      console.log('Auth check successful, session:', data?.session ? 'exists' : 'null');
      return { success: true, session: data?.session };
    } catch (error) {
      console.error('Unexpected error in auth check:', error);
      return { success: false, error };
    }
  }
};

// Export the Supabase client
export default supabase;
export { supabase };
