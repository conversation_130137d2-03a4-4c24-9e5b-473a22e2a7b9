/**
 * Medical Report Service
 * Provides methods for interacting with peak flow readings and medical reports in Supabase
 */

import supabase from '../config/supabase-client';
import { getCurrentUser } from './SupabaseService';

/**
 * Save peak flow reading
 * @param {Object} data - The peak flow reading data
 * @returns {Promise<Object>} The result of the operation
 */
export const savePeakFlowReading = async (data) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    const reading = {
      user_id: user.id,
      reading_value: data.value,
      reading_date: data.date || new Date().toISOString(),
      notes: data.notes || '',
      weather_condition: data.weather?.condition || '',
      temperature: data.weather?.temperature || null,
      humidity: data.weather?.humidity || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to peak_flow_readings table
    const { data: savedData, error } = await supabase
      .from('peak_flow_readings')
      .insert([reading])
      .select();

    if (error) throw error;

    return {
      success: true,
      reading: savedData[0]
    };
  } catch (error) {
    console.error('Error saving peak flow reading:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get all peak flow readings for the current user
 * @returns {Promise<Object>} The peak flow readings
 */
export const getPeakFlowReadings = async () => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Query peak flow readings for the current user
    const { data, error } = await supabase
      .from('peak_flow_readings')
      .select('*')
      .eq('user_id', user.id)
      .order('reading_date', { ascending: false });

    if (error) throw error;

    return {
      success: true,
      readings: data
    };
  } catch (error) {
    console.error('Error getting peak flow readings:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get peak flow readings for a specific date
 * @param {string} date - The date in ISO format (YYYY-MM-DD)
 * @returns {Promise<Object>} The peak flow readings for the date
 */
export const getPeakFlowReadingsByDate = async (date) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Create date range for the specified date
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);

    // Query peak flow readings for the current user and date
    const { data, error } = await supabase
      .from('peak_flow_readings')
      .select('*')
      .eq('user_id', user.id)
      .gte('reading_date', startDate.toISOString())
      .lte('reading_date', endDate.toISOString())
      .order('reading_date', { ascending: false });

    if (error) throw error;

    return {
      success: true,
      readings: data
    };
  } catch (error) {
    console.error('Error getting peak flow readings by date:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete a peak flow reading
 * @param {string} id - The reading ID
 * @returns {Promise<Object>} The result of the operation
 */
export const deletePeakFlowReading = async (id) => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Delete the reading
    const { error } = await supabase
      .from('peak_flow_readings')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error('Error deleting peak flow reading:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Get peak flow statistics for the current user
 * @returns {Promise<Object>} The peak flow statistics
 */
export const getPeakFlowStatistics = async () => {
  try {
    const { user, error: userError } = await getCurrentUser();
    if (userError) throw userError;
    if (!user) throw new Error('No user is signed in');

    // Get all readings
    const { data, error } = await supabase
      .from('peak_flow_readings')
      .select('reading_value, reading_date')
      .eq('user_id', user.id)
      .order('reading_date', { ascending: false });

    if (error) throw error;

    // Calculate statistics
    let avg = 0;
    let max = 0;
    let min = Number.MAX_SAFE_INTEGER;

    if (data.length > 0) {
      const sum = data.reduce((acc, reading) => acc + reading.reading_value, 0);
      avg = Math.round(sum / data.length);
      max = Math.max(...data.map(reading => reading.reading_value));
      min = Math.min(...data.map(reading => reading.reading_value));
    } else {
      min = 0;
    }

    return {
      success: true,
      stats: { avg, max, min, count: data.length }
    };
  } catch (error) {
    console.error('Error getting peak flow statistics:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default {
  savePeakFlowReading,
  getPeakFlowReadings,
  getPeakFlowReadingsByDate,
  deletePeakFlowReading,
  getPeakFlowStatistics
};
