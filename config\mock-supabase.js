/**
 * Mock Supabase client for Smart AsthmaCare app
 * Used as a fallback when the real Supabase client fails to initialize
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

console.log('Loading mock Supabase client');

// Create a mock Supabase client
const supabase = {
  auth: {
    // Sign up with email and password
    signUp: async (params) => {
      try {
        console.log('Mock signUp called with email:', params.email);

        // Create a mock user
        const mockUser = {
          id: `user-${Date.now()}`,
          email: params.email,
          user_metadata: params.options?.data || {}
        };

        // Store in AsyncStorage
        await AsyncStorage.setItem('mock_supabase_user', JSON.stringify(mockUser));

        return {
          data: {
            user: mockUser,
            session: {
              access_token: `mock-token-${Date.now()}`,
              refresh_token: `mock-refresh-${Date.now()}`,
              user: mockUser
            }
          },
          error: null
        };
      } catch (error) {
        console.error('Mock signUp error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Sign in with email and password
    signInWithPassword: async (params) => {
      try {
        console.log('Mock signInWithPassword called with email:', params.email);

        // For testing, accept any <NAME_EMAIL>/password
        if (params.email === '<EMAIL>' && params.password === 'password') {
          const mockUser = {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' }
          };

          // Store in AsyncStorage
          await AsyncStorage.setItem('mock_supabase_user', JSON.stringify(mockUser));

          return {
            data: {
              user: mockUser,
              session: {
                access_token: `mock-token-${Date.now()}`,
                refresh_token: `mock-refresh-${Date.now()}`,
                user: mockUser
              }
            },
            error: null
          };
        }

        // Otherwise return an error
        return {
          error: { message: 'Invalid login credentials' },
          data: null
        };
      } catch (error) {
        console.error('Mock signInWithPassword error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Sign in with Google
    signInWithIdToken: async (params) => {
      try {
        console.log('Mock signInWithIdToken called');

        // Create a mock Google user
        const mockUser = {
          id: `google-${Date.now()}`,
          email: `google-user-${Date.now()}@example.com`,
          user_metadata: {
            name: 'Google User',
            avatar_url: 'https://ui-avatars.com/api/?name=Google+User&background=4285F4&color=fff'
          }
        };

        // Store in AsyncStorage
        await AsyncStorage.setItem('mock_supabase_user', JSON.stringify(mockUser));

        return {
          data: {
            user: mockUser,
            session: {
              access_token: `mock-token-${Date.now()}`,
              refresh_token: `mock-refresh-${Date.now()}`,
              user: mockUser
            }
          },
          error: null
        };
      } catch (error) {
        console.error('Mock signInWithIdToken error:', error);
        return { error: { message: error.message }, data: null };
      }
    },

    // Sign out
    signOut: async () => {
      try {
        console.log('Mock signOut called');

        // Remove from AsyncStorage
        await AsyncStorage.removeItem('mock_supabase_user');

        return { error: null };
      } catch (error) {
        console.error('Mock signOut error:', error);
        return { error: { message: error.message } };
      }
    },

    // Get current session
    getSession: async () => {
      try {
        const userString = await AsyncStorage.getItem('mock_supabase_user');

        if (!userString) {
          return { data: { session: null }, error: null };
        }

        const user = JSON.parse(userString);

        return {
          data: {
            session: {
              access_token: `mock-token`,
              refresh_token: `mock-refresh`,
              user: user
            }
          },
          error: null
        };
      } catch (error) {
        console.error('Mock getSession error:', error);
        return { data: { session: null }, error: { message: error.message } };
      }
    },

    // Get current user
    getUser: async () => {
      try {
        const userString = await AsyncStorage.getItem('mock_supabase_user');

        if (!userString) {
          return { data: { user: null }, error: null };
        }

        const user = JSON.parse(userString);

        return { data: { user }, error: null };
      } catch (error) {
        console.error('Mock getUser error:', error);
        return { data: { user: null }, error: { message: error.message } };
      }
    },

    // Auth state change listener
    onAuthStateChange: (callback) => {
      // Call the callback once with the current session
      setTimeout(async () => {
        try {
          const userString = await AsyncStorage.getItem('mock_supabase_user');

          if (userString) {
            const user = JSON.parse(userString);
            callback('SIGNED_IN', {
              session: {
                access_token: 'mock-token',
                refresh_token: 'mock-refresh',
                user: user
              }
            });
          } else {
            callback('SIGNED_OUT', { session: null });
          }
        } catch (error) {
          console.error('Error in mock auth state change listener:', error);
        }
      }, 0);

      // Return a subscription object
      return {
        data: {
          subscription: {
            unsubscribe: () => {
              console.log('Mock auth state change listener unsubscribed');
            }
          }
        }
      };
    }
  },

  // Debug methods
  debug: {
    checkAuth: async () => {
      try {
        const userString = await AsyncStorage.getItem('mock_supabase_user');
        return {
          success: true,
          session: userString ? JSON.parse(userString) : null
        };
      } catch (error) {
        return { success: false, error };
      }
    }
  }
};

export default supabase;
export { supabase };
