/**
 * Google Sign-In Service
 * A robust implementation that works with Expo and React Native
 */

import { Platform } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GOOGLE_WEB_CLIENT_ID } from '@env';

// Constants
const STORAGE_KEY = '@GoogleSignIn:tokens';
const GOOGLE_AUTH_ENDPOINT = 'https://accounts.google.com/o/oauth2/v2/auth';
const GOOGLE_TOKEN_ENDPOINT = 'https://oauth2.googleapis.com/token';
const GOOGLE_USER_INFO_ENDPOINT = 'https://www.googleapis.com/oauth2/v3/userinfo';

// Scopes for Google Sign-In
const SCOPES = [
  'profile',
  'email',
  'openid',
];

// Generate a random string for PKCE
const generateRandomString = async (length = 32) => {
  const randomBytes = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
    { encoding: Crypto.CryptoEncoding.BASE64 }
  );
  return randomBytes.substring(0, length);
};

// Generate code challenge for PKCE
const generateCodeChallenge = async (codeVerifier) => {
  const digest = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    codeVerifier,
    { encoding: Crypto.CryptoEncoding.BASE64 }
  );
  return digest
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
};

// Store tokens in AsyncStorage
const storeTokens = async (tokens) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(tokens));
    return true;
  } catch (error) {
    console.error('Error storing Google tokens:', error);
    return false;
  }
};

// Get stored tokens from AsyncStorage
const getStoredTokens = async () => {
  try {
    const tokensString = await AsyncStorage.getItem(STORAGE_KEY);
    if (tokensString) {
      return JSON.parse(tokensString);
    }
    return null;
  } catch (error) {
    console.error('Error getting stored Google tokens:', error);
    return null;
  }
};

// Clear stored tokens
const clearTokens = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing Google tokens:', error);
    return false;
  }
};

// Exchange authorization code for tokens
const exchangeCodeForTokens = async (code, codeVerifier, redirectUri) => {
  try {
    const tokenResponse = await fetch(GOOGLE_TOKEN_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: GOOGLE_WEB_CLIENT_ID,
        grant_type: 'authorization_code',
        code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
      }).toString(),
    });

    const tokenData = await tokenResponse.json();
    
    if (tokenData.error) {
      throw new Error(`Token exchange error: ${tokenData.error}`);
    }
    
    return tokenData;
  } catch (error) {
    console.error('Error exchanging code for tokens:', error);
    throw error;
  }
};

// Get user info using access token
const getUserInfo = async (accessToken) => {
  try {
    const userInfoResponse = await fetch(GOOGLE_USER_INFO_ENDPOINT, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    const userInfo = await userInfoResponse.json();
    
    if (userInfo.error) {
      throw new Error(`User info error: ${userInfo.error}`);
    }
    
    return userInfo;
  } catch (error) {
    console.error('Error getting user info:', error);
    throw error;
  }
};

// Refresh access token using refresh token
const refreshAccessToken = async (refreshToken) => {
  try {
    const tokenResponse = await fetch(GOOGLE_TOKEN_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: GOOGLE_WEB_CLIENT_ID,
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      }).toString(),
    });

    const tokenData = await tokenResponse.json();
    
    if (tokenData.error) {
      throw new Error(`Token refresh error: ${tokenData.error}`);
    }
    
    // Update stored tokens
    const storedTokens = await getStoredTokens();
    if (storedTokens) {
      const updatedTokens = {
        ...storedTokens,
        access_token: tokenData.access_token,
        expires_at: Date.now() + tokenData.expires_in * 1000,
      };
      await storeTokens(updatedTokens);
    }
    
    return tokenData;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
};

// Check if access token is expired
const isTokenExpired = (expiresAt) => {
  return Date.now() >= expiresAt;
};

// Get valid access token (refresh if needed)
const getValidAccessToken = async () => {
  try {
    const tokens = await getStoredTokens();
    
    if (!tokens) {
      return null;
    }
    
    if (isTokenExpired(tokens.expires_at)) {
      // Token is expired, refresh it
      const refreshedTokens = await refreshAccessToken(tokens.refresh_token);
      return refreshedTokens.access_token;
    }
    
    return tokens.access_token;
  } catch (error) {
    console.error('Error getting valid access token:', error);
    return null;
  }
};

// Main sign-in function
const signIn = async () => {
  try {
    console.log('Starting Google Sign-In process...');
    
    // Generate PKCE code verifier and challenge
    const codeVerifier = await generateRandomString(64);
    const codeChallenge = await generateCodeChallenge(codeVerifier);
    
    // Create redirect URI
    const redirectUri = AuthSession.makeRedirectUri({
      useProxy: true,
      scheme: 'mhealth',
    });
    
    console.log('Redirect URI:', redirectUri);
    
    // Create authorization request
    const authUrl = new URL(GOOGLE_AUTH_ENDPOINT);
    authUrl.searchParams.append('client_id', GOOGLE_WEB_CLIENT_ID);
    authUrl.searchParams.append('redirect_uri', redirectUri);
    authUrl.searchParams.append('response_type', 'code');
    authUrl.searchParams.append('scope', SCOPES.join(' '));
    authUrl.searchParams.append('code_challenge', codeChallenge);
    authUrl.searchParams.append('code_challenge_method', 'S256');
    authUrl.searchParams.append('access_type', 'offline');
    authUrl.searchParams.append('prompt', 'consent');
    
    // Start auth session
    const result = await AuthSession.startAsync({
      authUrl: authUrl.toString(),
    });
    
    console.log('Auth session result type:', result.type);
    
    if (result.type === 'success') {
      const { code } = result.params;
      
      // Exchange code for tokens
      const tokens = await exchangeCodeForTokens(code, codeVerifier, redirectUri);
      
      // Add expiration time
      tokens.expires_at = Date.now() + tokens.expires_in * 1000;
      
      // Store tokens
      await storeTokens(tokens);
      
      // Get user info
      const userInfo = await getUserInfo(tokens.access_token);
      
      return {
        success: true,
        tokens,
        user: userInfo,
        idToken: tokens.id_token,
      };
    } else if (result.type === 'cancel') {
      return {
        success: false,
        error: 'Sign in was cancelled',
      };
    } else {
      return {
        success: false,
        error: 'Sign in failed',
      };
    }
  } catch (error) {
    console.error('Google Sign-In error:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred',
    };
  }
};

// Sign out function
const signOut = async () => {
  try {
    // Clear stored tokens
    await clearTokens();
    return { success: true };
  } catch (error) {
    console.error('Google Sign-Out error:', error);
    return {
      success: false,
      error: error.message || 'Failed to sign out',
    };
  }
};

// Check if user is signed in
const isSignedIn = async () => {
  try {
    const accessToken = await getValidAccessToken();
    return !!accessToken;
  } catch (error) {
    console.error('Error checking sign-in status:', error);
    return false;
  }
};

// Get current user info
const getCurrentUser = async () => {
  try {
    const accessToken = await getValidAccessToken();
    
    if (!accessToken) {
      return null;
    }
    
    return await getUserInfo(accessToken);
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Export the service
export default {
  signIn,
  signOut,
  isSignedIn,
  getCurrentUser,
  getValidAccessToken,
};
