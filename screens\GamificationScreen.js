/**
 * Gamification Screen
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import i18n from '../i18n/i18n';

const GamificationScreen = ({ navigation }) => {
  // Mock user data
  const userData = {
    streak: 7,
    points: 350,
    level: 3,
    nextLevel: 500,
    badges: [
      {
        id: '1',
        name: 'First Steps',
        description: 'Complete your profile and action plan',
        icon: 'footsteps',
        earned: true,
        date: '2023-05-01',
      },
      {
        id: '2',
        name: 'Consistent Tracker',
        description: 'Track your symptoms for 7 consecutive days',
        icon: 'calendar',
        earned: true,
        date: '2023-05-07',
      },
      {
        id: '3',
        name: 'Knowledge Seeker',
        description: 'Read 5 educational resources',
        icon: 'book',
        earned: true,
        date: '2023-05-10',
      },
      {
        id: '4',
        name: 'Medication Master',
        description: 'Take all medications on time for 14 days',
        icon: 'medkit',
        earned: false,
        progress: 10,
        total: 14,
      },
      {
        id: '5',
        name: 'Peak Flow Pro',
        description: 'Record your peak flow readings for 30 days',
        icon: 'pulse',
        earned: false,
        progress: 15,
        total: 30,
      },
      {
        id: '6',
        name: 'Weather Watcher',
        description: 'Check the weather forecast for 20 days',
        icon: 'cloudy',
        earned: false,
        progress: 12,
        total: 20,
      },
    ],
  };

  const renderBadge = (badge) => (
    <TouchableOpacity key={badge.id} style={styles.badgeContainer}>
      <View
        style={[
          styles.badgeIconContainer,
          badge.earned ? styles.earnedBadge : styles.unearnedBadge,
        ]}
      >
        <Ionicons
          name={badge.icon}
          size={32}
          color={badge.earned ? COLORS.WHITE : COLORS.TEXT + '50'}
        />
      </View>
      <Text
        style={[
          styles.badgeName,
          badge.earned ? styles.earnedBadgeText : styles.unearnedBadgeText,
        ]}
      >
        {badge.name}
      </Text>
      {badge.earned ? (
        <Text style={styles.badgeDate}>Earned on {badge.date}</Text>
      ) : (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${(badge.progress / badge.total) * 100}%` },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {badge.progress}/{badge.total}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('gamification.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content}>
        {/* Streak Card */}
        <Card style={styles.streakCard}>
          <View style={styles.streakHeader}>
            <Text style={styles.streakTitle}>{i18n.t('gamification.streak')}</Text>
            <View style={styles.streakDaysContainer}>
              <Text style={styles.streakDays}>{userData.streak}</Text>
              <Text style={styles.streakDaysLabel}>{i18n.t('gamification.days')}</Text>
            </View>
          </View>
          <View style={styles.streakCalendar}>
            {[...Array(7)].map((_, index) => (
              <View
                key={index}
                style={[
                  styles.streakDay,
                  index < userData.streak && styles.streakDayCompleted,
                ]}
              >
                {index < userData.streak && (
                  <Ionicons name="checkmark" size={16} color={COLORS.WHITE} />
                )}
              </View>
            ))}
          </View>
          <Text style={styles.streakMessage}>{i18n.t('gamification.keepGoing')}</Text>
        </Card>

        {/* Level Progress Card */}
        <Card style={styles.levelCard}>
          <View style={styles.levelHeader}>
            <View>
              <Text style={styles.levelTitle}>{i18n.t('gamification.level')}</Text>
              <Text style={styles.levelValue}>{userData.level}</Text>
            </View>
            <View>
              <Text style={styles.pointsTitle}>{i18n.t('gamification.points')}</Text>
              <Text style={styles.pointsValue}>{userData.points}</Text>
            </View>
          </View>
          <View style={styles.levelProgressContainer}>
            <View style={styles.levelProgressBar}>
              <View
                style={[
                  styles.levelProgressFill,
                  { width: `${(userData.points / userData.nextLevel) * 100}%` },
                ]}
              />
            </View>
            <Text style={styles.levelProgressText}>
              {userData.points}/{userData.nextLevel} points to Level {userData.level + 1}
            </Text>
          </View>
        </Card>

        {/* Badges Section */}
        <Text style={styles.sectionTitle}>{i18n.t('gamification.badges')}</Text>
        <View style={styles.badgesGrid}>
          {userData.badges.map(renderBadge)}
        </View>

        {/* Next Badge */}
        <Card style={styles.nextBadgeCard}>
          <Text style={styles.nextBadgeTitle}>{i18n.t('gamification.nextBadge')}</Text>
          <View style={styles.nextBadgeContent}>
            <View style={styles.nextBadgeIconContainer}>
              <Ionicons name="trophy" size={40} color={COLORS.PRIMARY} />
            </View>
            <View style={styles.nextBadgeInfo}>
              <Text style={styles.nextBadgeName}>Asthma Champion</Text>
              <Text style={styles.nextBadgeDescription}>
                Complete all daily tasks for 30 consecutive days
              </Text>
              <View style={styles.nextBadgeProgress}>
                <View style={styles.nextBadgeProgressBar}>
                  <View
                    style={[styles.nextBadgeProgressFill, { width: '23%' }]}
                  />
                </View>
                <Text style={styles.nextBadgeProgressText}>7/30 days</Text>
              </View>
            </View>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
  },
  streakCard: {
    marginBottom: SPACING.medium,
    backgroundColor: COLORS.WHITE,
  },
  streakHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  streakTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  streakDaysContainer: {
    alignItems: 'center',
  },
  streakDays: {
    fontSize: FONTS.SIZES.xxxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
  },
  streakDaysLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
  },
  streakCalendar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.medium,
  },
  streakDay: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.LIGHT_BG,
    alignItems: 'center',
    justifyContent: 'center',
  },
  streakDayCompleted: {
    backgroundColor: COLORS.PRIMARY,
  },
  streakMessage: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    fontWeight: FONTS.WEIGHTS.medium,
  },
  levelCard: {
    marginBottom: SPACING.medium,
  },
  levelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.medium,
  },
  levelTitle: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  levelValue: {
    fontSize: FONTS.SIZES.xxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.PRIMARY,
  },
  pointsTitle: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
    textAlign: 'right',
  },
  pointsValue: {
    fontSize: FONTS.SIZES.xxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.ACCENT,
    textAlign: 'right',
  },
  levelProgressContainer: {
    marginTop: SPACING.small,
  },
  levelProgressBar: {
    height: 10,
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: 5,
    marginBottom: SPACING.xs,
  },
  levelProgressFill: {
    height: '100%',
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 5,
  },
  levelProgressText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
  },
  badgesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.medium,
  },
  badgeContainer: {
    width: '48%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
    alignItems: 'center',
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  badgeIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.small,
  },
  earnedBadge: {
    backgroundColor: COLORS.PRIMARY,
  },
  unearnedBadge: {
    backgroundColor: COLORS.LIGHT_BG,
  },
  badgeName: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  earnedBadgeText: {
    color: COLORS.TEXT,
  },
  unearnedBadgeText: {
    color: COLORS.TEXT + '80',
  },
  badgeDate: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '70',
    textAlign: 'center',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: 3,
    marginBottom: SPACING.xs,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.SOFT_HIGHLIGHT,
    borderRadius: 3,
  },
  progressText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '70',
  },
  nextBadgeCard: {
    marginBottom: SPACING.large,
  },
  nextBadgeTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
  },
  nextBadgeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextBadgeIconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: COLORS.PRIMARY + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.medium,
  },
  nextBadgeInfo: {
    flex: 1,
  },
  nextBadgeName: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  nextBadgeDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT + '80',
    marginBottom: SPACING.small,
  },
  nextBadgeProgress: {
    width: '100%',
  },
  nextBadgeProgressBar: {
    height: 8,
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: 4,
    marginBottom: SPACING.xs,
  },
  nextBadgeProgressFill: {
    height: '100%',
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 4,
  },
  nextBadgeProgressText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT + '70',
  },
});

export default GamificationScreen;
