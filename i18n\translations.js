/**
 * Smart AsthmaCare App Translations
 * Supports English, Luganda, Ateso, and Luo
 */

export const translations = {
  // English translations
  en: {
    // Common
    appName: 'Smart AsthmaCare',
    next: 'Next',
    back: 'Back',
    skip: 'Skip',
    done: 'Done',
    cancel: 'Cancel',
    save: 'Save',
    submit: 'Submit',
    continue: 'Continue',
    loading: 'Loading...',
    search: 'Search',
    emergency: 'EMERGENCY',
    call: 'Call',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
    delete: 'Delete',
    close: 'Close',

    // Language selection
    languageSelection: 'Select Language',
    english: 'English',
    luganda: 'Luganda',
    ateso: 'Ateso',
    luo: 'Luo',
    languageConfirm: 'Confirm',

    // Onboarding
    onboarding: {
      title1: 'What is Asthma?',
      description1: 'Asthma is a condition that affects your airways – the tubes that carry air in and out of your lungs. It causes these airways to become inflamed and sensitive.',
      title2: 'How This App Helps',
      description2: 'Smart AsthmaCare helps you track your symptoms, medication, and environmental triggers. It provides personalized advice and emergency support when you need it.',
      title3: 'What to Expect',
      description3: 'Daily tracking, personalized action plans, educational resources, and emergency support - all designed to help you manage your asthma effectively.',
      getStarted: 'Get Started',
    },

    // Authentication
    auth: {
      login: 'Login',
      signup: 'Sign Up',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      forgotPassword: 'Forgot Password?',
      noAccount: 'Don\'t have an account?',
      haveAccount: 'Already have an account?',
      name: 'Full Name',
      phone: 'Phone Number',
      resetPassword: 'Reset Password',
      forgotPasswordInstructions: 'Enter your email address and we\'ll send you instructions to reset your password.',
      resetEmailSent: 'Reset Email Sent',
      resetEmailSentMessage: 'Password reset instructions have been sent to {{email}}. Please check your inbox.',
      resendEmail: 'Resend Email',
      rememberPassword: 'Remember your password?',
      backToLogin: 'Back to Login',
      createNewPassword: 'Create New Password',
      createNewPasswordInstructions: 'Please enter a new password for your account. Password must be at least 6 characters long.',
      newPassword: 'New Password',
      confirmNewPassword: 'Confirm New Password',
      passwordResetSuccess: 'Password Reset Successful',
      passwordResetSuccessMessage: 'Your password has been reset successfully. You can now log in with your new password.',
    },

    // Home Dashboard
    home: {
      welcome: 'Welcome',
      todayStatus: 'Today\'s Asthma Status',
      weatherAlert: 'Weather Alert',
      riskLevel: 'Risk Level',
      lowRisk: 'Low Risk',
      mediumRisk: 'Medium Risk',
      highRisk: 'High Risk',
      dailyTip: 'Daily Tip',
      viewActionPlan: 'View Action Plan',
      trackSymptoms: 'Track Symptoms',
      medicationReminder: 'Medication Reminder',
      educationalResources: 'Educational Resources',
      weatherTracker: 'Weather & Humidity',
      clinicDirectory: 'Clinic Directory',
      emergencySupport: 'Emergency Support',
      achievements: 'Achievements',
    },

    // Action Plan
    actionPlan: {
      title: 'Asthma Action Plan',
      personalInfo: 'Personal Information',
      medicalHistory: 'Medical History',
      triggers: 'Known Triggers',
      medications: 'Medications',
      greenZone: 'Green Zone (Doing Well)',
      yellowZone: 'Yellow Zone (Caution)',
      redZone: 'Red Zone (Medical Alert)',
      doctorInfo: 'Doctor Information',
      generatePlan: 'Generate Plan',
      updatePlan: 'Update Plan',
    },

    // Symptom Tracker
    symptoms: {
      title: 'Symptom Tracker',
      today: 'Today',
      peakFlow: 'Peak Flow Reading',
      enterValue: 'Enter Value',
      coughing: 'Coughing',
      wheezing: 'Wheezing',
      chestTightness: 'Chest Tightness',
      shortnessOfBreath: 'Shortness of Breath',
      nighttimeAwakening: 'Nighttime Awakening',
      limitedActivity: 'Limited Activity',
      notes: 'Notes',
      history: 'History',
      trends: 'Trends',
      recordedSuccessfully: 'Your symptoms have been recorded successfully!',
      viewHistory: 'View History',
      confirmDelete: 'Confirm Delete',
      deleteConfirmMessage: 'Are you sure you want to delete this report? This action cannot be undone.',
    },

    // Medication
    medication: {
      title: 'Medication Reminder',
      morning: 'Morning',
      afternoon: 'Afternoon',
      evening: 'Evening',
      taken: 'Taken',
      missed: 'Missed',
      addMedication: 'Add Medication',
      editMedication: 'Edit Medication',
      medicationName: 'Medication Name',
      dosage: 'Dosage',
      frequency: 'Frequency',
      schedule: 'Schedule',
      confirmDelete: 'Confirm Delete',
      deleteConfirmMessage: 'Are you sure you want to delete this medication? This action cannot be undone.',
      delete: 'Delete',
      // Notification related strings
      notificationPermissionTitle: 'Notification Permission Required',
      notificationPermissionMessage: 'We need permission to send you medication reminders. Please enable notifications in your device settings.',
      notificationScheduledTitle: 'Reminders Set',
      notificationScheduledMessage: 'Medication reminders have been scheduled. You will receive {{count}} notification(s) at the scheduled times.',
      notificationErrorTitle: 'Reminder Error',
      notificationErrorMessage: 'There was an error setting up your medication reminders. Please try again.',
      carryInhaler: 'Remember to carry your inhaler',
      enterNameDosage: 'Please enter medication name and dosage',
      selectTime: 'Please select at least one time for the medication',
      takenTitle: 'Taken',
      takenMessage: 'dose has been marked as taken',
      updateStatusError: 'Failed to update medication status',
      saveSuccess: 'Medication saved successfully',
      updateSuccess: 'Medication updated successfully',
      saveError: 'Failed to save medication',
      deleteSuccess: 'Medication deleted successfully',
      deleteError: 'Failed to delete medication',
    },

    // Education
    education: {
      title: 'Educational Resources',
      asthmaBasics: 'Asthma Basics',
      triggers: 'Common Triggers',
      inhalerUse: 'How to Use Your Inhaler',
      emergencyResponse: 'Emergency Response',
      livingWithAsthma: 'Living with Asthma',
      readMore: 'Read More',
    },

    // Gamification
    gamification: {
      title: 'Achievements',
      streak: 'Current Streak',
      days: 'days',
      badges: 'Badges',
      points: 'Points',
      level: 'Level',
      nextBadge: 'Next Badge',
      keepGoing: 'Keep Going!',
    },

    // Emergency
    emergency: {
      title: 'Emergency Support',
      callEmergency: 'Call Emergency Services',
      nearbyHospitals: 'Nearby Hospitals',
      firstAid: 'Asthma First Aid',
      step1: '1. Sit upright',
      step2: '2. Take 4 puffs of reliever',
      step3: '3. Wait 4 minutes',
      step4: '4. If no improvement, take 4 more puffs',
      step5: '5. If still no improvement, call emergency services',
      contactDoctor: 'Contact Your Doctor',
    },

    // Clinic Directory
    clinics: {
      title: 'Clinic Directory',
      searchPlaceholder: 'Search by name or location',
      distance: 'Distance',
      phone: 'Phone',
      directions: 'Directions',
      specializes: 'Specializes in asthma care',
      callNow: 'Call Now',
    },

    // Weather
    weather: {
      title: 'Weather & Humidity Tracker',
      currentLocation: 'Current Location',
      temperature: 'Temperature',
      humidity: 'Humidity',
      airQuality: 'Air Quality',
      forecast: 'Forecast',
      riskAssessment: 'Risk Assessment',
      highHumidity: 'High humidity detected',
      lowHumidity: 'Low humidity detected',
      carryInhaler: 'Remember to carry your inhaler',
      stayIndoors: 'Consider staying indoors',
    },

    // Notifications
    notifications: {
      title: 'Notifications',
      markAllRead: 'Mark All as Read',
      clearAll: 'Clear All',
      noNotifications: 'No notifications yet',
      medicationReminder: 'Medication Reminder',
      weatherAlert: 'Weather Alert',
      symptomTracking: 'Symptom Tracking',
      dailyTip: 'Daily Tip',
      loading: 'Loading notifications...',
      error: 'Error loading notifications',
      retry: 'Try Again',
      deleteConfirm: 'Are you sure you want to delete this notification?',
      clearConfirm: 'Are you sure you want to clear all notifications?',
      markAllReadSuccess: 'All notifications marked as read',
      clearAllSuccess: 'All notifications cleared',
    },

    // Chat
    chat: {
      title: 'AI Assistant',
      welcome: 'Hello! I am your Smart AsthmaCare Assistant. I can help you with asthma management, peak flow analysis, and medication recommendations. How can I assist you today?',
      inputPlaceholder: 'Type a message...',
      thinking: 'Assistant is typing...',
      offlineMode: 'Offline Mode - Limited functionality available',
      offlineResponse: 'I\'m currently in offline mode with limited functionality. Please check your internet connection to access all features.',
      fallbackResponse: 'I\'m not sure how to help with that. Could you try rephrasing your question?',
      askAboutAsthma: 'Tell me about asthma',
      askAboutPeakFlow: 'Analyze my peak flow',
      askAboutMedication: 'Recommend medication',
      inhalerUsage: 'To use your inhaler correctly:\n1. Shake the inhaler well\n2. Breathe out fully\n3. Put the mouthpiece in your mouth and seal your lips around it\n4. Start to breathe in slowly and press down on the inhaler\n5. Continue to breathe in slowly and deeply\n6. Hold your breath for 10 seconds, then breathe out slowly\n7. Wait at least 30 seconds before taking another puff if needed',
      commonTriggers: 'Common asthma triggers include:\n• Respiratory infections\n• Allergens (pollen, dust mites, pet dander)\n• Air pollution and irritants\n• Exercise\n• Weather changes\n• Strong emotions\n• Certain medications\n\nIdentifying and avoiding your personal triggers is an important part of asthma management.',
      emergencyAdvice: 'If you\'re experiencing a severe asthma attack:\n1. Use your rescue inhaler immediately\n2. Sit upright and try to stay calm\n3. Take one puff every 30-60 seconds, up to 10 puffs\n4. If symptoms don\'t improve or worsen, seek emergency medical help immediately\n5. Continue to use your rescue inhaler while waiting for help',
      noPeakFlowData: 'I don\'t have enough peak flow data to analyze. Please record your peak flow readings regularly in the Symptom Tracker.',
      peakFlowAnalysis: 'Based on your recent readings, your average peak flow is {{average}} L/min.',
      peakFlowGood: 'This is in the GREEN ZONE, which is good! Your airways are open and your asthma appears to be under control.',
      peakFlowCaution: 'This is in the YELLOW ZONE, which means caution. Your airways are narrowing and you may need to adjust your medication.',
      peakFlowDanger: 'This is in the RED ZONE, which requires immediate attention. Your airways are significantly narrowed and you should follow your action plan for the red zone.',
      peakFlowImproving: 'Your peak flow readings are showing improvement over time.',
      peakFlowDecreasing: 'Your peak flow readings have been decreasing, which may indicate worsening asthma control.',
      peakFlowStable: 'Your peak flow readings have been stable.',
      medicationDisclaimer: 'IMPORTANT: These recommendations are for informational purposes only and should not replace professional medical advice. Always consult with a healthcare provider before making any changes to your medication regimen.',
      medicationMild: 'Based on your peak flow readings and symptoms, you appear to have mild asthma. Common medications for mild asthma include:\n\n• A low-dose inhaled corticosteroid (ICS) as needed\n• OR a low-dose ICS-formoterol as needed\n\nYou should also have a rescue inhaler (like albuterol) available for quick relief of symptoms.',
      medicationModerate: 'Based on your peak flow readings and symptoms, you appear to have moderate asthma. Common medications for moderate asthma include:\n\n• Daily low-dose ICS-LABA (inhaled corticosteroid plus long-acting beta agonist)\n• OR medium-dose ICS\n\nYou should also have a rescue inhaler (like albuterol) available for quick relief of symptoms.',
      medicationSevere: 'Based on your peak flow readings and symptoms, you appear to have severe or poorly controlled asthma. Common medications for severe asthma include:\n\n• Medium to high-dose ICS-LABA (inhaled corticosteroid plus long-acting beta agonist)\n• Possibly add-on therapies such as long-acting muscarinic antagonists (LAMA), leukotriene receptor antagonists, or biologics\n\nYou should also have a rescue inhaler (like albuterol) available for quick relief of symptoms.\n\nIt is VERY IMPORTANT that you consult with a healthcare provider as soon as possible.',
      medicationNoData: 'I don\'t have enough data to make personalized medication recommendations. Please record your peak flow readings and symptoms regularly in the Symptom Tracker.\n\nIn general, asthma medications fall into two main categories:\n\n1. Controller medications (taken daily to prevent symptoms)\n2. Rescue medications (used during an asthma attack or before exercise)',
    },

    // Profile
    profile: {
      title: 'Profile',
      personalInfo: 'Personal Information',
      language: 'Language',
      notifications: 'Notifications',
      privacy: 'Privacy',
      logout: 'Logout',
      deleteAccount: 'Delete Account',
      editProfile: 'Edit Profile',
      languageChanged: 'Language Changed',
      languageChangedMessage: 'The app language has been updated successfully.',
      languageChangeError: 'There was an error changing the language. Please try again.',
      appLanguage: 'App Language',
      currentLanguage: 'Current language',
      changeLanguage: 'Change Language',
      selectLanguage: 'Select your preferred language',
      logoutConfirmation: 'Are you sure you want to logout?',
      deleteAccountConfirmation: 'Are you sure you want to delete your account? This action cannot be undone.',
      english: 'English',
      luganda: 'Luganda',
      ateso: 'Ateso',
      luo: 'Luo',
    },

    // Permissions
    permissions: {
      notificationTitle: 'Enable Notifications',
      notificationMessage: 'Smart AsthmaCare needs permission to send you medication reminders and important health alerts. Would you like to enable notifications?',
      notificationDeniedTitle: 'Notifications Disabled',
      notificationDeniedMessage: 'You have disabled notifications. You can enable them in your device settings to receive medication reminders.',
      locationTitle: 'Enable Location Access',
      locationMessage: 'Smart AsthmaCare needs access to your location to provide accurate weather information relevant to your asthma condition.',
      locationDeniedTitle: 'Location Access Disabled',
      locationDeniedMessage: 'You have disabled location access. You can enable it in your device settings to get accurate weather information.',
      allowAccess: 'Allow Access',
      notNow: 'Not Now',
    },
  },

  // Luganda translations (placeholders)
  lg: {
    // Common
    appName: 'Smart AsthmaCare',
    next: 'Eddako',
    back: 'Ddayo',
    skip: 'Buuka',
    done: 'Kiwedde',
    cancel: 'Sazaamu',
    save: 'Tereka',
    submit: 'Waayo',
    continue: 'Genda mu maaso',
    loading: 'Kilooda...',
    search: 'Noonya',
    emergency: 'EMBALWA',
    call: 'Koona',

    // Placeholder for other sections
    languageSelection: '[Luganda] Select Language',
    // ... other translations would go here
  },

  // Ateso translations (placeholders)
  at: {
    // Common
    appName: 'Smart AsthmaCare',
    next: 'Elosi',
    back: 'Abongun',
    skip: 'Tolot',
    done: 'Ejaasi',
    cancel: 'Igarakin',
    save: 'Itogogong',
    submit: 'Aite',
    continue: 'Ilosikinos',
    loading: 'Elosete...',
    search: 'Kijar',
    emergency: 'AOKOT',
    call: 'Koona',

    // Placeholder for other sections
    languageSelection: '[Ateso] Select Language',
    // ... other translations would go here
  },

  // Luo translations (placeholders)
  lu: {
    // Common
    appName: 'Smart AsthmaCare',
    next: 'Malubo',
    back: 'Dok cen',
    skip: 'Kal',
    done: 'Otum',
    cancel: 'Juk',
    save: 'Gwok',
    submit: 'Cwal',
    continue: 'Mede',
    loading: 'Tye ka cano...',
    search: 'Yeny',
    emergency: 'AYELA',
    call: 'Go cim',

    // Placeholder for other sections
    languageSelection: '[Luo] Select Language',
    // ... other translations would go here
  },
};

export default translations;
