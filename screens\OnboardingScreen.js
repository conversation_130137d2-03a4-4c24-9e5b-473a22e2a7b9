/**
 * Onboarding Screen
 */

import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, FlatList, Dimensions, Image, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Button from '../components/Button';
import i18n from '../i18n/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

const OnboardingScreen = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef(null);

  const onboardingData = [
    {
      id: '1',
      title: i18n.t('onboarding.title1'),
      description: i18n.t('onboarding.description1'),
      image: require('../assets/icon.png'), // Replace with actual onboarding images
      icon: 'medical',
    },
    {
      id: '2',
      title: i18n.t('onboarding.title2'),
      description: i18n.t('onboarding.description2'),
      image: require('../assets/icon.png'), // Replace with actual onboarding images
      icon: 'heart',
    },
    {
      id: '3',
      title: i18n.t('onboarding.title3'),
      description: i18n.t('onboarding.description3'),
      image: require('../assets/icon.png'), // Replace with actual onboarding images
      icon: 'checkmark-circle',
    },
  ];

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      flatListRef.current.scrollToIndex({ index: currentIndex + 1 });
      setCurrentIndex(currentIndex + 1);
    } else {
      handleGetStarted();
    }
  };

  const handleSkip = () => {
    flatListRef.current.scrollToIndex({ index: onboardingData.length - 1 });
    setCurrentIndex(onboardingData.length - 1);
  };

  const handleGetStarted = async () => {
    await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
    navigation.replace('Auth');
  };

  const renderItem = ({ item }) => {
    return (
      <View style={styles.slide}>
        <View style={styles.imageContainer}>
          <View style={styles.iconCircle}>
            <Ionicons name={item.icon} size={60} color={COLORS.PRIMARY} />
          </View>
        </View>
        <Text style={styles.title}>{item.title}</Text>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.skipContainer}>
        {currentIndex < onboardingData.length - 1 && (
          <TouchableOpacity onPress={handleSkip}>
            <Text style={styles.skipText}>{i18n.t('skip')}</Text>
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        ref={flatListRef}
        data={onboardingData}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(event.nativeEvent.contentOffset.x / width);
          setCurrentIndex(index);
        }}
      />

      <View style={styles.indicatorContainer}>
        {onboardingData.map((_, index) => (
          <View
            key={index}
            style={[
              styles.indicator,
              index === currentIndex && styles.activeIndicator,
            ]}
          />
        ))}
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={
            currentIndex === onboardingData.length - 1
              ? i18n.t('onboarding.getStarted')
              : i18n.t('next')
          }
          onPress={handleNext}
          style={styles.button}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  skipContainer: {
    alignItems: 'flex-end',
    padding: SPACING.medium,
  },
  skipText: {
    color: COLORS.PRIMARY,
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  slide: {
    width,
    padding: SPACING.large,
    alignItems: 'center',
  },
  imageContainer: {
    marginBottom: SPACING.large,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconCircle: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: COLORS.WHITE,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  title: {
    fontSize: FONTS.SIZES.xxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: SPACING.medium,
  },
  description: {
    fontSize: FONTS.SIZES.large,
    color: COLORS.TEXT,
    textAlign: 'center',
    paddingHorizontal: SPACING.medium,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: SPACING.large,
  },
  indicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.SOFT_HIGHLIGHT,
    marginHorizontal: 5,
  },
  activeIndicator: {
    backgroundColor: COLORS.PRIMARY,
    width: 20,
  },
  buttonContainer: {
    paddingHorizontal: SPACING.large,
    paddingBottom: SPACING.large,
  },
  button: {
    width: '100%',
  },
});

export default OnboardingScreen;
