/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * This configuration handles Node.js module resolution for React Native
 */

const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add specific blacklisted modules
config.resolver.blockList = [
  /node_modules\/ws\/.*/,
  /node_modules\/react-native-tcp\/.*/,
  /node_modules\/react-native-udp\/.*/
];

// Add extraNodeModules for Node.js module resolution
config.resolver.extraNodeModules = {
  // Provide empty implementations for Node.js modules
  ...config.resolver.extraNodeModules,
  net: path.resolve(__dirname, './shim.js'),
  tls: path.resolve(__dirname, './shim.js'),
  fs: path.resolve(__dirname, './shim.js'),
  path: path.resolve(__dirname, './shim.js'),
  stream: path.resolve(__dirname, './shim.js'),
  http: path.resolve(__dirname, './shim.js'),
  https: path.resolve(__dirname, './shim.js'),
  zlib: path.resolve(__dirname, './shim.js'),
  os: path.resolve(__dirname, './shim.js'),
  crypto: path.resolve(__dirname, './shim.js'),
  // Explicitly resolve domelementtype to fix import issues
  domelementtype: path.resolve(__dirname, 'node_modules/domelementtype'),
  // Add ramda module resolution
  'ramda/src': path.resolve(__dirname, 'node_modules/ramda/es'),
};

// Add additional resolver settings to help with module resolution
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Ensure symlinks are followed
config.resolver.disableHierarchicalLookup = false;
config.resolver.enableGlobalPackages = true;

module.exports = config;
