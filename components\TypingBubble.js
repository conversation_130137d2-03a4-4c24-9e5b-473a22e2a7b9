/**
 * TypingBubble Component
 * Displays text with a typing animation effect, letter by letter
 */

import React, { useState, useEffect, useRef } from 'react';
import { Text, View, StyleSheet, Animated } from 'react-native';
import { COLORS, FONTS } from '../components/theme';

const TypingBubble = ({
  text,
  style,
  textStyle,
  typingSpeed = 30,
  onTypingComplete,
  isUser = false
}) => {
  // Store the full text and the currently displayed text
  const fullText = useRef(text);
  const [displayedText, setDisplayedText] = useState(isUser ? text : '');
  const [isTyping, setIsTyping] = useState(!isUser);
  const typingInterval = useRef(null);
  const cursorOpacity = useRef(new Animated.Value(1)).current;

  // Flag to track if animation has completed
  const hasCompletedTyping = useRef(false);

  // Handle text changes
  useEffect(() => {
    // If text changes, update the full text reference
    fullText.current = text;

    // If this is a user message, show it immediately
    if (isUser) {
      setDisplayedText(text);
      setIsTyping(false);
      hasCompletedTyping.current = true;
      if (onTypingComplete) onTypingComplete();
      return;
    }

    // If we've already completed typing for this text, don't restart
    if (displayedText === text && hasCompletedTyping.current) {
      return;
    }

    // Clear any existing interval
    if (typingInterval.current) {
      clearInterval(typingInterval.current);
    }

    // Reset state for new animation
    setDisplayedText('');
    setIsTyping(true);
    hasCompletedTyping.current = false;

    // Start typing after a small delay
    const startDelay = setTimeout(() => {
      let charIndex = 0;

      // Set up interval to add one character at a time
      typingInterval.current = setInterval(() => {
        if (charIndex < text.length) {
          setDisplayedText(text.substring(0, charIndex + 1));
          charIndex++;
        } else {
          // Animation complete
          clearInterval(typingInterval.current);
          setIsTyping(false);
          hasCompletedTyping.current = true;
          if (onTypingComplete) onTypingComplete();
        }
      }, typingSpeed);
    }, 300);

    // Cleanup function
    return () => {
      clearTimeout(startDelay);
      if (typingInterval.current) {
        clearInterval(typingInterval.current);
      }
    };
  }, [text, isUser, typingSpeed, onTypingComplete]);

  // Blinking cursor animation
  useEffect(() => {
    if (isTyping) {
      // Create blinking animation
      const blinkAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(cursorOpacity, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(cursorOpacity, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      );

      blinkAnimation.start();

      return () => {
        blinkAnimation.stop();
      };
    }
  }, [isTyping, cursorOpacity]);

  return (
    <View style={[styles.container, style]}>
      <Text style={[styles.text, textStyle]}>
        {displayedText}
        {isTyping && (
          <Animated.Text style={[styles.cursor, { opacity: cursorOpacity }]}>|</Animated.Text>
        )}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 0,
  },
  text: {
    fontSize: 16,
    color: COLORS.TEXT,
    fontFamily: FONTS.REGULAR,
    lineHeight: 22,
  },
  cursor: {
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
  },
});

export default TypingBubble;
