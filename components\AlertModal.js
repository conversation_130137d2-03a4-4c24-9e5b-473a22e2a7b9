/**
 * Custom Alert Modal Component
 * A styled replacement for the standard Alert.alert()
 */

import { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Modal, 
  TouchableOpacity, 
  TouchableWithoutFeedback,
  Animated,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';

const { width } = Dimensions.get('window');

const AlertModal = ({ 
  visible, 
  title, 
  message, 
  buttons = [{ text: 'OK', onPress: () => {} }],
  type = 'info', // 'info', 'success', 'warning', 'error'
  onClose 
}) => {
  const [modalVisible, setModalVisible] = useState(visible);
  const [animation] = useState(new Animated.Value(0));

  useEffect(() => {
    setModalVisible(visible);
    if (visible) {
      Animated.spring(animation, {
        toValue: 1,
        useNativeDriver: true,
        friction: 8,
        tension: 40
      }).start();
    } else {
      Animated.timing(animation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true
      }).start();
    }
  }, [visible]);

  const handleButtonPress = (button) => {
    if (button.onPress) {
      button.onPress();
    }
    if (onClose) {
      onClose();
    }
  };

  // Get icon and color based on alert type
  const getTypeProperties = () => {
    switch (type) {
      case 'success':
        return { 
          icon: 'checkmark-circle', 
          color: COLORS.SUCCESS,
          backgroundColor: COLORS.SUCCESS + '10'
        };
      case 'warning':
        return { 
          icon: 'warning', 
          color: COLORS.WARNING,
          backgroundColor: COLORS.WARNING + '10'
        };
      case 'error':
        return { 
          icon: 'alert-circle', 
          color: COLORS.DANGER,
          backgroundColor: COLORS.DANGER + '10'
        };
      case 'info':
      default:
        return { 
          icon: 'information-circle', 
          color: COLORS.PRIMARY,
          backgroundColor: COLORS.PRIMARY + '10'
        };
    }
  };

  const { icon, color, backgroundColor } = getTypeProperties();

  const modalScale = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.9, 1]
  });

  const modalOpacity = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1]
  });

  return (
    <Modal
      transparent={true}
      visible={modalVisible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <Animated.View 
              style={[
                styles.modalContainer,
                { 
                  opacity: modalOpacity,
                  transform: [{ scale: modalScale }]
                }
              ]}
            >
              <View style={[styles.iconContainer, { backgroundColor }]}>
                <Ionicons name={icon} size={40} color={color} />
              </View>
              
              <Text style={styles.title}>{title}</Text>
              
              <Text style={styles.message}>{message}</Text>
              
              <View style={[
                styles.buttonContainer,
                buttons.length > 2 && styles.buttonContainerVertical
              ]}>
                {buttons.map((button, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.button,
                      index === buttons.length - 1 && styles.primaryButton,
                      buttons.length > 2 && styles.fullWidthButton,
                      button.style
                    ]}
                    onPress={() => handleButtonPress(button)}
                  >
                    <Text 
                      style={[
                        styles.buttonText,
                        index === buttons.length - 1 && styles.primaryButtonText,
                        button.textStyle
                      ]}
                    >
                      {button.text}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.large,
    padding: SPACING.large,
    alignItems: 'center',
    elevation: 5,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  title: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: SPACING.small,
  },
  message: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: SPACING.large,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  buttonContainerVertical: {
    flexDirection: 'column',
  },
  button: {
    flex: 1,
    paddingVertical: SPACING.medium,
    paddingHorizontal: SPACING.small,
    borderRadius: BORDER_RADIUS.medium,
    marginHorizontal: SPACING.xs,
    backgroundColor: COLORS.LIGHT_BG,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  fullWidthButton: {
    marginVertical: SPACING.xs,
    marginHorizontal: 0,
  },
  buttonText: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  primaryButtonText: {
    color: COLORS.WHITE,
  },
});

export default AlertModal;
