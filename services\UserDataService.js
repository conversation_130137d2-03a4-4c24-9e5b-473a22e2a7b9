/**
 * User Data Service
 * Handles all operations related to user data in Firestore
 */

import { 
  doc, 
  collection, 
  setDoc, 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  addDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp
} from 'firebase/firestore';
import { db, auth } from '../config/firebase';

/**
 * Save peak flow reading and symptoms
 * @param {Object} data - The peak flow and symptom data
 * @returns {Promise} - A promise that resolves with the saved data
 */
export const savePeakFlowReading = async (data) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Add timestamp
    const dataWithTimestamp = {
      ...data,
      userId: user.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Add to peak flow collection
    const docRef = await addDoc(collection(db, 'peakFlowReadings'), dataWithTimestamp);
    
    return { 
      success: true, 
      id: docRef.id,
      data: dataWithTimestamp
    };
  } catch (error) {
    console.error('Error saving peak flow reading:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Get all peak flow readings for the current user
 * @returns {Promise} - A promise that resolves with the user's peak flow readings
 */
export const getPeakFlowReadings = async () => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Query peak flow readings for the current user, ordered by date
    const q = query(
      collection(db, 'peakFlowReadings'),
      where('userId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const readings = [];

    querySnapshot.forEach((doc) => {
      readings.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return { 
      success: true, 
      readings 
    };
  } catch (error) {
    console.error('Error getting peak flow readings:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Delete a peak flow reading
 * @param {string} id - The ID of the reading to delete
 * @returns {Promise} - A promise that resolves when the reading is deleted
 */
export const deletePeakFlowReading = async (id) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Delete the document
    await deleteDoc(doc(db, 'peakFlowReadings', id));
    
    return { success: true };
  } catch (error) {
    console.error('Error deleting peak flow reading:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Save medication reminder
 * @param {Object} data - The medication reminder data
 * @returns {Promise} - A promise that resolves with the saved data
 */
export const saveMedicationReminder = async (data) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Add timestamp and user ID
    const dataWithTimestamp = {
      ...data,
      userId: user.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Add to medications collection
    const docRef = await addDoc(collection(db, 'medications'), dataWithTimestamp);
    
    return { 
      success: true, 
      id: docRef.id,
      data: dataWithTimestamp
    };
  } catch (error) {
    console.error('Error saving medication reminder:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Get all medication reminders for the current user
 * @returns {Promise} - A promise that resolves with the user's medication reminders
 */
export const getMedicationReminders = async () => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Query medications for the current user
    const q = query(
      collection(db, 'medications'),
      where('userId', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const medications = [];

    querySnapshot.forEach((doc) => {
      medications.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return { 
      success: true, 
      medications 
    };
  } catch (error) {
    console.error('Error getting medication reminders:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Update a medication reminder
 * @param {string} id - The ID of the medication to update
 * @param {Object} data - The updated medication data
 * @returns {Promise} - A promise that resolves when the medication is updated
 */
export const updateMedicationReminder = async (id, data) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Add updated timestamp
    const dataWithTimestamp = {
      ...data,
      updatedAt: serverTimestamp()
    };

    // Update the document
    await updateDoc(doc(db, 'medications', id), dataWithTimestamp);
    
    return { success: true };
  } catch (error) {
    console.error('Error updating medication reminder:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

/**
 * Delete a medication reminder
 * @param {string} id - The ID of the medication to delete
 * @returns {Promise} - A promise that resolves when the medication is deleted
 */
export const deleteMedicationReminder = async (id) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No user is signed in');
    }

    // Delete the document
    await deleteDoc(doc(db, 'medications', id));
    
    return { success: true };
  } catch (error) {
    console.error('Error deleting medication reminder:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

export default {
  savePeakFlowReading,
  getPeakFlowReadings,
  deletePeakFlowReading,
  saveMedicationReminder,
  getMedicationReminders,
  updateMedicationReminder,
  deleteMedicationReminder
};
