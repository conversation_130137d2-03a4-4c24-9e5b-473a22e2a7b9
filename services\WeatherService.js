/**
 * Weather Service
 * Handles fetching weather data from OpenWeatherMap API
 */

import axios from 'axios';
import * as Location from 'expo-location';

// OpenWeatherMap API key - Using the user-provided API key
const API_KEY = 'bf340d51cd8886119d83aa9490d77066';

// Base URL for OpenWeatherMap API
const BASE_URL = 'https://api.openweathermap.org/data/2.5';

// Cache for weather data to reduce API calls
let weatherCache = {
  current: null,
  airQuality: null,
  forecast: null,
  weekly: null,
  timestamp: null,
  coordinates: null
};

// Cache expiration time (30 minutes)
const CACHE_EXPIRATION = 30 * 60 * 1000;

/**
 * Get the user's current location
 * @returns {Promise<{latitude: number, longitude: number}>} The user's location
 */
export const getCurrentLocation = async () => {
  try {
    console.log('WeatherService: Getting current location');

    // Default location (Kampala, Uganda) if location access is denied
    const defaultLocation = {
      latitude: 0.3476,
      longitude: 32.5825,
    };

    // Check if we already have permission
    const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
    console.log('WeatherService: Location permission status:', existingStatus);

    if (existingStatus === 'granted') {
      console.log('WeatherService: Location permission already granted, getting current position');

      try {
        // Try to get last known location first (faster)
        const lastKnownLocation = await Location.getLastKnownPositionAsync();

        if (lastKnownLocation) {
          console.log('WeatherService: Using last known location:',
            lastKnownLocation.coords.latitude,
            lastKnownLocation.coords.longitude
          );

          return {
            latitude: lastKnownLocation.coords.latitude,
            longitude: lastKnownLocation.coords.longitude,
          };
        }
      } catch (lastKnownError) {
        console.log('WeatherService: Error getting last known location:', lastKnownError);
      }

      // If no last known location, get current position
      try {
        console.log('WeatherService: Getting current position');
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
          timeout: 15000 // 15 second timeout
        });

        console.log('WeatherService: Current position obtained:',
          location.coords.latitude,
          location.coords.longitude
        );

        return {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };
      } catch (currentPositionError) {
        console.error('WeatherService: Error getting current position:', currentPositionError);
        throw currentPositionError;
      }
    } else {
      console.log('WeatherService: Location permission not granted, requesting permission');

      // Request permission directly
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('WeatherService: Location permission request result:', status);

      if (status === 'granted') {
        try {
          console.log('WeatherService: Permission granted, getting current position');
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
            timeout: 15000 // 15 second timeout
          });

          console.log('WeatherService: Current position obtained after permission grant:',
            location.coords.latitude,
            location.coords.longitude
          );

          return {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
        } catch (error) {
          console.error('WeatherService: Error getting location after permission grant:', error);
          return defaultLocation;
        }
      } else {
        console.log('WeatherService: Permission denied, using default location');
        return defaultLocation;
      }
    }
  } catch (error) {
    console.error('WeatherService: Error in getCurrentLocation:', error);
    // Return default location (Kampala, Uganda) if location access is denied
    return {
      latitude: 0.3476,
      longitude: 32.5825,
    };
  }
};

/**
 * Get the current weather for a location
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 * @param {boolean} forceRefresh - Whether to force a refresh of the data
 * @returns {Promise<Object>} The weather data
 */
export const getCurrentWeather = async (latitude, longitude, forceRefresh = false) => {
  try {
    console.log('WeatherService: Getting current weather for', latitude, longitude);

    // Check if we have cached data for this location
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    const isCacheValid = weatherCache.timestamp &&
      (Date.now() - weatherCache.timestamp) < CACHE_EXPIRATION;

    // Use cached data if available and not expired
    if (!forceRefresh && isCacheValid && isSameLocation && weatherCache.current) {
      console.log('WeatherService: Using cached weather data');
      return weatherCache.current;
    }

    console.log('WeatherService: Fetching fresh weather data from OpenWeatherMap API');

    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));

    const response = await axios.get(`${BASE_URL}/weather`, {
      params: {
        lat: latitude,
        lon: longitude,
        appid: API_KEY,
        units: 'metric', // Use metric units (Celsius)
      },
      timeout: 10000, // 10 second timeout
    });

    // Update cache
    weatherCache.current = response.data;
    weatherCache.timestamp = Date.now();
    weatherCache.coordinates = { latitude, longitude };

    console.log('WeatherService: Successfully fetched weather data for', response.data.name);
    return response.data;
  } catch (error) {
    console.error('WeatherService: Error fetching weather data:', error);

    // If we have cached data for this location, return it even if expired
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    if (isSameLocation && weatherCache.current) {
      console.log('WeatherService: Returning expired cached data due to API error');
      return weatherCache.current;
    }

    throw error;
  }
};

/**
 * Get air quality text based on AQI value
 * @param {number} aqi - Air Quality Index value
 * @returns {string} Air quality description
 */
export const getAirQualityText = (aqi) => {
  if (aqi <= 50) return 'Good';
  if (aqi <= 100) return 'Moderate';
  if (aqi <= 150) return 'Unhealthy for Sensitive Groups';
  if (aqi <= 200) return 'Unhealthy';
  if (aqi <= 300) return 'Very Unhealthy';
  return 'Hazardous';
};

/**
 * Get the air quality for a location
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 * @param {boolean} forceRefresh - Whether to force a refresh of the data
 * @returns {Promise<Object>} The air quality data
 */
export const getAirQuality = async (latitude, longitude, forceRefresh = false) => {
  try {
    console.log('WeatherService: Getting air quality for', latitude, longitude);

    // Check if we have cached data for this location
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    const isCacheValid = weatherCache.timestamp &&
      (Date.now() - weatherCache.timestamp) < CACHE_EXPIRATION;

    // Use cached data if available and not expired
    if (!forceRefresh && isCacheValid && isSameLocation && weatherCache.airQuality) {
      console.log('WeatherService: Using cached air quality data');
      return weatherCache.airQuality;
    }

    console.log('WeatherService: Fetching fresh air quality data from API');

    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));

    const response = await axios.get(`${BASE_URL}/air_pollution`, {
      params: {
        lat: latitude,
        lon: longitude,
        appid: API_KEY,
      },
      timeout: 10000, // 10 second timeout
    });

    // Update cache
    weatherCache.airQuality = response.data;

    // Only update timestamp if it wasn't already updated by getCurrentWeather
    if (!weatherCache.timestamp || Math.abs(weatherCache.timestamp - Date.now()) > 1000) {
      weatherCache.timestamp = Date.now();
      weatherCache.coordinates = { latitude, longitude };
    }

    console.log('WeatherService: Successfully fetched air quality data');
    return response.data;
  } catch (error) {
    console.error('WeatherService: Error fetching air quality data:', error);

    // If we have cached data for this location, return it even if expired
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    if (isSameLocation && weatherCache.airQuality) {
      console.log('WeatherService: Returning expired cached air quality data due to API error');
      return weatherCache.airQuality;
    }

    // Return a default air quality object if no cached data is available
    return {
      list: [
        {
          main: {
            aqi: 2 // Moderate air quality as default
          },
          components: {
            co: 400,
            no: 10,
            no2: 20,
            o3: 60,
            so2: 15,
            pm2_5: 10,
            pm10: 20,
            nh3: 5
          }
        }
      ]
    };
  }
};

/**
 * Determine the asthma risk level based on weather conditions
 * @param {Object} weatherData - The weather data
 * @param {Object} airQualityData - The air quality data
 * @returns {Object} The risk assessment
 */
export const determineAsthmaRisk = (weatherData, airQualityData) => {
  // Default risk level
  let riskLevel = 'low';
  let message = 'Weather conditions are favorable for asthma management.';

  // Extract relevant data
  const temperature = weatherData.main.temp;
  const humidity = weatherData.main.humidity;
  const weatherCondition = weatherData.weather[0].main;

  // Air quality index (if available)
  const aqi = airQualityData?.list?.[0]?.main?.aqi || 1;

  // Check temperature extremes
  if (temperature < 0 || temperature > 30) {
    riskLevel = 'high';
    message = temperature < 0
      ? 'Very cold temperatures can trigger asthma symptoms. Limit outdoor activities.'
      : 'High temperatures may affect breathing. Stay hydrated and in air-conditioned environments.';
  }

  // Check humidity
  if (humidity > 60) {
    riskLevel = Math.max(riskLevel === 'high' ? 2 : 1, 1);
    message += ' High humidity can promote mold growth and dust mites.';
  } else if (humidity < 30) {
    riskLevel = Math.max(riskLevel === 'high' ? 2 : 1, 1);
    message += ' Low humidity can dry airways and trigger symptoms.';
  }

  // Check weather conditions - WeatherAPI uses different condition text format
  const rainConditions = ['Rain', 'Drizzle', 'Thunderstorm', 'Moderate rain', 'Heavy rain', 'Light rain', 'Patchy rain', 'Showers'];
  if (rainConditions.some(cond => weatherCondition.includes(cond))) {
    riskLevel = Math.max(riskLevel === 'high' ? 2 : 1, 1);
    message += ' Rainy conditions may increase mold spores.';
  }

  // Check air quality (US EPA scale: 1=Good, 2=Moderate, 3=Unhealthy for sensitive groups, etc.)
  if (aqi > 3) {
    riskLevel = 'high';
    message += ' Poor air quality detected. Consider staying indoors.';
  } else if (aqi > 2) {
    riskLevel = Math.max(riskLevel === 'high' ? 2 : 1, 1);
    message += ' Moderate air quality. Sensitive individuals should take precautions.';
  }

  // Convert numeric risk to string
  if (riskLevel === 2) riskLevel = 'high';
  if (riskLevel === 1) riskLevel = 'moderate';

  return {
    riskLevel,
    message,
    temperature: Math.round(temperature),
    humidity,
    condition: weatherCondition,
  };
};

/**
 * Get the weather forecast for a location
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 * @param {boolean} forceRefresh - Whether to force a refresh of the data
 * @returns {Promise<Object>} The forecast data
 */
export const getWeatherForecast = async (latitude, longitude, forceRefresh = false) => {
  try {
    console.log('WeatherService: Getting weather forecast for', latitude, longitude);

    // Check if we have cached data for this location
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    const isCacheValid = weatherCache.timestamp &&
      (Date.now() - weatherCache.timestamp) < CACHE_EXPIRATION;

    // Use cached data if available and not expired
    if (!forceRefresh && isCacheValid && isSameLocation && weatherCache.forecast) {
      console.log('WeatherService: Using cached forecast data');
      return weatherCache.forecast;
    }

    console.log('WeatherService: Fetching fresh forecast data from API');

    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));

    // OpenWeatherMap free API doesn't have a direct 7-day forecast endpoint
    // We'll use the 5-day/3-hour forecast and extract daily data
    const response = await axios.get(`${BASE_URL}/forecast`, {
      params: {
        lat: latitude,
        lon: longitude,
        appid: API_KEY,
        units: 'metric', // Use metric units (Celsius)
        cnt: 40, // Get 40 data points (5 days with 3-hour intervals)
      },
      timeout: 10000, // 10 second timeout
    });

    // Update cache
    weatherCache.forecast = response.data;

    // Only update timestamp if it wasn't already updated by getCurrentWeather
    if (!weatherCache.timestamp || Math.abs(weatherCache.timestamp - Date.now()) > 1000) {
      weatherCache.timestamp = Date.now();
      weatherCache.coordinates = { latitude, longitude };
    }

    console.log('WeatherService: Successfully fetched forecast data');
    return response.data;
  } catch (error) {
    console.error('WeatherService: Error fetching forecast data:', error);

    // If we have cached data for this location, return it even if expired
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    if (isSameLocation && weatherCache.forecast) {
      console.log('WeatherService: Returning expired cached forecast data due to API error');
      return weatherCache.forecast;
    }

    throw error;
  }
};

/**
 * Get the weather forecast for a location using the One Call API (if available)
 * This API provides 7-day forecast but requires paid subscription
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 * @param {boolean} forceRefresh - Whether to force a refresh of the data
 * @returns {Promise<Object>} The forecast data
 */
export const getWeeklyForecast = async (latitude, longitude, forceRefresh = false) => {
  try {
    console.log('WeatherService: Getting weekly forecast for', latitude, longitude);

    // Check if we have cached data for this location
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    const isCacheValid = weatherCache.timestamp &&
      (Date.now() - weatherCache.timestamp) < CACHE_EXPIRATION;

    // Use cached data if available and not expired
    if (!forceRefresh && isCacheValid && isSameLocation && weatherCache.weekly) {
      console.log('WeatherService: Using cached weekly forecast data');
      return weatherCache.weekly;
    }

    // If we already have forecast data from the 5-day forecast, don't try to get weekly forecast
    // This helps avoid rate limiting issues
    if (weatherCache.forecast && isSameLocation && isCacheValid) {
      console.log('WeatherService: Using 5-day forecast data instead of weekly forecast to avoid rate limiting');
      throw new Error('Using 5-day forecast instead to avoid rate limiting');
    }

    console.log('WeatherService: Fetching fresh weekly forecast data from API');

    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Note: This endpoint requires a paid subscription to OpenWeatherMap
    // We'll try to use it, but fall back to our regular forecast if it fails
    const response = await axios.get('https://api.openweathermap.org/data/2.5/onecall', {
      params: {
        lat: latitude,
        lon: longitude,
        appid: API_KEY,
        units: 'metric',
        exclude: 'minutely,hourly,alerts', // Only get daily forecast
      },
      timeout: 5000, // Add timeout to prevent long waiting
    });

    // Update cache
    weatherCache.weekly = response.data;

    // Only update timestamp if it wasn't already updated by getCurrentWeather
    if (!weatherCache.timestamp || Math.abs(weatherCache.timestamp - Date.now()) > 1000) {
      weatherCache.timestamp = Date.now();
      weatherCache.coordinates = { latitude, longitude };
    }

    console.log('WeatherService: Successfully fetched weekly forecast data');
    return response.data;
  } catch (error) {
    console.error('WeatherService: Error fetching weekly forecast data:', error);

    // If we have cached data for this location, return it even if expired
    const isSameLocation = weatherCache.coordinates &&
      Math.abs(weatherCache.coordinates.latitude - latitude) < 0.01 &&
      Math.abs(weatherCache.coordinates.longitude - longitude) < 0.01;

    if (isSameLocation && weatherCache.weekly) {
      console.log('WeatherService: Returning expired cached weekly forecast data due to API error');
      return weatherCache.weekly;
    }

    throw error;
  }
};

/**
 * Search for a city by name
 * @param {string} query - The city name to search for
 * @returns {Promise<Array>} Array of matching cities
 */
export const searchCity = async (query) => {
  try {
    console.log('WeatherService: Searching for city:', query);

    const response = await axios.get(`${BASE_URL}/find`, {
      params: {
        q: query,
        appid: API_KEY,
        type: 'like',
        sort: 'population',
        cnt: 10, // Limit to 10 results
      },
    });

    // If the API call is successful but returns no results
    if (response.data.count === 0) {
      console.log(`WeatherService: No cities found matching "${query}"`);
      return [];
    }

    // Format the results
    console.log(`WeatherService: Found ${response.data.list.length} cities matching "${query}"`);
    return response.data.list.map(city => ({
      name: city.name,
      country: city.sys.country,
      coordinates: {
        latitude: city.coord.lat,
        longitude: city.coord.lon,
      },
    }));
  } catch (error) {
    console.error('Error searching for city:', error);

    // For demo purposes, return mock data if the API fails
    if (query.toLowerCase().includes('london')) {
      return [
        { name: 'London', country: 'GB', coordinates: { latitude: 51.5074, longitude: -0.1278 } },
        { name: 'London', country: 'CA', coordinates: { latitude: 42.9849, longitude: -81.2453 } },
      ];
    } else if (query.toLowerCase().includes('new york') || query.toLowerCase().includes('newyork')) {
      return [
        { name: 'New York', country: 'US', coordinates: { latitude: 40.7128, longitude: -74.0060 } },
      ];
    } else if (query.toLowerCase().includes('tokyo')) {
      return [
        { name: 'Tokyo', country: 'JP', coordinates: { latitude: 35.6762, longitude: 139.6503 } },
      ];
    } else if (query.toLowerCase().includes('paris')) {
      return [
        { name: 'Paris', country: 'FR', coordinates: { latitude: 48.8566, longitude: 2.3522 } },
      ];
    } else if (query.length > 2) {
      // Return some mock results for any query with more than 2 characters
      return [
        { name: query, country: 'US', coordinates: { latitude: 40.7128, longitude: -74.0060 } },
        { name: `${query} City`, country: 'UK', coordinates: { latitude: 51.5074, longitude: -0.1278 } },
      ];
    }

    return [];
  }
};

export default {
  getCurrentLocation,
  getCurrentWeather,
  getAirQuality,
  getAirQualityText,
  getWeatherForecast,
  getWeeklyForecast,
  determineAsthmaRisk,
  searchCity,
};
