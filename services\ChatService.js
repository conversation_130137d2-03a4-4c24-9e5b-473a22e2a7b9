/**
 * Chat Service
 * Handles interactions with the OpenRouter API for GPT-4o integration
 */

import { OPENROUTER_API_KEY, OPENROUTER_MODEL, SYSTEM_PROMPT } from '@env';
import DatabaseService from './DatabaseService';

class ChatService {
  constructor() {
    this.apiKey = OPENROUTER_API_KEY;
    this.model = OPENROUTER_MODEL || 'openai/gpt-4o';
    this.systemPrompt = SYSTEM_PROMPT || this.getDefaultSystemPrompt();
    this.apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
  }

  /**
   * Get the default system prompt for the AI
   */
  getDefaultSystemPrompt() {
    return `You are an AI asthma management assistant in the Smart AsthmaCare app.
Your role is to help users manage their asthma by providing personalized advice based on their peak flow readings,
symptoms, medication adherence, and environmental factors.

You have access to the user's:
- Peak flow readings and symptom reports
- Medication schedule and adherence
- Weather conditions and air quality data
- Action plan

When analyzing data:
- Compare peak flow readings to the user's personal best and predicted values
- Identify trends and patterns in symptoms and triggers
- Check medication adherence and effectiveness
- Consider environmental factors like weather, pollen, and air quality

Provide clear, actionable advice that follows the user's action plan zones:
- Green Zone (80-100% of personal best): Maintain current management
- Yellow Zone (50-80% of personal best): Temporary increase in medication, monitor closely
- Red Zone (below 50% of personal best): Emergency medications, seek medical help

Always prioritize safety. If a user reports severe symptoms or very low peak flow readings,
advise them to follow their action plan's red zone instructions and seek medical attention if appropriate.

Be conversational, empathetic, and encouraging. Recognize the challenges of managing a chronic condition
and celebrate improvements and good adherence.`;
  }

  /**
   * Send a message to the AI and get a response
   */
  async sendMessage(message, conversationId, userId) {
    try {
      // Get user's medical data for context
      const userData = await this.getUserMedicalContext(userId);

      // Get previous messages in this conversation
      const { data: previousMessages } = await DatabaseService.getChatMessages(conversationId);

      // Format previous messages for the API
      const formattedPreviousMessages = previousMessages.map(msg => ({
        role: msg.is_from_user ? 'user' : 'assistant',
        content: msg.message
      }));

      // Create the messages array for the API
      const messages = [
        {
          role: 'system',
          content: this.systemPrompt + '\n\n' + userData
        },
        ...formattedPreviousMessages,
        {
          role: 'user',
          content: message
        }
      ];

      // Call the OpenRouter API
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': 'https://smartasthmacare-app.com',
          'X-Title': 'Smart AsthmaCare App'
        },
        body: JSON.stringify({
          model: this.model,
          messages: messages,
          temperature: 0.7,
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Error calling AI service');
      }

      const data = await response.json();
      const aiResponse = data.choices[0].message.content;

      // Save the AI's response to the database
      await DatabaseService.sendChatMessage({
        conversation_id: conversationId,
        message: aiResponse,
        is_from_user: false,
        message_type: 'text',
        ai_model: this.model
      });

      return {
        success: true,
        message: aiResponse
      };
    } catch (error) {
      console.error('Error in AI chat service:', error);
      return {
        success: false,
        error: error.message || 'Failed to get AI response'
      };
    }
  }

  /**
   * Get the user's medical context for the AI
   */
  async getUserMedicalContext(userId) {
    try {
      // Get user profile
      const { data: profile } = await DatabaseService.getUserProfile();

      // Get recent peak flow readings
      const { data: peakFlowReadings } = await DatabaseService.getPeakFlowReadings();
      const recentReadings = peakFlowReadings.slice(0, 10); // Get 10 most recent readings

      // Get recent symptom reports
      const { data: symptomReports } = await DatabaseService.getSymptomReports();
      const recentReports = symptomReports.slice(0, 10); // Get 10 most recent reports

      // Get medications
      const { data: medications } = await DatabaseService.getMedications();

      // Get medication logs
      const { data: medicationLogs } = await DatabaseService.getAllMedicationLogs();
      const recentLogs = medicationLogs.slice(0, 20); // Get 20 most recent logs

      // Get action plan
      const { data: actionPlan } = await DatabaseService.getActionPlan();

      // Get recent weather data
      const today = new Date();
      const oneWeekAgo = new Date(today);
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      const { data: weatherData } = await DatabaseService.getWeatherData(
        oneWeekAgo.toISOString(),
        today.toISOString()
      );

      // Format the data as a string for the AI
      let context = `USER MEDICAL CONTEXT:\n\n`;

      // Add profile information
      context += `PROFILE:\n`;
      context += `Name: ${profile?.name || 'Unknown'}\n`;
      context += `Age: ${profile?.date_of_birth ? this.calculateAge(profile.date_of_birth) : 'Unknown'}\n`;
      context += `Gender: ${profile?.gender || 'Unknown'}\n`;
      context += `Height: ${profile?.height ? profile.height + ' cm' : 'Unknown'}\n`;
      context += `Weight: ${profile?.weight ? profile.weight + ' kg' : 'Unknown'}\n`;
      context += `Medical Conditions: ${profile?.medical_conditions?.join(', ') || 'None reported'}\n`;
      context += `Allergies: ${profile?.allergies?.join(', ') || 'None reported'}\n\n`;

      // Add peak flow readings
      context += `RECENT PEAK FLOW READINGS:\n`;
      if (recentReadings.length > 0) {
        recentReadings.forEach(reading => {
          const date = new Date(reading.reading_date).toLocaleDateString();
          context += `${date}: ${reading.reading_value} L/min`;
          if (reading.zone) context += ` (${reading.zone} zone)`;
          if (reading.percent_of_personal_best) context += ` (${reading.percent_of_personal_best}% of personal best)`;
          context += `\n`;
        });
      } else {
        context += `No recent peak flow readings available.\n`;
      }
      context += `\n`;

      // Add symptom reports
      context += `RECENT SYMPTOM REPORTS:\n`;
      if (recentReports.length > 0) {
        recentReports.forEach(report => {
          const date = new Date(report.report_date).toLocaleDateString();
          context += `${date}: `;
          if (report.symptoms && report.symptoms.length > 0) {
            context += `Symptoms: ${report.symptoms.join(', ')}`;
            if (report.symptom_severity) {
              context += ` (Severity: ${JSON.stringify(report.symptom_severity)})`;
            }
          } else {
            context += `No symptoms reported`;
          }
          context += `\n`;
        });
      } else {
        context += `No recent symptom reports available.\n`;
      }
      context += `\n`;

      // Add medications
      context += `MEDICATIONS:\n`;
      if (medications.length > 0) {
        medications.forEach(med => {
          context += `${med.name} (${med.dosage || 'dosage not specified'})`;
          if (med.frequency) context += ` - ${med.frequency}`;
          if (med.medication_type) context += ` - Type: ${med.medication_type}`;
          context += `\n`;
        });
      } else {
        context += `No medications recorded.\n`;
      }
      context += `\n`;

      // Add medication adherence
      context += `RECENT MEDICATION ADHERENCE:\n`;
      if (recentLogs.length > 0) {
        const takenCount = recentLogs.filter(log => log.is_taken).length;
        const adherenceRate = (takenCount / recentLogs.length) * 100;
        context += `Adherence rate: ${adherenceRate.toFixed(1)}% (${takenCount}/${recentLogs.length} doses taken)\n`;
      } else {
        context += `No medication logs available.\n`;
      }
      context += `\n`;

      // Add action plan
      context += `ACTION PLAN:\n`;
      if (actionPlan) {
        context += `Green Zone: ${actionPlan.green_zone_peak_flow_min}-${actionPlan.green_zone_peak_flow_max} L/min\n`;
        context += `Yellow Zone: ${actionPlan.yellow_zone_peak_flow_min}-${actionPlan.yellow_zone_peak_flow_max} L/min\n`;
        context += `Red Zone: Below ${actionPlan.red_zone_peak_flow_max} L/min\n`;
      } else {
        context += `No action plan available.\n`;
      }
      context += `\n`;

      // Add recent weather
      context += `RECENT WEATHER CONDITIONS:\n`;
      if (weatherData.length > 0) {
        const latestWeather = weatherData[weatherData.length - 1];
        context += `Current: ${latestWeather.condition}, ${latestWeather.temperature}°C, ${latestWeather.humidity}% humidity`;
        if (latestWeather.air_quality_index) context += `, AQI: ${latestWeather.air_quality_index}`;
        if (latestWeather.pollen_count) context += `, Pollen: ${latestWeather.pollen_count}`;
        context += `\n`;
      } else {
        context += `No weather data available.\n`;
      }

      return context;
    } catch (error) {
      console.error('Error getting user medical context:', error);
      return 'Error retrieving user medical context. Please proceed with limited information.';
    }
  }

  /**
   * Calculate age from date of birth
   */
  calculateAge(dateOfBirth) {
    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }

    return age;
  }
}

export default new ChatService();
