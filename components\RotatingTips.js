/**
 * RotatingTips Component
 * Displays tips with a typing animation effect and auto-rotates between multiple tips
 */

import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING } from '../components/theme';
import Card from './Card';
import TypingBubble from './TypingBubble';

const { width } = Dimensions.get('window');

const RotatingTips = ({
  tips = [],
  title = 'Daily Tip',
  icon = <Ionicons name="bulb" size={24} color={COLORS.PRIMARY} />,
  autoRotateInterval = 30000, // 30 seconds by default
  style
}) => {
  const [currentTipIndex, setCurrentTipIndex] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [manualSwipe, setManualSwipe] = useState(false);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const autoRotateTimer = useRef(null);
  const tipKeys = useRef([]); // Store unique keys for each tip

  // Default tips if none are provided
  const defaultTips = [
    'Remember to rinse your mouth after using your corticosteroid inhaler to prevent thrush.',
    'Keep your rescue inhaler with you at all times, especially when exercising or traveling.',
    'Regular peak flow monitoring can help you detect asthma symptoms before they become severe.'
  ];

  // Use provided tips or default ones
  const tipsToShow = tips.length > 0 ? tips : defaultTips;

  // Generate unique keys for each tip on mount
  useEffect(() => {
    tipKeys.current = tipsToShow.map((_, index) => `tip-${index}-${Date.now()}`);
  }, []);

  // Handle auto-rotation of tips
  useEffect(() => {
    // Only start auto-rotation if typing is complete and not manually swiped recently
    if (isTypingComplete && !manualSwipe) {
      // Clear any existing timer
      if (autoRotateTimer.current) {
        clearTimeout(autoRotateTimer.current);
      }

      // Set new timer
      autoRotateTimer.current = setTimeout(() => {
        rotateTip();
      }, autoRotateInterval);
    }

    // Cleanup
    return () => {
      if (autoRotateTimer.current) {
        clearTimeout(autoRotateTimer.current);
      }
    };
  }, [currentTipIndex, isTypingComplete, manualSwipe]);

  // Handle typing completion
  const handleTypingComplete = () => {
    setIsTypingComplete(true);
  };

  // Rotate to the next tip with fade animation
  const rotateTip = () => {
    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      // Change tip
      const nextIndex = (currentTipIndex + 1) % tipsToShow.length;
      setCurrentTipIndex(nextIndex);
      setIsTypingComplete(false);

      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    });
  };

  // Handle manual navigation to previous tip
  const goToPreviousTip = () => {
    // Set manual swipe flag
    setManualSwipe(true);

    // Clear auto-rotate timer
    if (autoRotateTimer.current) {
      clearTimeout(autoRotateTimer.current);
    }

    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      // Change to previous tip
      const prevIndex = currentTipIndex === 0 ? tipsToShow.length - 1 : currentTipIndex - 1;
      setCurrentTipIndex(prevIndex);
      setIsTypingComplete(false);

      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        // Reset manual swipe after a delay
        setTimeout(() => {
          setManualSwipe(false);
        }, 5000); // Reset after 5 seconds of inactivity
      });
    });
  };

  // Handle manual navigation to next tip
  const goToNextTip = () => {
    // Set manual swipe flag
    setManualSwipe(true);

    // Clear auto-rotate timer
    if (autoRotateTimer.current) {
      clearTimeout(autoRotateTimer.current);
    }

    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      // Change to next tip
      const nextIndex = (currentTipIndex + 1) % tipsToShow.length;
      setCurrentTipIndex(nextIndex);
      setIsTypingComplete(false);

      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        // Reset manual swipe after a delay
        setTimeout(() => {
          setManualSwipe(false);
        }, 5000); // Reset after 5 seconds of inactivity
      });
    });
  };

  return (
    <Card
      title={title}
      icon={icon}
      style={[styles.card, style]}
    >
      <View style={styles.tipContainer}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={goToPreviousTip}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.PRIMARY} />
        </TouchableOpacity>

        <Animated.View style={[styles.tipContent, { opacity: fadeAnim }]}>
          <TypingBubble
            key={`tip-${currentTipIndex}-${Date.now()}`} // Force new instance on each tip change
            text={tipsToShow[currentTipIndex]}
            typingSpeed={20}
            textStyle={styles.tipText}
            onTypingComplete={handleTypingComplete}
          />

          {/* Pagination dots */}
          <View style={styles.pagination}>
            {tipsToShow.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.paginationDot,
                  index === currentTipIndex && styles.activeDot
                ]}
              />
            ))}
          </View>
        </Animated.View>

        <TouchableOpacity
          style={styles.navButton}
          onPress={goToNextTip}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-forward" size={24} color={COLORS.PRIMARY} />
        </TouchableOpacity>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: SPACING.medium,
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navButton: {
    padding: SPACING.xs,
  },
  tipContent: {
    flex: 1,
    paddingHorizontal: SPACING.small,
  },
  tipText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    lineHeight: 22,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: SPACING.small,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.LIGHT_BG,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: COLORS.PRIMARY,
    width: 12,
    height: 8,
  },
});

export default RotatingTips;
