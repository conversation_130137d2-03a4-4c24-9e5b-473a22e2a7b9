/**
 * Card Component
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';

const Card = ({
  title,
  subtitle,
  children,
  onPress,
  style,
  titleStyle,
  subtitleStyle,
  icon,
  footer,
  variant = 'default',
}) => {
  const cardContent = (
    <>
      {(title || icon) && (
        <View style={styles.header}>
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          {title && <Text style={[styles.title, titleStyle]}>{title}</Text>}
        </View>
      )}
      {subtitle && <Text style={[styles.subtitle, subtitleStyle]}>{subtitle}</Text>}
      <View style={styles.content}>{children}</View>
      {footer && <View style={styles.footer}>{footer}</View>}
    </>
  );

  // Get card style based on variant
  const getCardStyle = () => {
    switch (variant) {
      case 'outlined':
        return styles.outlinedCard;
      case 'elevated':
        return styles.elevatedCard;
      case 'flat':
        return styles.flatCard;
      default:
        return styles.defaultCard;
    }
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[styles.card, getCardStyle(), style]}
        onPress={onPress}
        activeOpacity={0.8}
      >
        {cardContent}
      </TouchableOpacity>
    );
  }

  return <View style={[styles.card, getCardStyle(), style]}>{cardContent}</View>;
};

const styles = StyleSheet.create({
  card: {
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
  },
  defaultCard: {
    backgroundColor: COLORS.WHITE,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  outlinedCard: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderColor: COLORS.SOFT_HIGHLIGHT,
  },
  elevatedCard: {
    backgroundColor: COLORS.WHITE,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  flatCard: {
    backgroundColor: COLORS.LIGHT_BG,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  iconContainer: {
    marginRight: SPACING.small,
  },
  title: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    flex: 1,
  },
  subtitle: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
    opacity: 0.8,
  },
  content: {
    marginVertical: SPACING.xs,
  },
  footer: {
    marginTop: SPACING.small,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_BG,
    paddingTop: SPACING.small,
  },
});

export default Card;
