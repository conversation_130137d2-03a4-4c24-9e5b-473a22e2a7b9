{"name": "smart-asthmacare", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build -p android --profile preview"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "expo": "~53.0.9", "expo-location": "^18.1.5", "expo-status-bar": "~2.2.3", "react": "18.2.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}