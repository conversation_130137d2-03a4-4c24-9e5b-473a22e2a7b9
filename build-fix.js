#!/usr/bin/env node

/**
 * Build Fix Script for Smart AsthmaCare App
 * This script fixes common build issues and prepares the app for EAS build
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting build fix process...');

// Function to run command safely
function runCommand(command, description) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Function to update package.json
function updatePackageJson() {
  console.log('📝 Updating package.json...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found');
    return false;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Ensure React versions are compatible
    packageJson.dependencies.react = '18.2.0';
    packageJson.dependencies['react-dom'] = '18.2.0';
    
    // Remove problematic packages if they exist
    const problematicPackages = [
      'postinstall-postinstall',
      'react-native-tcp',
      'react-native-udp',
      'react-native-websocket'
    ];
    
    problematicPackages.forEach(pkg => {
      if (packageJson.dependencies[pkg]) {
        delete packageJson.dependencies[pkg];
        console.log(`🗑️  Removed problematic package: ${pkg}`);
      }
    });
    
    // Ensure patch-package is in devDependencies
    if (!packageJson.devDependencies['patch-package']) {
      packageJson.devDependencies['patch-package'] = '^8.0.0';
    }
    
    // Ensure postinstall script exists
    if (!packageJson.scripts.postinstall) {
      packageJson.scripts.postinstall = 'patch-package';
    }
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ package.json updated successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to update package.json:', error.message);
    return false;
  }
}

// Function to check and fix app.json
function checkAppJson() {
  console.log('📝 Checking app.json...');
  
  const appJsonPath = path.join(process.cwd(), 'app.json');
  
  if (!fs.existsSync(appJsonPath)) {
    console.error('❌ app.json not found');
    return false;
  }
  
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // Ensure proper plugin configuration
    if (!appJson.expo.plugins) {
      appJson.expo.plugins = [];
    }
    
    // Check if expo-location plugin is properly configured
    const hasLocationPlugin = appJson.expo.plugins.some(plugin => 
      (Array.isArray(plugin) && plugin[0] === 'expo-location') ||
      plugin === 'expo-location'
    );
    
    if (!hasLocationPlugin) {
      appJson.expo.plugins.push([
        'expo-location',
        {
          locationAlwaysAndWhenInUsePermission: 'Allow Smart AsthmaCare to use your location to provide weather information relevant to your asthma condition.',
          locationWhenInUsePermission: 'Allow Smart AsthmaCare to use your location to provide weather information relevant to your asthma condition.'
        }
      ]);
      
      fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
      console.log('✅ app.json updated with proper plugin configuration');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to check app.json:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 Smart AsthmaCare Build Fix Script');
  console.log('=====================================');
  
  // Step 1: Update package.json
  if (!updatePackageJson()) {
    process.exit(1);
  }
  
  // Step 2: Check app.json
  if (!checkAppJson()) {
    process.exit(1);
  }
  
  // Step 3: Clean and install dependencies
  console.log('🧹 Cleaning old dependencies...');
  if (fs.existsSync('node_modules')) {
    runCommand('rm -rf node_modules', 'Removing node_modules');
  }
  if (fs.existsSync('package-lock.json')) {
    runCommand('rm package-lock.json', 'Removing package-lock.json');
  }
  
  // Step 4: Install dependencies
  if (!runCommand('npm install --legacy-peer-deps', 'Installing dependencies')) {
    console.log('⚠️  npm install failed, but continuing...');
  }
  
  // Step 5: Apply patches
  if (!runCommand('npx patch-package', 'Applying patches')) {
    console.log('⚠️  patch-package failed, but continuing...');
  }
  
  console.log('');
  console.log('✅ Build fix process completed!');
  console.log('');
  console.log('🎯 Next steps:');
  console.log('1. Run: eas build --platform android --profile preview');
  console.log('2. Monitor the build progress on expo.dev');
  console.log('3. Download the APK when ready');
  console.log('');
  console.log('📱 Your app will maintain all functionality:');
  console.log('   - Supabase Auth');
  console.log('   - AI Chatbot');
  console.log('   - Weather Integration');
  console.log('   - Location Features');
  console.log('');
  console.log('🎉 Good luck with your submission!');
}

// Run the script
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
