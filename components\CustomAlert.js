/**
 * Custom Alert Component
 * A styled alert modal that matches the app's design language
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';
import Button from './Button';

const CustomAlert = ({
  visible,
  onClose,
  title,
  message,
  type = 'info', // 'info', 'success', 'warning', 'danger'
  confirmText = 'OK',
  cancelText = 'Cancel',
  onConfirm,
  showCancel = false,
  confirmType = 'primary', // 'primary', 'outline', 'danger'
  autoClose = false,
  autoCloseTimeout = 3000,
}) => {
  // Auto-close functionality
  useEffect(() => {
    let timeoutId;
    if (visible && autoClose) {
      timeoutId = setTimeout(() => {
        onClose();
      }, autoCloseTimeout);
    }
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [visible, autoClose, autoCloseTimeout, onClose]);
  // Determine icon and color based on type
  let iconName, iconColor, iconBgColor;

  switch (type) {
    case 'success':
      iconName = 'checkmark-circle';
      iconColor = COLORS.SUCCESS;
      iconBgColor = COLORS.SUCCESS + '20';
      break;
    case 'warning':
      iconName = 'warning';
      iconColor = COLORS.WARNING;
      iconBgColor = COLORS.WARNING + '20';
      break;
    case 'danger':
      iconName = 'alert-circle';
      iconColor = COLORS.DANGER;
      iconBgColor = COLORS.DANGER + '20';
      break;
    case 'info':
    default:
      iconName = 'information-circle';
      iconColor = COLORS.PRIMARY;
      iconBgColor = COLORS.PRIMARY + '20';
      break;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={[styles.iconContainer, { backgroundColor: iconBgColor }]}>
            <Ionicons name={iconName} size={60} color={iconColor} />
          </View>

          <Text style={styles.modalTitle}>{title}</Text>
          <Text style={styles.modalDescription}>{message}</Text>

          {showCancel ? (
            <View style={styles.modalButtonsRow}>
              <Button
                title={cancelText}
                onPress={onClose}
                type="outline"
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
              <Button
                title={confirmText}
                onPress={() => {
                  if (onConfirm) {
                    onConfirm();
                  }
                  // Only close the alert if onConfirm doesn't handle it
                  // This allows onConfirm to show another alert if needed
                  if (!onConfirm) {
                    onClose();
                  }
                }}
                type={confirmType}
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
            </View>
          ) : (
            <Button
              title={confirmText}
              onPress={() => {
                if (onConfirm) {
                  onConfirm();
                }
                // Only close the alert if onConfirm doesn't handle it
                // This allows onConfirm to show another alert if needed
                if (!onConfirm) {
                  onClose();
                }
              }}
              type={confirmType}
              style={styles.modalButton}
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.large,
    padding: SPACING.large,
    alignItems: 'center',
    elevation: 5,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  modalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginVertical: SPACING.small,
  },
  modalDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: SPACING.medium,
    lineHeight: 22,
  },
  modalButton: {
    marginTop: SPACING.small,
    width: '100%',
  },
  modalButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButtonHalf: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
});

export default CustomAlert;
