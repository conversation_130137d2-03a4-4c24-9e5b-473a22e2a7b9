/**
 * React Native polyfills for Node.js modules
 */

// Polyfill for global objects
if (typeof global.self === 'undefined') {
  global.self = global;
}

if (typeof btoa === 'undefined') {
  global.btoa = function (str) {
    return Buffer.from(str, 'binary').toString('base64');
  };
}

if (typeof atob === 'undefined') {
  global.atob = function (b64Encoded) {
    return Buffer.from(b64Encoded, 'base64').toString('binary');
  };
}

// Polyfill for Math.random.seed
console.log('Adding Math.random.seed polyfill');
if (Math.random && !Math.random.seed) {
  Math.random.seed = function(s) {
    return function() {
      s = Math.sin(s) * 10000;
      return s - Math.floor(s);
    };
  };
} else if (!Math.random) {
  console.log('Math.random is not defined, creating it');
  Math.random = function() {
    return Math.sin(Date.now()) * 10000 % 1;
  };
  Math.random.seed = function(s) {
    return function() {
      s = Math.sin(s) * 10000;
      return s - Math.floor(s);
    };
  };
}

// Polyfill for process
if (typeof process === 'undefined') {
  global.process = require('process');
} else {
  const bProcess = require('process');
  for (const p in bProcess) {
    if (!(p in process)) {
      process[p] = bProcess[p];
    }
  }
}

// Polyfill for URL
global.URL = require('react-native-url-polyfill').URL;

// Polyfill for Buffer
global.Buffer = require('buffer').Buffer;

// Polyfill for crypto
console.log('Setting up crypto polyfill');
try {
  // Check if crypto already exists
  if (!global.crypto) {
    try {
      const cryptoModule = require('react-native-crypto');
      global.crypto = cryptoModule;
    } catch (cryptoImportError) {
      console.log('Could not import react-native-crypto, using fallback');
      // Create a basic crypto object if it doesn't exist
      global.crypto = {};
    }
  }

  // Ensure crypto has all required methods
  if (!global.crypto.getRandomValues) {
    console.log('Adding crypto.getRandomValues polyfill');
    global.crypto.getRandomValues = function(arr) {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    };
  }

  // Ensure crypto.subtle exists
  if (!global.crypto.subtle) {
    console.log('Adding crypto.subtle polyfill');
    global.crypto.subtle = {
      digest: async function(algorithm, data) {
        console.warn('Using mock crypto.subtle.digest');
        // Create a simple hash function
        let hash = new Uint8Array(32);
        if (data && data.length) {
          let sum = 0;
          for (let i = 0; i < data.length; i++) {
            sum += (typeof data === 'string' ? data.charCodeAt(i) : data[i]);
          }
          for (let i = 0; i < 32; i++) {
            hash[i] = (sum + i) % 256;
          }
        }
        return hash;
      }
    };
  }
} catch (error) {
  console.error('Error setting up crypto polyfill:', error);

  // Fallback implementation
  global.crypto = {
    getRandomValues: function(arr) {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
    subtle: {
      digest: async function(algorithm, data) {
        console.warn('Using fallback crypto.subtle.digest');
        // Create a simple hash function
        let hash = new Uint8Array(32);
        if (data && data.length) {
          let sum = 0;
          for (let i = 0; i < data.length; i++) {
            sum += (typeof data === 'string' ? data.charCodeAt(i) : data[i]);
          }
          for (let i = 0; i < 32; i++) {
            hash[i] = (sum + i) % 256;
          }
        }
        return hash;
      }
    }
  };
}

// Polyfill for Node.js net module (used by ws)
if (typeof global.net === 'undefined') {
  console.log('Adding net module polyfill');
  global.net = {
    connect: () => {
      console.warn('Net.connect is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    },
    createConnection: () => {
      console.warn('Net.createConnection is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    },
    Socket: class Socket {
      constructor() {
        console.warn('Net.Socket is not supported in React Native');
      }
      on() {}
      once() {}
      emit() {}
      end() {}
      destroy() {}
    }
  };
}

// Polyfill for Node.js tls module (used by ws)
if (typeof global.tls === 'undefined') {
  console.log('Adding tls module polyfill');
  global.tls = {
    connect: () => {
      console.warn('TLS.connect is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    },
    TLSSocket: class TLSSocket {
      constructor() {
        console.warn('TLS.TLSSocket is not supported in React Native');
      }
      on() {}
      once() {}
      emit() {}
      end() {}
      destroy() {}
    }
  };
}

// Polyfill for Node.js http module (used by ws)
if (typeof global.http === 'undefined') {
  console.log('Adding http module polyfill');
  global.http = {
    request: () => {
      console.warn('HTTP.request is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    },
    get: () => {
      console.warn('HTTP.get is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    }
  };
}

// Polyfill for Node.js https module (used by ws)
if (typeof global.https === 'undefined') {
  console.log('Adding https module polyfill');
  global.https = {
    request: () => {
      console.warn('HTTPS.request is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    },
    get: () => {
      console.warn('HTTPS.get is not supported in React Native');
      return {
        on: () => {},
        once: () => {},
        emit: () => {},
        end: () => {},
        destroy: () => {}
      };
    }
  };
}

// Polyfill for Node.js fs module
if (typeof global.fs === 'undefined') {
  console.log('Adding fs module polyfill');
  global.fs = {
    readFile: () => {
      console.warn('FS.readFile is not supported in React Native');
      return Promise.resolve(null);
    },
    writeFile: () => {
      console.warn('FS.writeFile is not supported in React Native');
      return Promise.resolve(null);
    }
  };
}

// Polyfill for Node.js path module
if (typeof global.path === 'undefined') {
  console.log('Adding path module polyfill');
  global.path = {
    join: (...args) => args.join('/'),
    resolve: (...args) => args.join('/')
  };
}

// Polyfill for Node.js os module
if (typeof global.os === 'undefined') {
  console.log('Adding os module polyfill');
  global.os = {
    platform: () => 'react-native',
    type: () => 'react-native',
    homedir: () => '/'
  };
}

// Polyfill for Node.js stream module
if (typeof global.stream === 'undefined') {
  console.log('Adding stream module polyfill');
  global.stream = {
    Readable: class Readable {
      constructor() {
        console.warn('Stream.Readable is not supported in React Native');
      }
      on() {}
      once() {}
      emit() {}
      pipe() {}
      destroy() {}
    },
    Writable: class Writable {
      constructor() {
        console.warn('Stream.Writable is not supported in React Native');
      }
      on() {}
      once() {}
      emit() {}
      write() {}
      end() {}
      destroy() {}
    }
  };
}

console.log('All Node.js module polyfills loaded successfully');

// Export the shim
export default {};
