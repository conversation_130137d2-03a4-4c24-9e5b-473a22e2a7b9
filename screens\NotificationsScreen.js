/**
 * Notifications Screen
 * Displays all notifications from the database
 */

import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Button from '../components/Button';
import CustomAlert from '../components/CustomAlert';
import NotificationService from '../services/NotificationService';
import i18n from '../i18n/i18n';

const NotificationsScreen = ({ navigation }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info',
  });

  // Load notifications when the screen loads
  useEffect(() => {
    loadNotifications();
  }, []);

  // Load notifications from the database
  const loadNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      const { success, data, error } = await NotificationService.getNotifications();

      if (!success) {
        throw new Error(error || 'Failed to load notifications');
      }

      setNotifications(data.map(notification => ({
        id: notification.id,
        type: notification.notification_type,
        title: notification.title,
        message: notification.message,
        time: notification.time,
        read: notification.is_read,
      })));
    } catch (err) {
      console.error('Error loading notifications:', err);
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = () => {
    setRefreshing(true);
    loadNotifications();
  };

  // Mark a notification as read
  const markAsRead = async (notificationId) => {
    try {
      const { success, error } = await NotificationService.markNotificationAsRead(notificationId);

      if (!success) {
        throw new Error(error || 'Failed to mark notification as read');
      }

      // Update local state
      setNotifications(
        notifications.map((notification) => {
          if (notification.id === notificationId) {
            return { ...notification, read: true };
          }
          return notification;
        })
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
      showError('Error', err.message);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const { success, error } = await NotificationService.markAllNotificationsAsRead();

      if (!success) {
        throw new Error(error || 'Failed to mark all notifications as read');
      }

      // Update local state
      setNotifications(
        notifications.map((notification) => ({ ...notification, read: true }))
      );

      showSuccess(i18n.t('success'), i18n.t('notifications.markAllReadSuccess'));
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      showError(i18n.t('error'), err.message);
    }
  };

  // Delete a notification
  const deleteNotification = async (notificationId) => {
    try {
      const { success, error } = await NotificationService.deleteNotification(notificationId);

      if (!success) {
        throw new Error(error || 'Failed to delete notification');
      }

      // Refresh the notifications list
      await loadNotifications();
    } catch (err) {
      console.error('Error deleting notification:', err);
      showError(i18n.t('error'), err.message);
    }
  };

  // Clear all notifications
  const clearAllNotifications = async () => {
    try {
      // Show confirmation alert
      setAlertConfig({
        title: i18n.t('notifications.clearAll'),
        message: i18n.t('notifications.clearConfirm'),
        type: 'warning',
        showCancel: true,
        confirmText: i18n.t('notifications.clearAll'),
        cancelText: i18n.t('cancel'),
        onConfirm: async () => {
          setAlertVisible(false);

          const { success, error } = await NotificationService.deleteAllNotifications();

          if (!success) {
            throw new Error(error || 'Failed to clear all notifications');
          }

          // Refresh the notifications list
          await loadNotifications();

          showSuccess(i18n.t('success'), i18n.t('notifications.clearAllSuccess'));
        }
      });
      setAlertVisible(true);
    } catch (err) {
      console.error('Error clearing all notifications:', err);
      showError(i18n.t('error'), err.message);
    }
  };

  // Show success alert
  const showSuccess = (title, message) => {
    setAlertConfig({
      title,
      message,
      type: 'success',
      showCancel: false,
      confirmText: i18n.t('ok'),
    });
    setAlertVisible(true);
  };

  // Show error alert
  const showError = (title, message) => {
    setAlertConfig({
      title,
      message,
      type: 'danger',
      showCancel: false,
      confirmText: i18n.t('ok'),
    });
    setAlertVisible(true);
  };

  // Get icon for notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'medication':
        return <Ionicons name="medkit" size={24} color={COLORS.PRIMARY} />;
      case 'weather':
        return <Ionicons name="cloud" size={24} color={COLORS.INFO} />;
      case 'tip':
        return <Ionicons name="bulb" size={24} color={COLORS.WARNING} />;
      case 'symptom':
        return <Ionicons name="pulse" size={24} color={COLORS.DANGER} />;
      default:
        return <Ionicons name="notifications" size={24} color={COLORS.PRIMARY} />;
    }
  };

  // Handle notification press
  const handleNotificationPress = async (notification) => {
    // Mark as read
    await markAsRead(notification.id);

    // Navigate based on notification type
    switch (notification.type) {
      case 'medication':
        navigation.navigate('Medication');
        break;
      case 'weather':
        navigation.navigate('Weather');
        break;
      case 'symptom':
        navigation.navigate('SymptomTracker');
        break;
      default:
        // Just mark as read for other types
        break;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('notifications.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightIcon={<Ionicons name="person-circle" size={24} color={COLORS.WHITE} />}
        onRightPress={() => navigation.navigate('Profile')}
      />

      {/* Custom Alert */}
      <CustomAlert
        visible={alertVisible}
        onClose={() => setAlertVisible(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
        confirmText={alertConfig.confirmText}
        cancelText={alertConfig.cancelText}
        onConfirm={alertConfig.onConfirm}
        showCancel={alertConfig.showCancel}
      />

      {/* Mark All Read button below header */}
      {!loading && notifications.length > 0 && (
        <TouchableOpacity onPress={markAllAsRead} style={styles.markAllReadButton}>
          <Text style={styles.markAllReadText}>{i18n.t('notifications.markAllRead')}</Text>
        </TouchableOpacity>
      )}

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.PRIMARY]}
            tintColor={COLORS.PRIMARY}
          />
        }
      >
        {loading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>{i18n.t('notifications.loading')}</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={60} color={COLORS.DANGER} />
            <Text style={styles.errorText}>{error}</Text>
            <Button
              title={i18n.t('notifications.retry')}
              onPress={loadNotifications}
              style={styles.retryButton}
            />
          </View>
        ) : notifications.length > 0 ? (
          <>
            {notifications.map((notification) => (
              <TouchableOpacity
                key={notification.id}
                style={[
                  styles.notificationItem,
                  !notification.read && styles.unreadNotification,
                ]}
                onPress={() => handleNotificationPress(notification)}
              >
                <View style={styles.notificationIcon}>
                  {getNotificationIcon(notification.type)}
                </View>
                <View style={styles.notificationContent}>
                  <Text style={styles.notificationTitle}>{notification.title}</Text>
                  <Text style={styles.notificationMessage}>{notification.message}</Text>
                  <Text style={styles.notificationTime}>{notification.time}</Text>
                </View>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => deleteNotification(notification.id)}
                >
                  <Ionicons name="close-circle" size={20} color={COLORS.TEXT_LIGHT} />
                </TouchableOpacity>
              </TouchableOpacity>
            ))}

            <Button
              title={i18n.t('notifications.clearAll')}
              onPress={clearAllNotifications}
              type="outline"
              style={styles.clearButton}
            />
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="notifications-off" size={80} color={COLORS.LIGHT_BG} />
            <Text style={styles.emptyText}>{i18n.t('notifications.noNotifications')}</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
  },
  contentContainer: {
    paddingBottom: 130, // Increased padding at the bottom (100 + 30 extra)
    minHeight: '100%', // Ensure content fills the screen for pull-to-refresh
  },
  markAllReadButton: {
    alignSelf: 'flex-end',
    paddingHorizontal: SPACING.medium,
    paddingVertical: SPACING.small,
    marginRight: SPACING.medium,
    marginTop: SPACING.small,
  },
  markAllReadText: {
    color: COLORS.PRIMARY,
    fontSize: FONTS.SIZES.small,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.medium,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  unreadNotification: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY,
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_BG,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.medium,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  notificationTime: {
    fontSize: FONTS.SIZES.xs,
    color: COLORS.TEXT_LIGHT,
  },
  deleteButton: {
    padding: SPACING.xs,
  },
  clearButton: {
    marginTop: SPACING.medium,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl * 2,
  },
  emptyText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT_LIGHT,
    marginTop: SPACING.medium,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl * 2,
  },
  loadingText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT_LIGHT,
    marginTop: SPACING.medium,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl * 2,
  },
  errorText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.DANGER,
    marginTop: SPACING.medium,
    marginBottom: SPACING.medium,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: SPACING.medium,
  },
});

export default NotificationsScreen;
