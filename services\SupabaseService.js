/**
 * Supabase Service
 * Provides methods for interacting with Supabase database
 */

import supabase from '../config/supabase-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Get the current session
 * @returns {Promise<Object>} The current session
 */
export const getSession = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error getting session:', error);
    return { data: null, error };
  }
};

/**
 * Get the current user
 * @returns {Promise<Object>} The current user
 */
export const getCurrentUser = async () => {
  try {
    const { data: sessionData } = await getSession();
    return {
      user: sessionData?.session?.user || null,
      error: null
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { user: null, error };
  }
};

/**
 * Create or update user profile
 * @param {Object} user - The user object
 * @param {Object} profileData - Additional profile data
 * @returns {Promise<Object>} The result of the operation
 */
export const createOrUpdateProfile = async (user, profileData = {}) => {
  try {
    if (!user) throw new Error('No user provided');

    const profile = {
      id: user.id,
      email: user.email,
      name: profileData.name || user.user_metadata?.name || user.email.split('@')[0],
      phone: profileData.phone || user.user_metadata?.phone || '',
      photo_url: profileData.photoURL || user.user_metadata?.avatar_url || '',
      updated_at: new Date().toISOString()
    };

    // Check if profile exists
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (existingProfile) {
      // Update existing profile
      const { error } = await supabase
        .from('profiles')
        .update(profile)
        .eq('id', user.id);

      if (error) throw error;
    } else {
      // Create new profile
      profile.created_at = new Date().toISOString();
      const { error } = await supabase
        .from('profiles')
        .insert([profile]);

      if (error) throw error;
    }

    // Cache the profile
    await AsyncStorage.setItem('userProfile', JSON.stringify(profile));

    return { success: true, profile };
  } catch (error) {
    console.error('Error creating/updating profile:', error);
    return { success: false, error };
  }
};

/**
 * Get user profile
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} The user profile
 */
export const getUserProfile = async (userId) => {
  try {
    if (!userId) {
      const { user } = await getCurrentUser();
      if (!user) throw new Error('No user is signed in');
      userId = user.id;
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) throw error;

    // Cache the profile
    if (data) {
      await AsyncStorage.setItem('userProfile', JSON.stringify(data));
    }

    return { profile: data, error: null };
  } catch (error) {
    console.error('Error getting user profile:', error);
    return { profile: null, error };
  }
};

export default {
  getSession,
  getCurrentUser,
  createOrUpdateProfile,
  getUserProfile
};
