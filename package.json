{"name": "smart-asthmacare", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "migrate": "node scripts/run-migration.js", "build:android": "eas build --platform android --profile preview"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/realtime-js": "^2.11.7", "@supabase/supabase-js": "^2.49.4", "@tradle/react-native-http": "^2.0.1", "axios": "^1.9.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "console-browserify": "^1.2.0", "constants-browserify": "^1.0.0", "date-fns": "^4.1.0", "dns.js": "^1.0.1", "dom-serializer": "^2.0.0", "domain-browser": "^5.7.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "entities": "^6.0.0", "expo": "~53.0.9", "expo-av": "^15.0.0", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "^7.1.4", "expo-localization": "^16.1.5", "expo-location": "^18.1.5", "expo-notifications": "^0.31.2", "expo-status-bar": "~2.2.3", "firebase": "^11.7.1", "https-browserify": "^1.0.0", "i18n-js": "^4.5.1", "path-browserify": "^1.0.1", "process": "^0.11.10", "react": "18.2.0", "react-native": "0.72.6", "react-native-crypto": "^2.2.0", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-chat": "^2.4.0", "react-native-level-fs": "^3.0.1", "react-native-os": "^1.2.6", "react-native-pager-view": "^6.7.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-tab-view": "^4.1.0", "react-native-tcp": "^4.0.0", "react-native-udp": "^4.1.7", "react-native-url-polyfill": "^2.0.0", "react-native-websocket": "^1.0.2", "readable-stream": "^4.7.0", "vm-browserify": "^1.1.2"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.4.5", "@types/react": "~18.2.14", "@types/react-native": "~0.72.6"}, "private": true}