import './shim'; // Import polyfills first
import React, { useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as Notifications from 'expo-notifications';
import { View, Text, ActivityIndicator, AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppNavigator from './navigation/AppNavigator';
import { COLORS } from './components/theme';
import NotificationService from './services/NotificationService';
import AlertProvider from './components/AlertProvider';
import { AuthProvider } from './context/SupabaseAuthContext';

// Import Supabase client directly
import supabaseClient, { supabase } from './config/supabase-client';

export default function App() {
  // Reference to the navigation object
  const navigationRef = useRef(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);
  const appState = useRef(AppState.currentState);

  // Initialize app
  useEffect(() => {
    console.log('App initialization started');

    // Set up app state change listener
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');
      }
      appState.current = nextAppState;
    });

    // Enhanced initialization with debugging
    const initApp = async () => {
      try {
        console.log('Initializing app...');

        // Define hardcoded fallback values
        const ENV_FALLBACKS = {
          SUPABASE_URL: 'https://zwinrrbdlecsbuogvzky.supabase.co',
          SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp3aW5ycmJkbGVjc2J1b2d2emt5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczMjYzNTQsImV4cCI6MjA2MjkwMjM1NH0.YdtUNNnyWpzZvzcLLfrEV6Y-q6Tpouakm0E6klALhVM',
          GOOGLE_WEB_CLIENT_ID: '968403173867-8sjbb0o2209djig0dfb74k50jf4bo8g3.apps.googleusercontent.com'
        };

        console.log('Fallback environment values:');
        console.log('- SUPABASE_URL:', ENV_FALLBACKS.SUPABASE_URL);
        console.log('- SUPABASE_ANON_KEY length:', ENV_FALLBACKS.SUPABASE_ANON_KEY.length);

        // Create a global ENV object that will be accessible throughout the app
        global.ENV = {};

        // Try to load environment variables, fall back to hardcoded values if needed
        try {
          const envModule = require('@env');

          // Check if the module loaded properly
          if (envModule) {
            console.log('Environment module loaded successfully');

            // Copy all environment variables to our global ENV object
            Object.keys(ENV_FALLBACKS).forEach(key => {
              global.ENV[key] = envModule[key] || ENV_FALLBACKS[key];
            });

            console.log('Environment variables loaded:');
            console.log('- SUPABASE_URL:', global.ENV.SUPABASE_URL);
            console.log('- SUPABASE_ANON_KEY length:', global.ENV.SUPABASE_ANON_KEY.length);
            console.log('- GOOGLE_WEB_CLIENT_ID:', global.ENV.GOOGLE_WEB_CLIENT_ID);
          } else {
            console.warn('Environment module is empty, using fallbacks');
            global.ENV = { ...ENV_FALLBACKS };
          }
        } catch (envError) {
          console.error('Error accessing environment variables:', envError);
          console.log('Using fallback environment values');
          global.ENV = { ...ENV_FALLBACKS };
        }

        // Google Sign-In is now handled by GoogleSignInService

        // Import and test Supabase connection
        try {
          console.log('Initializing Supabase with URL:', global.ENV.SUPABASE_URL);

          // Import the Supabase client directly - no dynamic imports
          // The import is already at the top of the file
          // This ensures the Supabase client is properly initialized

          // We'll just log that we're using the Supabase client
          console.log('Using Supabase client with hardcoded credentials');

          // No need to test the connection here, it will be tested in the auth context
        } catch (supabaseError) {
          console.error('Error in Supabase initialization:', supabaseError);
          console.log('Will use fallback Supabase client');
        }

        // Set app as ready
        setIsReady(true);
      } catch (error) {
        console.error('Unexpected error during initialization:', error);
        setError(error.message || 'An unexpected error occurred');
        setIsReady(true); // Continue anyway
      }
    };

    // Start initialization
    initApp();

    // Clean up function
    return () => {
      console.log('Cleaning up App initialization');
      subscription.remove();
    };
  }, []);

  // Set up notification handler
  useEffect(() => {
    if (!isReady) return;

    // Request notification permissions
    NotificationService.requestNotificationPermissions();

    // Schedule symptom tracking reminder only if it hasn't been scheduled before
    const setupNotifications = async () => {
      try {
        // Check if symptom tracking reminder has already been scheduled
        const hasScheduledReminder = await AsyncStorage.getItem('has_scheduled_symptom_reminder');

        if (!hasScheduledReminder) {
          // Schedule symptom tracking reminder
          await NotificationService.scheduleSymptomTrackingReminder();
          console.log('Symptom tracking reminder scheduled');

          // Mark that we've scheduled the reminder
          await AsyncStorage.setItem('has_scheduled_symptom_reminder', 'true');
        } else {
          console.log('Symptom tracking reminder already scheduled');
        }
      } catch (error) {
        console.error('Error scheduling symptom tracking reminder:', error);
      }
    };

    setupNotifications();

    // Set up notification received handler (when app is in foreground)
    const receivedSubscription = Notifications.addNotificationReceivedListener(() => {
      // Log notification received
      console.log('Notification received in foreground');
    });

    // Set up notification response handler (when user taps on notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      // Get the data from the notification
      const data = response.notification.request.content.data;

      // Navigate based on notification type
      if (data && data.medicationId) {
        // Navigate to the medication screen
        if (navigationRef.current) {
          navigationRef.current.navigate('Medication');
        }
      } else if (data && data.type === 'weather') {
        // Navigate to the weather screen
        if (navigationRef.current) {
          navigationRef.current.navigate('Weather');
        }
      } else if (data && data.type === 'symptom') {
        // Navigate to the symptom tracker screen
        if (navigationRef.current) {
          navigationRef.current.navigate('SymptomTracker');
        }
      }
    });

    // Clean up the subscriptions when the component unmounts
    return () => {
      receivedSubscription.remove();
      responseSubscription.remove();
    };
  }, [isReady]);

  // Show loading screen while app initializes
  if (!isReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: COLORS.LIGHT_BG }}>
        <ActivityIndicator size="large" color={COLORS.PRIMARY} />
        <Text style={{ marginTop: 20, color: COLORS.TEXT, fontSize: 16 }}>Initializing app...</Text>
      </View>
    );
  }

  // Show error screen if there was an error
  if (error) {
    console.log('Showing error screen, but continuing with app initialization');
    // Just log the error but continue with the app
    // This is a simplified approach since we're not using Auth
    setError(null);
  }

  // Render the app
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <AlertProvider>
          <StatusBar style="light" backgroundColor={COLORS.PRIMARY} />
          <AppNavigator ref={navigationRef} />
          {/* Error banner will be shown by the AppNavigator if needed */}
        </AlertProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
