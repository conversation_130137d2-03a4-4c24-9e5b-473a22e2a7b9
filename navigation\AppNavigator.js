/**
 * App Navigation
 */

import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../context/SupabaseAuthContext';

// Screens
import LanguageSelectScreen from '../screens/LanguageSelectScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import AuthScreen from '../screens/AuthScreen';
import HomeScreen from '../screens/HomeScreen';
import ActionPlanScreen from '../screens/ActionPlanScreen';
import SymptomTrackerScreen from '../screens/SymptomTrackerScreen';
import MedicationScreen from '../screens/MedicationScreen';
import ChatScreen from '../screens/ChatScreen';
import GamificationScreen from '../screens/GamificationScreen';
import EmergencyScreen from '../screens/EmergencyScreen';
import ClinicDirectoryScreen from '../screens/ClinicDirectoryScreen';
import WeatherScreen from '../screens/WeatherScreen';
import ProfileScreen from '../screens/ProfileScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import EducationScreen from '../screens/EducationScreen';

// Theme
import { COLORS } from '../components/theme';

// i18n
import { initLanguage } from '../i18n/i18n';

const Stack = createStackNavigator();
const Tab = createMaterialTopTabNavigator();

// Custom Tab Bar Component
const CustomTabBar = ({ state, descriptors, navigation }) => {
  return (
    <View style={tabStyles.tabBarContainer}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = options.tabBarLabel || options.title || route.name;
        const isFocused = state.index === index;

        // Get the icon name based on the route
        let iconName;
        if (route.name === 'Home') {
          iconName = isFocused ? 'home' : 'home-outline';
        } else if (route.name === 'Weather') {
          iconName = isFocused ? 'cloud' : 'cloud-outline';
        } else if (route.name === 'Chat') {
          iconName = isFocused ? 'chatbubbles' : 'chatbubbles-outline';
        } else if (route.name === 'Notifications') {
          iconName = isFocused ? 'notifications' : 'notifications-outline';
        }

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            style={tabStyles.tabButton}
          >
            <Ionicons
              name={iconName}
              size={28}
              color={isFocused ? COLORS.PRIMARY : COLORS.TEXT}
            />
            <Text
              style={[
                tabStyles.tabLabel,
                { color: isFocused ? COLORS.PRIMARY : COLORS.TEXT }
              ]}
            >
              {label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

// Main Tab Navigator
const MainTabNavigator = () => {
  return (
    <View style={{ flex: 1 }}>
      <Tab.Navigator
        tabBar={props => <CustomTabBar {...props} />}
        screenOptions={{
          tabBarShowLabel: true,
          tabBarIndicatorStyle: { opacity: 0 }, // Hide the default indicator
          swipeEnabled: true,
          animationEnabled: true,
          lazy: true,
          // Add bottom padding to content to prevent tab bar from hiding content
          tabBarStyle: { paddingBottom: 70 },
          // Add bottom margin to screen content to prevent tab bar from hiding content
          contentContainerStyle: { paddingBottom: 80 },
        }}
        style={{ flex: 1 }}
      >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ tabBarLabel: 'Home' }}
      />
      <Tab.Screen
        name="Weather"
        component={WeatherScreen}
        options={{ tabBarLabel: 'Weather' }}
      />
      <Tab.Screen
        name="Chat"
        component={ChatScreen}
        options={{ tabBarLabel: 'AI Chat' }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{ tabBarLabel: 'Notifications' }}
      />
    </Tab.Navigator>
    </View>
  );
};

// Main App Navigator
const AppNavigator = React.forwardRef((props, ref) => {
  const [isLoading, setIsLoading] = useState(true);
  const [initialRoute, setInitialRoute] = useState('LanguageSelect');
  const { currentUser, loading: authLoading, error: authError, isFallbackMode } = useAuth();

  // Log auth errors but don't block the app
  useEffect(() => {
    if (authError) {
      console.error('Authentication error:', authError);
    }

    if (isFallbackMode) {
      console.warn('Running in fallback mode (no authentication)');
    }
  }, [authError, isFallbackMode]);

  useEffect(() => {
    // Initialize app
    const initApp = async () => {
      try {
        // Initialize language first to ensure correct translations
        await initLanguage();

        // Check onboarding status
        const hasSelectedLanguage = await AsyncStorage.getItem('hasSelectedLanguage');
        const hasCompletedOnboarding = await AsyncStorage.getItem('hasCompletedOnboarding');

        if (hasSelectedLanguage === 'true') {
          if (hasCompletedOnboarding === 'true') {
            // Auth route will be determined by auth state
            setInitialRoute('Auth');
          } else {
            setInitialRoute('Onboarding');
          }
        } else {
          setInitialRoute('LanguageSelect');
        }
      } catch (error) {
        console.error('Error initializing app:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initApp();
  }, []);

  if (isLoading || authLoading) {
    return null; // Or a loading screen
  }

  // Determine if user needs to go through auth flow
  // If there's an auth error, we'll still allow navigation but won't enforce authentication
  console.log('Auth state:', { authError, currentUser, initialRoute });

  // Check if we should bypass auth (for testing)
  const bypassAuth = false; // Set to true to bypass authentication for testing

  // Normal auth check logic
  const needsAuth = !bypassAuth && !authError && !currentUser && initialRoute === 'Auth';

  return (
    <NavigationContainer ref={ref}>
      {isFallbackMode && (
        <View style={{
          backgroundColor: '#FFC107',
          padding: 5,
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
        }}>
          <Ionicons name="warning" size={16} color="#000" />
          <Text style={{
            color: '#000',
            marginLeft: 5,
            fontSize: 12,
            fontWeight: '500'
          }}>
            Demo Mode: Authentication unavailable
          </Text>
        </View>
      )}
      <Stack.Navigator
        initialRouteName={
          // If auth error, skip auth flow and go to initial route or Main
          authError ? (initialRoute === 'Auth' ? 'Main' : initialRoute) :
          // Otherwise, normal flow: Auth if needed, Main if logged in, or initial route
          (needsAuth ? 'Auth' : (currentUser ? 'Main' : initialRoute))
        }
        screenOptions={{ headerShown: false }}
      >
        <Stack.Screen name="LanguageSelect" component={LanguageSelectScreen} />
        <Stack.Screen name="Onboarding" component={OnboardingScreen} />
        <Stack.Screen
          name="Auth"
          component={AuthScreen}
          options={{
            // Prevent going back to onboarding after logging in
            gestureEnabled: false
          }}
        />
        <Stack.Screen
          name="Main"
          component={MainTabNavigator}
          options={{
            // Prevent going back to auth after logging in
            gestureEnabled: false
          }}
        />
        <Stack.Screen name="ActionPlan" component={ActionPlanScreen} />
        <Stack.Screen name="SymptomTracker" component={SymptomTrackerScreen} />
        <Stack.Screen name="Medication" component={MedicationScreen} />
        <Stack.Screen name="Gamification" component={GamificationScreen} />
        <Stack.Screen name="Emergency" component={EmergencyScreen} />
        <Stack.Screen name="ClinicDirectory" component={ClinicDirectoryScreen} />
        <Stack.Screen name="Weather" component={WeatherScreen} />
        <Stack.Screen name="Profile" component={ProfileScreen} />
        <Stack.Screen name="ChatScreen" component={ChatScreen} />
        <Stack.Screen name="Learn" component={EducationScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
});



// Styles for the custom tab bar
const tabStyles = StyleSheet.create({
  tabBarContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    height: 70,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_BG,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  tabLabel: {
    fontSize: 14,
    marginTop: 4,
    fontWeight: '500',
  },
});

export default AppNavigator;
