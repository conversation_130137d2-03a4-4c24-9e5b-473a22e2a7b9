/**
 * Mock Google Sign-In Service
 * A completely standalone implementation that doesn't rely on any external packages
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Constants
const STORAGE_KEY = '@MockGoogleSignIn:user';

// Mock user data
const MOCK_USER = {
  id: 'google-user-123456',
  email: '<EMAIL>',
  name: 'Google User',
  picture: 'https://ui-avatars.com/api/?name=Google+User&background=4285F4&color=fff',
  provider: 'google'
};

// Store user in AsyncStorage
const storeUser = async (user) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(user));
    return true;
  } catch (error) {
    console.error('Error storing mock Google user:', error);
    return false;
  }
};

// Get stored user from AsyncStorage
const getStoredUser = async () => {
  try {
    const userString = await AsyncStorage.getItem(STORAGE_KEY);
    if (userString) {
      return JSON.parse(userString);
    }
    return null;
  } catch (error) {
    console.error('Error getting stored mock Google user:', error);
    return null;
  }
};

// Clear stored user
const clearUser = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing mock Google user:', error);
    return false;
  }
};

// Generate a mock JWT token
const generateMockToken = () => {
  try {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    };

    const payload = {
      sub: MOCK_USER.id,
      name: MOCK_USER.name,
      email: MOCK_USER.email,
      picture: MOCK_USER.picture,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    };

    // Base64 encode the header and payload
    // Use a safer approach for React Native
    const base64Header = Buffer.from(JSON.stringify(header)).toString('base64');
    const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');

    // Generate a random signature
    const signature = Math.random().toString(36).substring(2);

    // Combine to form a JWT
    return `${base64Header}.${base64Payload}.${signature}`;
  } catch (error) {
    console.error('Error generating mock token:', error);
    // Fallback to a simple string token
    return `mock-token-${Date.now()}`;
  }
};

// Main sign-in function
const signIn = async () => {
  try {
    console.log('Starting mock Google Sign-In process...');

    // Store mock user
    await storeUser(MOCK_USER);

    // Generate a mock ID token
    const mockIdToken = generateMockToken();

    // Create a more detailed user object for Supabase
    const supabaseUser = {
      id: MOCK_USER.id,
      email: MOCK_USER.email,
      name: MOCK_USER.name,
      picture: MOCK_USER.picture,
      user_metadata: {
        full_name: MOCK_USER.name,
        avatar_url: MOCK_USER.picture,
        email: MOCK_USER.email,
        provider: 'google'
      }
    };

    return {
      success: true,
      user: supabaseUser,
      idToken: mockIdToken,
      tokens: {
        id_token: mockIdToken,
        access_token: `mock-access-token-${Date.now()}`,
        refresh_token: `mock-refresh-token-${Date.now()}`,
        expires_at: Date.now() + 3600 * 1000
      }
    };
  } catch (error) {
    console.error('Mock Google Sign-In error:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};

// Sign out function
const signOut = async () => {
  try {
    // Clear stored user
    await clearUser();
    return { success: true };
  } catch (error) {
    console.error('Mock Google Sign-Out error:', error);
    return {
      success: false,
      error: error.message || 'Failed to sign out'
    };
  }
};

// Check if user is signed in
const isSignedIn = async () => {
  try {
    const user = await getStoredUser();
    return !!user;
  } catch (error) {
    console.error('Error checking mock sign-in status:', error);
    return false;
  }
};

// Get current user info
const getCurrentUser = async () => {
  try {
    return await getStoredUser();
  } catch (error) {
    console.error('Error getting mock current user:', error);
    return null;
  }
};

// Export the service
export default {
  signIn,
  signOut,
  isSignedIn,
  getCurrentUser
};
