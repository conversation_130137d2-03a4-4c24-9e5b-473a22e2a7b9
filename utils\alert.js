/**
 * Custom Alert Utility
 * Provides a styled alternative to the standard Alert.alert()
 */

import React from 'react';
import { Alert as RNAlert } from 'react-native';

// Global state to track if an alert is currently showing
let isAlertShowing = false;
let alertQueue = [];

// Reference to the alert modal component
let alertRef = null;

// Set the alert reference
export const setAlertRef = (ref) => {
  alertRef = ref;
};

/**
 * Show a styled alert
 * @param {string} title - Alert title
 * @param {string} message - Alert message
 * @param {Array} buttons - Array of button objects { text, onPress, style }
 * @param {string} type - Alert type: 'info', 'success', 'warning', 'error'
 */
export const showAlert = (title, message, buttons = [{ text: 'OK' }], type = 'info') => {
  // If the alert modal is available, use it
  if (alertRef && alertRef.current) {
    // If an alert is already showing, queue this one
    if (isAlertShowing) {
      alertQueue.push({ title, message, buttons, type });
      return;
    }

    isAlertShowing = true;
    
    // Process button presses to handle the queue
    const processedButtons = buttons.map(button => ({
      ...button,
      onPress: () => {
        // Call the original onPress if it exists
        if (button.onPress) {
          button.onPress();
        }
        
        // Set a small delay before showing the next alert
        setTimeout(() => {
          isAlertShowing = false;
          
          // Show the next alert in the queue if there is one
          if (alertQueue.length > 0) {
            const nextAlert = alertQueue.shift();
            showAlert(nextAlert.title, nextAlert.message, nextAlert.buttons, nextAlert.type);
          }
        }, 300);
      }
    }));

    // Show the alert using the modal
    alertRef.current.show({
      visible: true,
      title,
      message,
      buttons: processedButtons,
      type,
    });
  } else {
    // Fallback to standard Alert if the modal is not available
    RNAlert.alert(title, message, buttons);
  }
};

/**
 * Show a success alert
 */
export const showSuccess = (title, message, buttons) => {
  showAlert(title, message, buttons, 'success');
};

/**
 * Show an error alert
 */
export const showError = (title, message, buttons) => {
  showAlert(title, message, buttons, 'error');
};

/**
 * Show a warning alert
 */
export const showWarning = (title, message, buttons) => {
  showAlert(title, message, buttons, 'warning');
};

/**
 * Show an info alert
 */
export const showInfo = (title, message, buttons) => {
  showAlert(title, message, buttons, 'info');
};

export default {
  showAlert,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  setAlertRef,
};
