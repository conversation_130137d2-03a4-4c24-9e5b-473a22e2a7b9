/**
 * Notification Service for Smart AsthmaCare App
 * Handles scheduling and managing all types of notifications:
 * - Medication reminders
 * - Weather alerts
 * - Symptom tracking reminders
 * - General notifications
 */

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import AsyncStorage from '@react-native-async-storage/async-storage';
import supabase from '../config/supabase-client';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowBanner: true,
    shouldShowList: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Storage keys for notification IDs
const NOTIFICATION_IDS_STORAGE_KEY = 'mhealth_notification_ids';
const WEATHER_NOTIFICATION_ID_KEY = 'mhealth_weather_notification_id';
const SYMPTOM_NOTIFICATION_ID_KEY = 'mhealth_symptom_notification_id';

/**
 * Play custom notification sound
 * Note: This is a placeholder function since we're using the system's default notification sound
 * @returns {Promise<void>}
 */
export const playNotificationSound = async () => {
  try {
    // We're using the system's default notification sound
    // This function is kept for API compatibility
    console.log('Using system default notification sound');
  } catch (error) {
    console.error('Error playing notification sound:', error);
  }
};

/**
 * Request notification permissions
 * @returns {Promise<boolean>} Whether permissions were granted
 */
export const requestNotificationPermissions = async (showAlerts = true) => {
  if (!Device.isDevice) {
    console.log('Notifications are not supported in the simulator');
    return false;
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;

  if (existingStatus !== 'granted') {
    if (showAlerts) {
      // Import required modules
      const CustomAlert = require('../components/CustomAlert').default;
      const i18n = require('../i18n/i18n').default;

      // Create a function to show the alert
      // This needs to be used in a component with state
      // So we'll return a function that the component can use
      return {
        showPermissionAlert: (setAlertConfig, setAlertVisible) => {
          return new Promise((resolve) => {
            setAlertConfig({
              title: i18n.t('permissions.notificationTitle'),
              message: i18n.t('permissions.notificationMessage'),
              type: 'info',
              showCancel: true,
              confirmText: i18n.t('permissions.allowAccess'),
              cancelText: i18n.t('permissions.notNow'),
              onConfirm: async () => {
                setAlertVisible(false);

                const { status } = await Notifications.requestPermissionsAsync();
                finalStatus = status;

                // No need to request audio permissions as we're using the system's default sound

                if (finalStatus !== 'granted') {
                  // Show denied message
                  setTimeout(() => {
                    setAlertConfig({
                      title: i18n.t('permissions.notificationDeniedTitle'),
                      message: i18n.t('permissions.notificationDeniedMessage'),
                      type: 'warning',
                      showCancel: false,
                    });
                    setAlertVisible(true);
                  }, 500);
                }

                resolve(finalStatus === 'granted');
              },
              onClose: () => {
                resolve(false);
              }
            });
            setAlertVisible(true);
          });
        }
      };
    } else {
      // Just request permissions without alerts
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;

      // No need to request audio permissions as we're using the system's default sound
    }
  }

  return finalStatus === 'granted';
};

/**
 * Schedule a medication reminder notification
 * @param {Object} medication - The medication object
 * @param {string} timeOfDay - 'morning', 'afternoon', or 'evening'
 * @returns {Promise<string>} The notification ID
 */
export const scheduleMedicationReminder = async (medication, timeOfDay) => {
  if (!medication.schedule[timeOfDay]) {
    return null;
  }

  const hasPermissions = await requestNotificationPermissions();
  if (!hasPermissions) {
    return null;
  }

  // Parse the time string (HH:MM)
  const [hours, minutes] = medication.schedule[timeOfDay].time.split(':').map(Number);

  // Create a Date object for the next occurrence of this time
  const scheduledTime = new Date();
  scheduledTime.setHours(hours, minutes, 0, 0);

  // If the time has already passed today, schedule for tomorrow
  if (scheduledTime <= new Date()) {
    scheduledTime.setDate(scheduledTime.getDate() + 1);
  }

  // Create a human-readable time string
  const timeString = scheduledTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  // Schedule the notification with exact timing
  const notificationId = await Notifications.scheduleNotificationAsync({
    content: {
      title: `Time to take ${medication.name}`,
      body: `${medication.dosage} - ${timeOfDay} dose at ${timeString}`,
      data: { medicationId: medication.id, timeOfDay },
      sound: true, // Use system default sound
    },
    trigger: {
      hour: hours,
      minute: minutes,
      second: 0, // Set seconds to 0 for exact timing
      repeats: true,
    },
  });

  // Store the notification ID for later management
  await storeNotificationId(medication.id, timeOfDay, notificationId);

  // Also create a database notification
  const title = `Time to take ${medication.name}`;
  const message = `${medication.dosage} - ${timeOfDay} dose at ${timeString}`;

  try {
    await createDatabaseNotification({
      title,
      message,
      notification_type: 'medication',
      is_read: false,
      is_dismissed: false,
      related_entity_id: medication.id,
      related_entity_type: 'medication',
      scheduled_for: scheduledTime.toISOString(),
    });
  } catch (error) {
    console.error('Error creating database notification:', error);
    // Continue even if database notification creation fails
  }

  return notificationId;
};

/**
 * Schedule all reminders for a medication
 * @param {Object} medication - The medication object
 * @returns {Promise<Object>} Object with notification IDs by time of day
 */
export const scheduleAllReminders = async (medication) => {
  const result = {};

  for (const timeOfDay of ['morning', 'afternoon', 'evening']) {
    if (medication.schedule[timeOfDay]) {
      const notificationId = await scheduleMedicationReminder(medication, timeOfDay);
      if (notificationId) {
        result[timeOfDay] = notificationId;
      }
    }
  }

  return result;
};

/**
 * Cancel a medication reminder notification
 * @param {string} medicationId - The medication ID
 * @param {string} timeOfDay - 'morning', 'afternoon', or 'evening'
 * @returns {Promise<boolean>} Whether the cancellation was successful
 */
export const cancelMedicationReminder = async (medicationId, timeOfDay) => {
  const notificationId = await getNotificationId(medicationId, timeOfDay);

  if (!notificationId) {
    return false;
  }

  await Notifications.cancelScheduledNotificationAsync(notificationId);
  await removeNotificationId(medicationId, timeOfDay);

  return true;
};

/**
 * Cancel all reminders for a medication
 * @param {string} medicationId - The medication ID
 * @returns {Promise<boolean>} Whether the cancellation was successful
 */
export const cancelAllReminders = async (medicationId) => {
  let success = true;

  for (const timeOfDay of ['morning', 'afternoon', 'evening']) {
    const result = await cancelMedicationReminder(medicationId, timeOfDay);
    if (!result) {
      success = false;
    }
  }

  return success;
};

/**
 * Store a notification ID in AsyncStorage
 * @param {string} medicationId - The medication ID
 * @param {string} timeOfDay - 'morning', 'afternoon', or 'evening'
 * @param {string} notificationId - The notification ID
 * @returns {Promise<void>}
 */
const storeNotificationId = async (medicationId, timeOfDay, notificationId) => {
  try {
    // Get existing notification IDs
    const storedIdsJson = await AsyncStorage.getItem(NOTIFICATION_IDS_STORAGE_KEY);
    const storedIds = storedIdsJson ? JSON.parse(storedIdsJson) : {};

    // Update with new notification ID
    if (!storedIds[medicationId]) {
      storedIds[medicationId] = {};
    }

    storedIds[medicationId][timeOfDay] = notificationId;

    // Save back to AsyncStorage
    await AsyncStorage.setItem(NOTIFICATION_IDS_STORAGE_KEY, JSON.stringify(storedIds));
  } catch (error) {
    console.error('Error storing notification ID:', error);
  }
};

/**
 * Get a notification ID from AsyncStorage
 * @param {string} medicationId - The medication ID
 * @param {string} timeOfDay - 'morning', 'afternoon', or 'evening'
 * @returns {Promise<string|null>} The notification ID or null
 */
const getNotificationId = async (medicationId, timeOfDay) => {
  try {
    const storedIdsJson = await AsyncStorage.getItem(NOTIFICATION_IDS_STORAGE_KEY);
    const storedIds = storedIdsJson ? JSON.parse(storedIdsJson) : {};

    return storedIds[medicationId]?.[timeOfDay] || null;
  } catch (error) {
    console.error('Error getting notification ID:', error);
    return null;
  }
};

/**
 * Remove a notification ID from AsyncStorage
 * @param {string} medicationId - The medication ID
 * @param {string} timeOfDay - 'morning', 'afternoon', or 'evening'
 * @returns {Promise<void>}
 */
const removeNotificationId = async (medicationId, timeOfDay) => {
  try {
    const storedIdsJson = await AsyncStorage.getItem(NOTIFICATION_IDS_STORAGE_KEY);
    const storedIds = storedIdsJson ? JSON.parse(storedIdsJson) : {};

    if (storedIds[medicationId] && storedIds[medicationId][timeOfDay]) {
      delete storedIds[medicationId][timeOfDay];

      // If no more times of day for this medication, remove the medication entry
      if (Object.keys(storedIds[medicationId]).length === 0) {
        delete storedIds[medicationId];
      }

      await AsyncStorage.setItem(NOTIFICATION_IDS_STORAGE_KEY, JSON.stringify(storedIds));
    }
  } catch (error) {
    console.error('Error removing notification ID:', error);
  }
};

/**
 * Create a notification in the database
 * @param {Object} notificationData - The notification data
 * @returns {Promise<Object>} The created notification
 */
export const createDatabaseNotification = async (notificationData) => {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Add user_id to the notification data
    const notificationWithUserId = {
      ...notificationData,
      user_id: user.id,
      created_at: new Date().toISOString(),
    };

    // Insert into the database
    const { data, error } = await supabase
      .from('notifications')
      .insert([notificationWithUserId])
      .select();

    if (error) throw error;
    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error creating notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all notifications for the current user
 * @param {boolean} includeRead - Whether to include read notifications
 * @returns {Promise<Object>} The notifications
 */
export const getNotifications = async (includeRead = true) => {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Query the database
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_dismissed', false)
      .order('created_at', { ascending: false });

    // Filter out read notifications if requested
    if (!includeRead) {
      query = query.eq('is_read', false);
    }

    const { data, error } = await query;

    if (error) throw error;

    // Format the time for each notification
    const formattedNotifications = data.map(notification => {
      // Simple time formatting function
      const formatTimeAgo = (dateString) => {
        const now = new Date();
        const date = new Date(dateString);
        const seconds = Math.floor((now - date) / 1000);

        let interval = Math.floor(seconds / 31536000); // years
        if (interval > 1) return `${interval} years ago`;
        if (interval === 1) return 'a year ago';

        interval = Math.floor(seconds / 2592000); // months
        if (interval > 1) return `${interval} months ago`;
        if (interval === 1) return 'a month ago';

        interval = Math.floor(seconds / 86400); // days
        if (interval > 1) return `${interval} days ago`;
        if (interval === 1) return 'yesterday';

        interval = Math.floor(seconds / 3600); // hours
        if (interval > 1) return `${interval} hours ago`;
        if (interval === 1) return 'an hour ago';

        interval = Math.floor(seconds / 60); // minutes
        if (interval > 1) return `${interval} minutes ago`;
        if (interval === 1) return 'a minute ago';

        if (seconds < 10) return 'just now';
        return `${Math.floor(seconds)} seconds ago`;
      };

      return {
        ...notification,
        time: formatTimeAgo(notification.created_at),
      };
    });

    return { success: true, data: formattedNotifications };
  } catch (error) {
    console.error('Error getting notifications:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Mark a notification as read
 * @param {string} notificationId - The notification ID
 * @returns {Promise<Object>} Success status
 */
export const markNotificationAsRead = async (notificationId) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Mark all notifications as read
 * @returns {Promise<Object>} Success status
 */
export const markAllNotificationsAsRead = async () => {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No authenticated user found');
    }

    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', user.id)
      .eq('is_dismissed', false);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete a notification (mark as dismissed)
 * @param {string} notificationId - The notification ID
 * @returns {Promise<Object>} Success status
 */
export const deleteNotification = async (notificationId) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ is_dismissed: true })
      .eq('id', notificationId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error deleting notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete all notifications (mark as dismissed)
 * @returns {Promise<Object>} Success status
 */
export const deleteAllNotifications = async () => {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No authenticated user found');
    }

    const { error } = await supabase
      .from('notifications')
      .update({ is_dismissed: true })
      .eq('user_id', user.id)
      .eq('is_dismissed', false);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error deleting all notifications:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Schedule a weather alert notification
 * @param {Object} weatherData - The weather data
 * @returns {Promise<string>} The notification ID
 */
export const scheduleWeatherAlert = async (weatherData) => {
  const { condition, humidity, description } = weatherData;

  // Check if humidity is high (over 70%)
  const isHighHumidity = humidity > 70;

  // Check if there are other risk factors
  const hasRiskFactors = weatherData.asthmaRiskLevel === 'high' ||
                         weatherData.asthmaRiskLevel === 'moderate';

  // Only send notification if there's a risk
  if (!isHighHumidity && !hasRiskFactors) {
    return null;
  }

  const hasPermissions = await requestNotificationPermissions();
  if (!hasPermissions) {
    return null;
  }

  // Create notification content
  let title, body;

  if (isHighHumidity) {
    title = 'Weather Alert: High Humidity';
    body = `Current humidity is ${humidity}%. This may affect your asthma. Consider staying indoors.`;
  } else if (hasRiskFactors) {
    title = 'Weather Alert: Asthma Risk';
    body = `Current weather conditions may affect your asthma. ${description || condition}`;
  }

  // Cancel any existing weather alert notification
  const existingId = await AsyncStorage.getItem(WEATHER_NOTIFICATION_ID_KEY);
  if (existingId) {
    await Notifications.cancelScheduledNotificationAsync(existingId);
  }

  // Schedule the notification
  const notificationId = await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data: { type: 'weather', weatherData },
      sound: true, // Use system default sound
    },
    trigger: null, // Send immediately
  });

  // Store the notification ID
  await AsyncStorage.setItem(WEATHER_NOTIFICATION_ID_KEY, notificationId);

  // Also create a database notification
  await createDatabaseNotification({
    title,
    message: body,
    notification_type: 'weather',
    is_read: false,
    is_dismissed: false,
  });

  return notificationId;
};

/**
 * Schedule a symptom tracking reminder
 * @returns {Promise<string>} The notification ID
 */
export const scheduleSymptomTrackingReminder = async () => {
  const hasPermissions = await requestNotificationPermissions();
  if (!hasPermissions) {
    return null;
  }

  // Cancel any existing symptom tracking reminder
  const existingId = await AsyncStorage.getItem(SYMPTOM_NOTIFICATION_ID_KEY);
  if (existingId) {
    await Notifications.cancelScheduledNotificationAsync(existingId);
  }

  // Schedule for 3 PM every day (afternoon)
  const notificationId = await Notifications.scheduleNotificationAsync({
    content: {
      title: 'Symptom Tracking Reminder',
      body: 'Have you tracked your asthma symptoms today? Regular tracking helps manage your condition better.',
      data: { type: 'symptom' },
      sound: true, // Use system default sound
    },
    trigger: {
      hour: 15, // 3 PM (afternoon)
      minute: 0,
      repeats: true,
    },
  });

  // Store the notification ID
  await AsyncStorage.setItem(SYMPTOM_NOTIFICATION_ID_KEY, notificationId);

  // Also create a database notification
  await createDatabaseNotification({
    title: 'Symptom Tracking Reminder',
    message: 'Have you tracked your asthma symptoms today? Regular tracking helps manage your condition better.',
    notification_type: 'symptom',
    is_read: false,
    is_dismissed: false,
  });

  return notificationId;
};

/**
 * Reset the symptom tracking reminder (for testing purposes)
 * @returns {Promise<void>}
 */
export const resetSymptomTrackingReminder = async () => {
  try {
    // Remove the flag that indicates we've scheduled the reminder
    await AsyncStorage.removeItem('has_scheduled_symptom_reminder');

    // Cancel any existing symptom tracking reminder
    const existingId = await AsyncStorage.getItem(SYMPTOM_NOTIFICATION_ID_KEY);
    if (existingId) {
      await Notifications.cancelScheduledNotificationAsync(existingId);
      await AsyncStorage.removeItem(SYMPTOM_NOTIFICATION_ID_KEY);
    }

    console.log('Symptom tracking reminder has been reset');
  } catch (error) {
    console.error('Error resetting symptom tracking reminder:', error);
  }
};

export default {
  requestNotificationPermissions,
  scheduleMedicationReminder,
  scheduleAllReminders,
  cancelMedicationReminder,
  cancelAllReminders,
  createDatabaseNotification,
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  deleteAllNotifications,
  scheduleWeatherAlert,
  scheduleSymptomTrackingReminder,
  resetSymptomTrackingReminder,
  playNotificationSound,
};
