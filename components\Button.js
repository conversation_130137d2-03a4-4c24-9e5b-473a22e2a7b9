/**
 * Custom Button Component
 */

import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';

const Button = ({
  title,
  onPress,
  type = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon = null,
  style,
  textStyle,
}) => {
  // Determine button style based on type
  const getButtonStyle = () => {
    switch (type) {
      case 'secondary':
        return styles.secondary;
      case 'outline':
        return styles.outline;
      case 'danger':
        return styles.danger;
      case 'success':
        return styles.success;
      case 'text':
        return styles.text;
      default:
        return styles.primary;
    }
  };

  // Determine button size
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return styles.small;
      case 'large':
        return styles.large;
      default:
        return styles.medium;
    }
  };

  // Determine text color based on button type
  const getTextColor = () => {
    switch (type) {
      case 'outline':
      case 'text':
        return { color: COLORS.PRIMARY };
      default:
        return { color: COLORS.WHITE };
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        getButtonSize(),
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator color={type === 'outline' ? COLORS.PRIMARY : COLORS.WHITE} />
      ) : (
        <>
          {icon}
          <Text style={[styles.buttonText, getTextColor(), textStyle]}>{title}</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: BORDER_RADIUS.medium,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    textAlign: 'center',
  },
  // Button types
  primary: {
    backgroundColor: COLORS.PRIMARY,
  },
  secondary: {
    backgroundColor: COLORS.SOFT_HIGHLIGHT,
  },
  outline: {
    backgroundColor: COLORS.TRANSPARENT,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  danger: {
    backgroundColor: COLORS.DANGER,
  },
  success: {
    backgroundColor: COLORS.SUCCESS,
  },
  text: {
    backgroundColor: COLORS.TRANSPARENT,
  },
  // Button sizes
  small: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.small,
  },
  medium: {
    paddingVertical: SPACING.small,
    paddingHorizontal: SPACING.medium,
  },
  large: {
    paddingVertical: SPACING.medium,
    paddingHorizontal: SPACING.large,
  },
  // Button states
  disabled: {
    opacity: 0.5,
  },
});

export default Button;
