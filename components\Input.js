/**
 * Input Component
 */

import React, { useState } from 'react';
import { View, TextInput, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from './theme';

const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry,
  keyboardType,
  autoCapitalize,
  error,
  multiline,
  numberOfLines,
  style,
  inputStyle,
  labelStyle,
  icon,
  onIconPress,
  editable = true,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const togglePasswordVisibility = () => setIsPasswordVisible(!isPasswordVisible);

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={[styles.label, labelStyle]}>{label}</Text>}
      <View
        style={[
          styles.inputContainer,
          isFocused && styles.focused,
          error && styles.error,
          !editable && styles.disabled,
        ]}
      >
        {icon && (
          <TouchableOpacity
            onPress={onIconPress}
            style={styles.iconContainer}
            disabled={!onIconPress}
          >
            {icon}
          </TouchableOpacity>
        )}
        <TextInput
          style={[
            styles.input,
            multiline && styles.multilineInput,
            icon && styles.inputWithIcon,
            inputStyle,
          ]}
          placeholder={placeholder}
          placeholderTextColor={COLORS.TEXT + '80'}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize || 'none'}
          onFocus={handleFocus}
          onBlur={handleBlur}
          multiline={multiline}
          numberOfLines={numberOfLines}
          editable={editable}
        />
        {secureTextEntry && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={styles.iconContainer}
          >
            <Ionicons
              name={isPasswordVisible ? 'eye-off' : 'eye'}
              size={20}
              color={COLORS.TEXT}
            />
          </TouchableOpacity>
        )}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.medium,
  },
  label: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.SOFT_HIGHLIGHT,
    borderRadius: BORDER_RADIUS.medium,
    backgroundColor: COLORS.WHITE,
  },
  input: {
    flex: 1,
    height: 48,
    paddingHorizontal: SPACING.medium,
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: SPACING.medium,
  },
  inputWithIcon: {
    paddingLeft: 0,
  },
  iconContainer: {
    paddingHorizontal: SPACING.medium,
  },
  focused: {
    borderColor: COLORS.PRIMARY,
    borderWidth: 2,
  },
  error: {
    borderColor: COLORS.DANGER,
  },
  errorText: {
    color: COLORS.DANGER,
    fontSize: FONTS.SIZES.small,
    marginTop: SPACING.xs,
  },
  disabled: {
    backgroundColor: COLORS.LIGHT_BG,
    opacity: 0.7,
  },
});

export default Input;
