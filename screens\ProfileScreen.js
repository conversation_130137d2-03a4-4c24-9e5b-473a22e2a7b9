/**
 * Profile Screen
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Image, Modal, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import Button from '../components/Button';
import CustomAlert from '../components/CustomAlert';
import { useAuth } from '../context/SupabaseAuthContext';
import i18n, { setLanguage, getCurrentLanguage } from '../i18n/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ProfileScreen = ({ navigation }) => {
  // Modal states
  const [languageModalVisible, setLanguageModalVisible] = useState(false);
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);
  const [deleteAccountModalVisible, setDeleteAccountModalVisible] = useState(false);
  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);
  const [languageChangeSuccessVisible, setLanguageChangeSuccessVisible] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(null);
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  // Edit profile form state
  const [editForm, setEditForm] = useState({
    name: '',
    phone: ''
  });
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);

  // Get auth functions and user data from context
  const { currentUser, userProfile, logout, deleteUserAccount, updateUserProfile } = useAuth();

  // Log user data for debugging
  useEffect(() => {
    console.log('Current user from context:', currentUser);
    console.log('User profile from context:', userProfile);
  }, [currentUser, userProfile]);

  // User data state
  const [userData, setUserData] = useState({
    name: 'User',
    email: '<EMAIL>',
    phone: '',
    language: 'English',
    notifications: {
      reminders: true,
      alerts: true,
      tips: false,
    },
    privacy: {
      shareData: false,
      locationTracking: true,
    },
  });

  // Update user data when profile or current user changes
  useEffect(() => {
    // First try to get data from userProfile
    if (userProfile) {
      setUserData(prevData => ({
        ...prevData,
        name: userProfile.name || 'User',
        email: userProfile.email || currentUser?.email || '<EMAIL>',
        phone: userProfile.phone || '',
      }));
    }
    // If no userProfile, try to get data from currentUser
    else if (currentUser) {
      setUserData(prevData => ({
        ...prevData,
        name: currentUser.user_metadata?.name || currentUser.user_metadata?.full_name || 'User',
        email: currentUser.email || '<EMAIL>',
        phone: currentUser.user_metadata?.phone || '',
      }));
    }
  }, [userProfile, currentUser]);

  // Function to get the display name for a language code
  const getLanguageDisplayName = (langCode) => {
    // Force the language names to be consistent regardless of current language
    // This ensures the language names are always displayed correctly
    let displayName;

    switch (langCode) {
      case 'en':
        displayName = 'English';
        break;
      case 'lg':
        displayName = 'Luganda';
        break;
      case 'at':
        displayName = 'Ateso';
        break;
      case 'lu':
        displayName = 'Luo';
        break;
      default:
        displayName = 'English';
    }

    return displayName;
  };

  // Load the current language on mount and when i18n changes
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const currentLang = await getCurrentLanguage();
        const displayName = getLanguageDisplayName(currentLang);

        setUserData(prevData => ({
          ...prevData,
          language: displayName
        }));
      } catch (error) {
        console.error('Error loading language:', error);
      }
    };

    loadLanguage();

    // This will ensure the language display updates when the app language changes
    const interval = setInterval(loadLanguage, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleLanguageChange = () => {
    setLanguageModalVisible(true);
  };

  const handleLanguageSelect = (code) => {
    setSelectedLanguage(code);
    setLanguageModalVisible(false);
    updateLanguage(code);
  };

  const updateLanguage = async (code) => {
    try {
      // Save the language code to AsyncStorage
      await setLanguage(code);
      await AsyncStorage.setItem('userLanguage', code);

      // Get the display name for the selected language
      const displayName = getLanguageDisplayName(code);

      // Update the UI
      setUserData({
        ...userData,
        language: displayName,
      });

      // Show success message with styled modal
      setLanguageChangeSuccessVisible(true);
    } catch (error) {
      console.error('Error updating language:', error);
      // We'll handle errors with a modal in a real app
    }
  };

  const handleToggleNotification = (type) => {
    setUserData({
      ...userData,
      notifications: {
        ...userData.notifications,
        [type]: !userData.notifications[type],
      },
    });
  };

  const handleTogglePrivacy = (type) => {
    setUserData({
      ...userData,
      privacy: {
        ...userData.privacy,
        [type]: !userData.privacy[type],
      },
    });
  };

  const handleLogout = () => {
    setLogoutModalVisible(true);
  };

  const confirmLogout = async () => {
    setLogoutModalVisible(false);

    try {
      // Call logout function from auth context
      const result = await logout();

      if (result.success) {
        navigation.replace('Auth');
      } else {
        // Show error alert
        setAlertConfig({
          title: 'Logout Failed',
          message: result.error || 'Failed to log out',
          type: 'danger'
        });
        setAlertVisible(true);
      }
    } catch (error) {
      // Show error alert
      setAlertConfig({
        title: 'Logout Error',
        message: error.message || 'An unexpected error occurred',
        type: 'danger'
      });
      setAlertVisible(true);
    }
  };

  const handleDeleteAccount = () => {
    setDeleteAccountModalVisible(true);
  };

  const confirmDeleteAccount = async () => {
    setDeleteAccountModalVisible(false);

    try {
      // Call deleteUserAccount function from auth context
      const result = await deleteUserAccount();

      if (result.success) {
        // Show success alert
        setAlertConfig({
          title: 'Account Deleted',
          message: 'Your account has been successfully deleted.',
          type: 'success',
          onConfirm: () => {
            navigation.replace('Auth');
          }
        });
        setAlertVisible(true);
      } else {
        // Show error alert
        setAlertConfig({
          title: 'Delete Failed',
          message: result.error || 'Failed to delete account',
          type: 'danger'
        });
        setAlertVisible(true);
      }
    } catch (error) {
      // Show error alert
      setAlertConfig({
        title: 'Delete Error',
        message: error.message || 'An unexpected error occurred',
        type: 'danger'
      });
      setAlertVisible(true);
    }
  };

  // Handle opening the edit profile modal
  const handleEditProfile = () => {
    // Initialize form with current user data
    setEditForm({
      name: userData.name,
      phone: userData.phone || ''
    });
    setEditProfileModalVisible(true);
  };

  // Handle updating the profile
  const handleUpdateProfile = async () => {
    setIsUpdatingProfile(true);

    try {
      // Call updateUserProfile function from auth context
      const result = await updateUserProfile({
        name: editForm.name,
        phone: editForm.phone
      });

      if (result.success) {
        // Close modal
        setEditProfileModalVisible(false);

        // Show success alert
        setAlertConfig({
          title: 'Profile Updated',
          message: 'Your profile has been successfully updated.',
          type: 'success'
        });
        setAlertVisible(true);

        // Update local state
        setUserData(prevData => ({
          ...prevData,
          name: editForm.name,
          phone: editForm.phone
        }));
      } else {
        // Show error alert
        setAlertConfig({
          title: 'Update Failed',
          message: result.error || 'Failed to update profile',
          type: 'danger'
        });
        setAlertVisible(true);
      }
    } catch (error) {
      // Show error alert
      setAlertConfig({
        title: 'Update Error',
        message: error.message || 'An unexpected error occurred',
        type: 'danger'
      });
      setAlertVisible(true);
    } finally {
      setIsUpdatingProfile(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('profile.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Image
              source={require('../assets/icon.png')} // Replace with actual user avatar
              style={styles.avatar}
              resizeMode="cover"
            />
            <TouchableOpacity style={styles.editAvatarButton}>
              <Ionicons name="camera" size={20} color={COLORS.WHITE} />
            </TouchableOpacity>
          </View>
          <Text style={styles.userName}>{userData.name}</Text>
          <TouchableOpacity
            style={styles.editProfileButton}
            onPress={handleEditProfile}
          >
            <Text style={styles.editProfileText}>{i18n.t('profile.editProfile')}</Text>
          </TouchableOpacity>
        </View>

        {/* Personal Information */}
        <Card title={i18n.t('profile.personalInfo')} style={styles.section}>
          <View style={styles.infoItem}>
            <View style={styles.infoIcon}>
              <Ionicons name="mail-outline" size={20} color={COLORS.PRIMARY} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{userData.email}</Text>
            </View>
          </View>
          <View style={styles.infoItem}>
            <View style={styles.infoIcon}>
              <Ionicons name="call-outline" size={20} color={COLORS.PRIMARY} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Phone</Text>
              <Text style={styles.infoValue}>{userData.phone}</Text>
            </View>
          </View>
        </Card>

        {/* Language */}
        <Card title={i18n.t('profile.language')} style={styles.section}>
          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleLanguageChange}
          >
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>{i18n.t('profile.appLanguage')}</Text>
              <Text style={styles.settingValue}>{userData.language}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={COLORS.TEXT} />
          </TouchableOpacity>
        </Card>

        {/* Notifications */}
        <Card title={i18n.t('profile.notifications')} style={styles.section}>
          <View style={styles.settingItem}>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Medication Reminders</Text>
              <Text style={styles.settingDescription}>
                Receive reminders for your medication schedule
              </Text>
            </View>
            <Switch
              value={userData.notifications.reminders}
              onValueChange={() => handleToggleNotification('reminders')}
              trackColor={{ false: COLORS.LIGHT_BG, true: COLORS.PRIMARY }}
              thumbColor={COLORS.WHITE}
            />
          </View>
          <View style={styles.settingItem}>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Weather Alerts</Text>
              <Text style={styles.settingDescription}>
                Get notified about weather conditions that may affect your asthma
              </Text>
            </View>
            <Switch
              value={userData.notifications.alerts}
              onValueChange={() => handleToggleNotification('alerts')}
              trackColor={{ false: COLORS.LIGHT_BG, true: COLORS.PRIMARY }}
              thumbColor={COLORS.WHITE}
            />
          </View>
          <View style={styles.settingItem}>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Daily Tips</Text>
              <Text style={styles.settingDescription}>
                Receive daily tips and advice for managing your asthma
              </Text>
            </View>
            <Switch
              value={userData.notifications.tips}
              onValueChange={() => handleToggleNotification('tips')}
              trackColor={{ false: COLORS.LIGHT_BG, true: COLORS.PRIMARY }}
              thumbColor={COLORS.WHITE}
            />
          </View>
        </Card>

        {/* Privacy */}
        <Card title={i18n.t('profile.privacy')} style={styles.section}>
          <View style={styles.settingItem}>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Share Anonymous Data</Text>
              <Text style={styles.settingDescription}>
                Share anonymous data to help improve asthma research
              </Text>
            </View>
            <Switch
              value={userData.privacy.shareData}
              onValueChange={() => handleTogglePrivacy('shareData')}
              trackColor={{ false: COLORS.LIGHT_BG, true: COLORS.PRIMARY }}
              thumbColor={COLORS.WHITE}
            />
          </View>
          <View style={styles.settingItem}>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Location Tracking</Text>
              <Text style={styles.settingDescription}>
                Allow the app to track your location for weather alerts
              </Text>
            </View>
            <Switch
              value={userData.privacy.locationTracking}
              onValueChange={() => handleTogglePrivacy('locationTracking')}
              trackColor={{ false: COLORS.LIGHT_BG, true: COLORS.PRIMARY }}
              thumbColor={COLORS.WHITE}
            />
          </View>
        </Card>

        {/* Account Actions */}
        <View style={styles.accountActions}>
          <Button
            title={i18n.t('profile.logout')}
            onPress={handleLogout}
            type="outline"
            icon={<Ionicons name="log-out-outline" size={20} color={COLORS.PRIMARY} />}
            style={styles.logoutButton}
          />
          <Button
            title={i18n.t('profile.deleteAccount')}
            onPress={handleDeleteAccount}
            type="danger"
            icon={<Ionicons name="trash-outline" size={20} color={COLORS.WHITE} />}
            style={styles.deleteButton}
          />
        </View>
      </ScrollView>

      {/* Language Selection Modal */}
      <Modal
        visible={languageModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setLanguageModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('profile.changeLanguage')}</Text>
              <TouchableOpacity onPress={() => setLanguageModalVisible(false)}>
                <Ionicons name="close" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
            </View>
            <Text style={styles.modalDescription}>{i18n.t('profile.selectLanguage')}</Text>

            <View style={styles.languageOptions}>
              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => handleLanguageSelect('en')}
              >
                <Text style={styles.languageName}>English</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => handleLanguageSelect('lg')}
              >
                <Text style={styles.languageName}>Luganda</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => handleLanguageSelect('at')}
              >
                <Text style={styles.languageName}>Ateso</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.languageOption}
                onPress={() => handleLanguageSelect('lu')}
              >
                <Text style={styles.languageName}>Luo</Text>
              </TouchableOpacity>
            </View>

            <Button
              title={i18n.t('cancel')}
              onPress={() => setLanguageModalVisible(false)}
              type="outline"
              style={styles.modalButton}
            />
          </View>
        </View>
      </Modal>

      {/* Language Change Success Modal */}
      <Modal
        visible={languageChangeSuccessVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setLanguageChangeSuccessVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.successIconContainer}>
              <Ionicons name="checkmark-circle" size={60} color={COLORS.SUCCESS} />
            </View>
            <Text style={styles.modalTitle}>{i18n.t('profile.languageChanged')}</Text>
            <Text style={styles.modalDescription}>
              {i18n.t('profile.languageChangedMessage')}
            </Text>
            <Text style={styles.currentLanguage}>
              {i18n.t('profile.currentLanguage')}: {userData.language}
            </Text>

            <Button
              title={i18n.t('ok')}
              onPress={() => {
                setLanguageChangeSuccessVisible(false);
                navigation.navigate('Main');
              }}
              style={styles.modalButton}
            />
          </View>
        </View>
      </Modal>

      {/* Logout Confirmation Modal */}
      <Modal
        visible={logoutModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setLogoutModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.warningIconContainer}>
              <Ionicons name="warning" size={60} color={COLORS.WARNING} />
            </View>
            <Text style={styles.modalTitle}>{i18n.t('profile.logout')}</Text>
            <Text style={styles.modalDescription}>
              {i18n.t('profile.logoutConfirmation')}
            </Text>

            <View style={styles.modalButtonsRow}>
              <Button
                title={i18n.t('cancel')}
                onPress={() => setLogoutModalVisible(false)}
                type="outline"
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
              <Button
                title={i18n.t('profile.logout')}
                onPress={confirmLogout}
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
            </View>
          </View>
        </View>
      </Modal>

      {/* Delete Account Confirmation Modal */}
      <Modal
        visible={deleteAccountModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setDeleteAccountModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.dangerIconContainer}>
              <Ionicons name="alert-circle" size={60} color={COLORS.DANGER} />
            </View>
            <Text style={styles.modalTitle}>{i18n.t('profile.deleteAccount')}</Text>
            <Text style={styles.modalDescription}>
              {i18n.t('profile.deleteAccountConfirmation')}
            </Text>

            <View style={styles.modalButtonsRow}>
              <Button
                title={i18n.t('cancel')}
                onPress={() => setDeleteAccountModalVisible(false)}
                type="outline"
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
              <Button
                title={i18n.t('delete')}
                onPress={confirmDeleteAccount}
                type="danger"
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
            </View>
          </View>
        </View>
      </Modal>

      {/* Edit Profile Modal */}
      <Modal
        visible={editProfileModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setEditProfileModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Edit Profile</Text>
              <TouchableOpacity onPress={() => setEditProfileModalVisible(false)}>
                <Ionicons name="close" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
            </View>

            {/* Name Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Name</Text>
              <View style={styles.inputWrapper}>
                <Ionicons name="person-outline" size={20} color={COLORS.TEXT} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={editForm.name}
                  onChangeText={(text) => setEditForm(prev => ({ ...prev, name: text }))}
                  placeholder="Enter your name"
                />
              </View>
            </View>

            {/* Phone Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone</Text>
              <View style={styles.inputWrapper}>
                <Ionicons name="call-outline" size={20} color={COLORS.TEXT} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  value={editForm.phone}
                  onChangeText={(text) => setEditForm(prev => ({ ...prev, phone: text }))}
                  placeholder="Enter your phone number"
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            <View style={styles.modalButtonsRow}>
              <Button
                title="Cancel"
                onPress={() => setEditProfileModalVisible(false)}
                type="outline"
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
              <Button
                title="Save"
                onPress={handleUpdateProfile}
                loading={isUpdatingProfile}
                style={[styles.modalButton, styles.modalButtonHalf]}
              />
            </View>
          </View>
        </View>
      </Modal>

      {/* Custom Alert */}
      <CustomAlert
        visible={alertVisible}
        onClose={() => setAlertVisible(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
        onConfirm={alertConfig.onConfirm}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: SPACING.large,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: SPACING.medium,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.LIGHT_BG,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.PRIMARY,
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: COLORS.WHITE,
  },
  userName: {
    fontSize: FONTS.SIZES.xl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  editProfileButton: {
    paddingHorizontal: SPACING.medium,
    paddingVertical: SPACING.xs,
    backgroundColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.medium,
  },
  editProfileText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  section: {
    marginBottom: SPACING.medium,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.small,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  infoIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.PRIMARY + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.medium,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  settingContent: {
    flex: 1,
    marginRight: SPACING.medium,
  },
  settingLabel: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginBottom: 2,
  },
  settingValue: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.PRIMARY,
  },
  settingDescription: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  accountActions: {
    marginVertical: SPACING.large,
  },
  logoutButton: {
    marginBottom: SPACING.medium,
  },
  deleteButton: {
    backgroundColor: COLORS.DANGER,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: COLORS.WHITE,
    borderRadius: BORDER_RADIUS.large,
    padding: SPACING.large,
    alignItems: 'center',
    elevation: 5,
    shadowColor: COLORS.TEXT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  modalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginVertical: SPACING.small,
  },
  modalDescription: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
    marginBottom: SPACING.medium,
    lineHeight: 22,
  },
  currentLanguage: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.PRIMARY,
    marginBottom: SPACING.medium,
  },
  languageOptions: {
    width: '100%',
    marginBottom: SPACING.medium,
  },
  languageOption: {
    paddingVertical: SPACING.medium,
    paddingHorizontal: SPACING.small,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  languageName: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  modalButton: {
    marginTop: SPACING.small,
    width: '100%',
  },
  modalButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButtonHalf: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  successIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.SUCCESS + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  warningIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.WARNING + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  dangerIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.DANGER + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  infoIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.PRIMARY + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  // Input styles
  inputContainer: {
    width: '100%',
    marginBottom: SPACING.medium,
  },
  inputLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.medium,
    paddingHorizontal: SPACING.small,
    backgroundColor: COLORS.WHITE,
  },
  inputIcon: {
    marginRight: SPACING.small,
  },
  input: {
    flex: 1,
    height: 50,
    color: COLORS.TEXT,
    fontSize: FONTS.SIZES.medium,
  },
});

export default ProfileScreen;
