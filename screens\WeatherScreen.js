/**
 * Weather & Humidity Tracker Screen
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  TextInput,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../components/theme';
import Header from '../components/Header';
import Card from '../components/Card';
import WeatherAlert from '../components/WeatherAlert';
import i18n from '../i18n/i18n';
import WeatherService from '../services/WeatherService';
import NotificationService from '../services/NotificationService';

const WeatherScreen = ({ navigation }) => {
  const [currentLocation, setCurrentLocation] = useState('Loading...');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [coordinates, setCoordinates] = useState(null);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Weather data state
  const [weatherData, setWeatherData] = useState({
    current: {
      temperature: 25, // Default temperature
      humidity: 60, // Default humidity
      condition: 'Clear', // Default condition
      icon: 'partly-sunny', // Default icon
      airQuality: 'Moderate', // Default air quality
      riskLevel: 'low', // Default risk level
      message: 'Loading weather information for your location...', // Default message
    },
    forecast: [], // Will be populated with forecast data
    locations: [
      { name: 'Current Location', country: '', coordinates: null },
    ],
  });

  // Fetch weather data when component mounts
  useEffect(() => {
    console.log('WeatherScreen: Component mounted, fetching weather data');
    fetchWeatherData();
  }, []);

  // Function to fetch weather data
  const fetchWeatherData = async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('WeatherScreen: Starting to fetch weather data' + (forceRefresh ? ' (forced refresh)' : ''));

      // Get current location
      const location = await WeatherService.getCurrentLocation();
      console.log('WeatherScreen: Got location:', location);
      setCoordinates(location);

      try {
        console.log('WeatherScreen: Fetching weather data for location:', location.latitude, location.longitude);

        // Get current weather with caching
        const weatherResponse = await WeatherService.getCurrentWeather(
          location.latitude,
          location.longitude,
          forceRefresh
        );
        console.log('WeatherScreen: Weather data received:', weatherResponse.name);

        // Get air quality with caching
        const airQualityResponse = await WeatherService.getAirQuality(
          location.latitude,
          location.longitude,
          forceRefresh
        );
        console.log('WeatherScreen: Air quality data received');

        // Get forecast data - try to get weekly forecast first with caching
        let weeklyForecast = null;
        try {
          // Only try to get weekly forecast if we're not rate limited
          if (!forceRefresh) {
            weeklyForecast = await WeatherService.getWeeklyForecast(
              location.latitude,
              location.longitude,
              false // Don't force refresh weekly forecast to avoid rate limiting
            );
            console.log('WeatherScreen: Weekly forecast data received');
          }
        } catch (weeklyForecastError) {
          console.log('WeatherScreen: Using 5-day forecast instead of weekly forecast');
          // Continue with 5-day forecast
        }

        // Get 5-day forecast as backup with caching
        const forecastResponse = await WeatherService.getWeatherForecast(
          location.latitude,
          location.longitude,
          forceRefresh
        );
        console.log('WeatherScreen: 5-day forecast data received');

        // Determine asthma risk
        const riskAssessment = WeatherService.determineAsthmaRisk(
          weatherResponse,
          airQualityResponse
        );
        console.log('WeatherScreen: Risk assessment completed:', riskAssessment.riskLevel);

        // Get location name
        const locationName = weatherResponse.name;
        const country = weatherResponse.sys.country;
        console.log('WeatherScreen: Location identified as:', locationName, country);
        setCurrentLocation(`${locationName}, ${country}`);

        // Process forecast data using both data sources
        const forecastData = processForecastData(forecastResponse, weeklyForecast);
        console.log('WeatherScreen: Forecast data processed, days:', forecastData.length);

        // Get air quality text
        const airQualityText = WeatherService.getAirQualityText(airQualityResponse?.list?.[0]?.main?.aqi || 1);

        // Update weather data state with real data
        const updatedWeatherData = {
          current: {
            temperature: riskAssessment.temperature,
            humidity: riskAssessment.humidity,
            condition: weatherResponse.weather[0].main,
            icon: getIconName(weatherResponse.weather[0].main),
            airQuality: airQualityText,
            riskLevel: riskAssessment.riskLevel,
            message: riskAssessment.message,
          },
          forecast: forecastData,
          locations: [
            {
              name: locationName,
              country: country,
              coordinates: location
            },
            ...weatherData.locations.filter(loc =>
              loc.name !== 'Current Location' &&
              loc.name !== 'Your City' &&
              loc.name !== 'Your Current Location'
            ),
          ],
        };

        setWeatherData(updatedWeatherData);

        console.log('WeatherScreen: Weather data state updated with real data');

        // Check if we should send a weather alert notification
        if (riskAssessment.riskLevel === 'high' || riskAssessment.riskLevel === 'moderate' || riskAssessment.humidity > 70) {
          console.log('WeatherScreen: Sending weather alert notification');

          // Create weather alert data
          const weatherAlertData = {
            condition: weatherResponse.weather[0].main,
            description: weatherResponse.weather[0].description,
            humidity: riskAssessment.humidity,
            temperature: riskAssessment.temperature,
            asthmaRiskLevel: riskAssessment.riskLevel,
          };

          // Schedule the weather alert notification
          try {
            await NotificationService.scheduleWeatherAlert(weatherAlertData);
            console.log('WeatherScreen: Weather alert notification scheduled');
          } catch (notificationError) {
            console.error('WeatherScreen: Error scheduling weather alert notification:', notificationError);
          }
        }

        // Clear any error message since we successfully got data
        setError(null);
      } catch (apiError) {
        console.error('WeatherScreen: API Error fetching weather data:', apiError);

        // Check if it's a rate limiting error (429)
        if (apiError.response && apiError.response.status === 429) {
          console.log('WeatherScreen: Rate limiting error (429), too many requests');
          setError('Weather API rate limit exceeded. Please try again later. Using location data with limited weather information.');
        } else if (apiError.response && apiError.response.status === 401) {
          console.log('WeatherScreen: API key unauthorized (401), check your OpenWeatherMap API key');
          setError('Weather API key issue. Please check your API key. Using location data with limited weather information.');
        } else {
          console.log('WeatherScreen: Other API error, possibly network related');
          setError('Unable to fetch complete weather data. Using location data with limited weather information.');
        }

        // Use location coordinates for reverse geocoding to get location name
        try {
          console.log('WeatherScreen: Attempting to get location name from coordinates');
          const geocodingResponse = await Location.reverseGeocodeAsync({
            latitude: location.latitude,
            longitude: location.longitude
          });

          if (geocodingResponse && geocodingResponse.length > 0) {
            const geocode = geocodingResponse[0];
            const cityName = geocode.city || geocode.subregion || geocode.region || 'Unknown Location';
            const countryCode = geocode.isoCountryCode || '';

            console.log('WeatherScreen: Location name from geocoding:', cityName, countryCode);
            setCurrentLocation(`${cityName}, ${countryCode}`);

            // Create forecast data based on location
            const forecastData = createForecastFromLocation(cityName, countryCode, location);
            console.log('WeatherScreen: Created forecast data with length:', forecastData.length);

            // Update weather data with location-based data
            const newWeatherData = {
              current: {
                temperature: forecastData[0].temperature,
                humidity: forecastData[0].humidity,
                condition: forecastData[0].condition,
                icon: forecastData[0].icon,
                airQuality: 'Moderate', // Default value
                riskLevel: forecastData[0].riskLevel,
                message: `Weather information for ${cityName} is based on your actual location.`,
              },
              forecast: forecastData,
              locations: [
                {
                  name: cityName,
                  country: countryCode,
                  coordinates: location
                },
                ...weatherData.locations.filter(loc =>
                  loc.name !== 'Current Location' &&
                  loc.name !== 'Your City' &&
                  loc.name !== 'Your Current Location' &&
                  loc.name !== cityName
                ),
              ],
            };

            console.log('WeatherScreen: Setting new weather data:',
              'Temperature:', newWeatherData.current.temperature,
              'Condition:', newWeatherData.current.condition
            );

            setWeatherData(newWeatherData);

            // Clear error message since we have data to display
            setError(null);
          } else {
            console.log('WeatherScreen: No geocoding results found');
            setCurrentLocation('Your Current Location');

            // Create generic forecast data
            const forecastData = createForecastFromLocation('Your Location', '', location);

            // Update weather data with generic location-based data
            setWeatherData({
              current: {
                temperature: forecastData[0].temperature,
                humidity: forecastData[0].humidity,
                condition: forecastData[0].condition,
                icon: forecastData[0].icon,
                airQuality: 'Moderate', // Default value
                riskLevel: forecastData[0].riskLevel,
                message: 'Weather information is based on your current location.',
              },
              forecast: forecastData,
              locations: [
                {
                  name: 'Your Current Location',
                  country: '',
                  coordinates: location
                },
                ...weatherData.locations.filter(loc =>
                  loc.name !== 'Current Location' &&
                  loc.name !== 'Your City' &&
                  loc.name !== 'Your Current Location'
                ),
              ],
            });

            // Clear error message since we have data to display
            setError(null);
          }
        } catch (geocodeError) {
          console.error('WeatherScreen: Error getting location name from coordinates:', geocodeError);
          setCurrentLocation('Your Current Location');

          // Create generic forecast data even if geocoding fails
          const forecastData = createForecastFromLocation('Your Location', '', location);

          // Update weather data with generic location-based data
          setWeatherData({
            current: {
              temperature: forecastData[0].temperature,
              humidity: forecastData[0].humidity,
              condition: forecastData[0].condition,
              icon: forecastData[0].icon,
              airQuality: 'Moderate', // Default value
              riskLevel: forecastData[0].riskLevel,
              message: 'Weather information is based on your current location.',
            },
            forecast: forecastData,
            locations: [
              {
                name: 'Your Current Location',
                country: '',
                coordinates: location
              },
              ...weatherData.locations.filter(loc =>
                loc.name !== 'Current Location' &&
                loc.name !== 'Your City' &&
                loc.name !== 'Your Current Location'
              ),
            ],
          });

          // Clear error message since we have data to display
          setError(null);
        }
      }
    } catch (err) {
      console.error('WeatherScreen: Error fetching location data:', err);
      setError('Unable to access location. Please check your location permissions and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Create forecast data based on location and current date/time
  const createForecastFromLocation = (cityName, countryCode, location) => {
    console.log('WeatherScreen: Creating forecast data for location:', cityName, countryCode);

    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const today = new Date();
    const forecastData = [];

    // Get season based on location and date
    const month = today.getMonth(); // 0-11
    let season;

    // Determine if location is in northern or southern hemisphere
    const isNorthernHemisphere = location.latitude > 0;

    if (isNorthernHemisphere) {
      // Northern hemisphere seasons
      if (month >= 2 && month <= 4) season = 'spring';
      else if (month >= 5 && month <= 7) season = 'summer';
      else if (month >= 8 && month <= 10) season = 'autumn';
      else season = 'winter';
    } else {
      // Southern hemisphere seasons (reversed)
      if (month >= 2 && month <= 4) season = 'autumn';
      else if (month >= 5 && month <= 7) season = 'winter';
      else if (month >= 8 && month <= 10) season = 'spring';
      else season = 'summer';
    }

    // Generate base temperature based on location and season
    let baseTemp;
    if (Math.abs(location.latitude) < 23.5) {
      // Tropical region (between tropics)
      baseTemp = 28; // Hot all year
    } else if (Math.abs(location.latitude) < 40) {
      // Subtropical
      baseTemp = season === 'summer' ? 30 : season === 'winter' ? 15 : 22;
    } else {
      // Temperate/cold
      baseTemp = season === 'summer' ? 25 : season === 'winter' ? 5 : 15;
    }

    // Add some randomness based on location
    const locationSeed = (location.latitude + location.longitude) % 10;
    baseTemp += locationSeed - 5; // -5 to +5 variation

    // Generate forecast for 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(today.getDate() + i);
      const dayName = i === 0 ? 'Today' : i === 1 ? 'Tomorrow' : days[date.getDay()];

      // Add daily variation
      const dailyVariation = Math.sin(i * 0.5) * 3; // Sinusoidal variation over days
      const temp = Math.round(baseTemp + dailyVariation);

      // Determine weather condition based on season and random factors
      let condition, icon, riskLevel;
      const rand = Math.random();

      if (season === 'summer') {
        if (rand < 0.6) {
          condition = 'Clear';
          icon = 'sunny';
          riskLevel = 'low';
        } else if (rand < 0.9) {
          condition = 'Clouds';
          icon = 'partly-sunny';
          riskLevel = 'moderate';
        } else {
          condition = 'Rain';
          icon = 'rainy';
          riskLevel = 'high';
        }
      } else if (season === 'winter') {
        if (rand < 0.4) {
          condition = 'Clouds';
          icon = 'partly-sunny';
          riskLevel = 'moderate';
        } else if (rand < 0.7) {
          condition = 'Rain';
          icon = 'rainy';
          riskLevel = 'high';
        } else {
          condition = 'Clear';
          icon = 'sunny';
          riskLevel = 'low';
        }
      } else {
        // Spring/autumn
        if (rand < 0.5) {
          condition = 'Clouds';
          icon = 'partly-sunny';
          riskLevel = 'moderate';
        } else if (rand < 0.8) {
          condition = 'Clear';
          icon = 'sunny';
          riskLevel = 'low';
        } else {
          condition = 'Rain';
          icon = 'rainy';
          riskLevel = 'high';
        }
      }

      // Generate humidity based on condition and season
      let humidity;
      if (condition === 'Rain') {
        humidity = 75 + Math.floor(Math.random() * 15); // 75-90%
      } else if (condition === 'Clouds') {
        humidity = 60 + Math.floor(Math.random() * 15); // 60-75%
      } else {
        humidity = 40 + Math.floor(Math.random() * 20); // 40-60%
      }

      // Adjust for season
      if (season === 'summer') humidity -= 10;
      if (season === 'winter') humidity += 10;

      // Ensure humidity is in valid range
      humidity = Math.max(30, Math.min(95, humidity));

      forecastData.push({
        day: dayName,
        date: date,
        temperature: temp,
        minTemp: temp - 3 - Math.floor(Math.random() * 2),
        maxTemp: temp + 3 + Math.floor(Math.random() * 2),
        humidity: humidity,
        condition: condition,
        icon: icon,
        riskLevel: riskLevel,
        windSpeed: 2 + Math.floor(Math.random() * 6), // 2-8 m/s
        precipitation: condition === 'Rain' ? 70 + Math.floor(Math.random() * 30) :
                      condition === 'Clouds' ? 20 + Math.floor(Math.random() * 30) :
                      Math.floor(Math.random() * 10),
      });
    }

    // Log the generated forecast data to verify it's correct
    console.log('WeatherScreen: Generated forecast data for today:', forecastData[0]);

    return forecastData;
  };

  // Helper function to create mock forecast data
  const createMockForecast = (locationName = '', countryCode = '') => {
    console.log('WeatherScreen: Creating mock forecast data for:', locationName || 'generic location');

    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const today = new Date();
    const mockForecast = [];

    // Add today and the next 6 days
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(today.getDate() + i);
      const dayName = i === 0 ? 'Today' : i === 1 ? 'Tomorrow' : days[date.getDay()];

      // Generate somewhat realistic weather patterns
      let dayTemp, dayHumidity, dayCondition, dayIcon, dayRiskLevel;

      if (i === 0) {
        // Today
        dayTemp = 24;
        dayHumidity = 65;
        dayCondition = 'Partly Cloudy';
        dayIcon = 'partly-sunny';
        dayRiskLevel = 'moderate';
      } else if (i === 1) {
        // Tomorrow
        dayTemp = 26;
        dayHumidity = 60;
        dayCondition = 'Sunny';
        dayIcon = 'sunny';
        dayRiskLevel = 'low';
      } else if (i === 2 || i === 3) {
        // Mid-week rain
        dayTemp = 22 - (i - 2);
        dayHumidity = 75 + (i - 2) * 5;
        dayCondition = 'Rain';
        dayIcon = 'rainy';
        dayRiskLevel = 'high';
      } else {
        // End of week
        dayTemp = 23 + (i - 4);
        dayHumidity = Math.max(30, 70 - ((i - 3) * 10));
        dayCondition = i === 6 ? 'Sunny' : 'Partly Cloudy';
        dayIcon = i === 6 ? 'sunny' : 'partly-sunny';
        dayRiskLevel = i === 6 ? 'low' : 'moderate';
      }

      mockForecast.push({
        day: dayName,
        date: date,
        temperature: dayTemp,
        minTemp: dayTemp - 3,
        maxTemp: dayTemp + 3,
        humidity: dayHumidity,
        condition: dayCondition,
        icon: dayIcon,
        riskLevel: dayRiskLevel,
        windSpeed: Math.floor(Math.random() * 5) + 2, // 2-7 m/s
        precipitation: dayCondition === 'Rain' ? 80 : dayCondition === 'Partly Cloudy' ? 30 : 10,
      });
    }

    return mockForecast;
  };

  // Helper function to process forecast data for a full week
  const processForecastData = (forecastResponse, weeklyForecast = null) => {
    // If we have weekly forecast data from the One Call API, use it
    if (weeklyForecast && weeklyForecast.daily) {
      const processedData = [];
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const today = new Date();

      // Process each day from the weekly forecast
      weeklyForecast.daily.forEach((item, index) => {
        const date = new Date(item.dt * 1000);
        let dayName;

        if (index === 0) {
          dayName = 'Today';
        } else if (index === 1) {
          dayName = 'Tomorrow';
        } else {
          dayName = days[date.getDay()];
        }

        processedData.push({
          day: dayName,
          date: date,
          temperature: Math.round(item.temp.day),
          minTemp: Math.round(item.temp.min),
          maxTemp: Math.round(item.temp.max),
          humidity: item.humidity,
          condition: item.weather[0].main,
          description: item.weather[0].description,
          icon: getIconName(item.weather[0].main),
          riskLevel: determineRiskLevel(item.temp.day, item.humidity, item.weather[0].main),
          windSpeed: item.wind_speed,
          precipitation: item.pop * 100, // Probability of precipitation as percentage
        });
      });

      return processedData;
    }

    // Fallback to processing the 5-day/3-hour forecast
    if (!forecastResponse || !forecastResponse.list) return [];

    const forecastList = forecastResponse.list;
    const processedData = [];

    // Get day names
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const today = new Date();

    // Group forecast data by day
    const dailyData = {};

    forecastList.forEach(item => {
      const date = new Date(item.dt * 1000);
      const dayKey = date.toDateString();

      if (!dailyData[dayKey]) {
        dailyData[dayKey] = {
          date: date,
          temps: [],
          humidity: [],
          conditions: [],
          windSpeed: [],
          precipitation: [],
        };
      }

      dailyData[dayKey].temps.push(item.main.temp);
      dailyData[dayKey].humidity.push(item.main.humidity);
      dailyData[dayKey].conditions.push(item.weather[0].main);
      dailyData[dayKey].windSpeed.push(item.wind.speed);
      dailyData[dayKey].precipitation.push(item.pop || 0);
    });

    // Process daily data
    Object.keys(dailyData).sort((a, b) => new Date(a) - new Date(b)).forEach((dayKey, index) => {
      const data = dailyData[dayKey];
      const date = data.date;

      // Get the most common weather condition for the day
      const conditionCounts = {};
      data.conditions.forEach(condition => {
        conditionCounts[condition] = (conditionCounts[condition] || 0) + 1;
      });
      const condition = Object.keys(conditionCounts).reduce((a, b) =>
        conditionCounts[a] > conditionCounts[b] ? a : b, data.conditions[0]);

      // Calculate average values
      const avgTemp = data.temps.reduce((sum, temp) => sum + temp, 0) / data.temps.length;
      const minTemp = Math.min(...data.temps);
      const maxTemp = Math.max(...data.temps);
      const avgHumidity = data.humidity.reduce((sum, h) => sum + h, 0) / data.humidity.length;
      const avgWindSpeed = data.windSpeed.reduce((sum, w) => sum + w, 0) / data.windSpeed.length;
      const maxPrecipitation = Math.max(...data.precipitation) * 100; // Convert to percentage

      let dayName;
      if (index === 0) {
        dayName = 'Today';
      } else if (index === 1) {
        dayName = 'Tomorrow';
      } else {
        dayName = days[date.getDay()];
      }

      processedData.push({
        day: dayName,
        date: date,
        temperature: Math.round(avgTemp),
        minTemp: Math.round(minTemp),
        maxTemp: Math.round(maxTemp),
        humidity: Math.round(avgHumidity),
        condition: condition,
        icon: getIconName(condition),
        riskLevel: determineRiskLevel(avgTemp, avgHumidity, condition),
        windSpeed: Math.round(avgWindSpeed),
        precipitation: Math.round(maxPrecipitation),
      });
    });

    return processedData;
  };

  // Helper function to determine risk level based on weather conditions
  const determineRiskLevel = (temperature, humidity, condition) => {
    let riskLevel = 'low';

    // Check temperature extremes
    if (temperature < 0 || temperature > 30) {
      riskLevel = 'high';
    }

    // Check humidity
    if (humidity > 60) {
      riskLevel = riskLevel === 'high' ? 'high' : 'moderate';
    } else if (humidity < 30) {
      riskLevel = riskLevel === 'high' ? 'high' : 'moderate';
    }

    // Check weather conditions - support both OpenWeatherMap and WeatherAPI formats
    const rainConditions = ['Rain', 'Drizzle', 'Thunderstorm', 'Moderate rain', 'Heavy rain', 'Light rain', 'Patchy rain', 'Showers'];
    if (rainConditions.some(cond => condition && condition.includes && condition.includes(cond))) {
      riskLevel = riskLevel === 'high' ? 'high' : 'moderate';
    }

    return riskLevel;
  };

  // Process forecast data from WeatherAPI.com
  const processWeatherAPIForecast = (forecastDays) => {
    console.log('WeatherScreen: Processing WeatherAPI forecast data, days:', forecastDays.length);

    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const forecastData = [];
    const today = new Date();

    forecastDays.forEach((day, index) => {
      const date = new Date(day.date);
      const dayName = index === 0 ? 'Today' : index === 1 ? 'Tomorrow' : days[date.getDay()];

      // Get risk level based on day's conditions
      const riskLevel = determineRiskLevel(
        day.day.avgtemp_c,
        day.day.avghumidity,
        day.day.condition.text
      );

      forecastData.push({
        day: dayName,
        date: date,
        temperature: Math.round(day.day.avgtemp_c),
        minTemp: Math.round(day.day.mintemp_c),
        maxTemp: Math.round(day.day.maxtemp_c),
        humidity: day.day.avghumidity,
        condition: day.day.condition.text,
        icon: getIconNameFromWeatherAPI(day.day.condition.text, true), // Assume daytime for forecast
        riskLevel: riskLevel,
        windSpeed: day.day.maxwind_kph,
        precipitation: day.day.daily_chance_of_rain,
      });
    });

    console.log('WeatherScreen: Processed forecast data:', forecastData.length, 'days');
    return forecastData;
  };

  // Get icon name from WeatherAPI.com condition text
  const getIconNameFromWeatherAPI = (conditionText, isDay) => {
    const condition = conditionText ? conditionText.toLowerCase() : '';

    // Night icons if it's night time
    if (!isDay) {
      if (condition.includes('clear') || condition.includes('sunny')) return 'moon';
      if (condition.includes('partly cloudy')) return 'partly-cloudy-night';
    }

    // Day or night icons
    if (condition.includes('sunny') || condition.includes('clear')) return 'sunny';
    if (condition.includes('partly cloudy')) return 'partly-sunny';
    if (condition.includes('cloudy') || condition.includes('overcast')) return 'cloudy';
    if (condition.includes('mist') || condition.includes('fog')) return 'cloudy';
    if (condition.includes('rain') || condition.includes('drizzle') || condition.includes('shower')) return 'rainy';
    if (condition.includes('snow') || condition.includes('sleet') || condition.includes('ice')) return 'snow';
    if (condition.includes('thunder') || condition.includes('lightning')) return 'thunderstorm';

    // Default icon
    return 'partly-sunny';
  };

  // Helper function to get icon name based on weather condition
  const getIconName = (condition) => {
    switch (condition) {
      case 'Clear':
        return 'sunny';
      case 'Clouds':
        return 'partly-sunny';
      case 'Rain':
      case 'Drizzle':
        return 'rainy';
      case 'Thunderstorm':
        return 'thunderstorm';
      case 'Snow':
        return 'snow';
      case 'Mist':
      case 'Fog':
      case 'Haze':
        return 'cloud';
      default:
        return 'partly-sunny';
    }
  };

  // Helper function to get air quality text
  const getAirQualityText = (aqi) => {
    switch (aqi) {
      case 1:
        return 'Good';
      case 2:
        return 'Fair';
      case 3:
        return 'Moderate';
      case 4:
        return 'Poor';
      case 5:
        return 'Very Poor';
      default:
        return 'Unknown';
    }
  };

  // Get risk color based on risk level
  const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
      case 'high':
        return COLORS.DANGER;
      case 'moderate':
      case 'medium': // For backward compatibility
        return COLORS.WARNING;
      case 'low':
      default:
        return COLORS.SUCCESS;
    }
  };

  // Get risk text based on risk level
  const getRiskText = (riskLevel) => {
    switch (riskLevel) {
      case 'high':
        return i18n.t('home.highRisk');
      case 'moderate':
      case 'medium': // For backward compatibility
        return i18n.t('home.mediumRisk');
      case 'low':
      default:
        return i18n.t('home.lowRisk');
    }
  };

  const handleRefresh = () => {
    console.log('WeatherScreen: Manual refresh button pressed');
    // Force refresh to bypass cache
    fetchWeatherData(true);
  };

  // Function to handle pull-to-refresh
  const onRefresh = async () => {
    console.log('WeatherScreen: Pull-to-refresh triggered');
    setRefreshing(true);
    try {
      // Force refresh to bypass cache
      await fetchWeatherData(true);
      console.log('WeatherScreen: Refresh completed successfully');
    } catch (error) {
      console.error('WeatherScreen: Error during refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle search button press
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearchLoading(true);
      const results = await WeatherService.searchCity(searchQuery.trim());
      setSearchResults(results);
    } catch (err) {
      console.error('Error searching for city:', err);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  // Handle search result selection
  const handleSearchResultSelect = (location) => {
    setSearchModalVisible(false);
    setSearchQuery('');
    setSearchResults([]);
    handleLocationSelect(location);
  };

  const handleLocationSelect = async (location) => {
    if (!location.coordinates) {
      // If it's just a named location without coordinates, just update the display
      setCurrentLocation(`${location.name}, ${location.country}`);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setCurrentLocation(`${location.name}, ${location.country}`);

      try {
        // Get current weather for the selected location
        const weatherResponse = await WeatherService.getCurrentWeather(
          location.coordinates.latitude,
          location.coordinates.longitude
        );

        // Get air quality for the selected location
        const airQualityResponse = await WeatherService.getAirQuality(
          location.coordinates.latitude,
          location.coordinates.longitude
        );

        // Get weekly forecast for the selected location
        let weeklyForecast = null;
        try {
          weeklyForecast = await WeatherService.getWeeklyForecast(
            location.coordinates.latitude,
            location.coordinates.longitude
          );
        } catch (weeklyForecastError) {
          console.error('Error fetching weekly forecast data:', weeklyForecastError);
          // Continue with 5-day forecast
        }

        // Get 5-day forecast as backup
        const forecastResponse = await WeatherService.getWeatherForecast(
          location.coordinates.latitude,
          location.coordinates.longitude
        );

        // Determine asthma risk
        const riskAssessment = WeatherService.determineAsthmaRisk(
          weatherResponse,
          airQualityResponse
        );

        // Process forecast data using both data sources
        const forecastData = processForecastData(forecastResponse, weeklyForecast);

        // Update weather data state
        setWeatherData({
          ...weatherData,
          current: {
            temperature: riskAssessment.temperature,
            humidity: riskAssessment.humidity,
            condition: weatherResponse.weather[0].main,
            icon: getIconName(weatherResponse.weather[0].main),
            airQuality: getAirQualityText(airQualityResponse?.list?.[0]?.main?.aqi || 1),
            riskLevel: riskAssessment.riskLevel,
            message: riskAssessment.message,
          },
          forecast: forecastData,
        });
      } catch (apiError) {
        console.error('API Error fetching location weather data:', apiError);

        // Generate mock data based on the location
        // Different locations will have different mock data to make it seem more realistic
        let mockTemp, mockHumidity, mockCondition, mockIcon, mockRiskLevel;

        // Generate somewhat realistic mock data based on the location name
        // This is just for demo purposes
        if (location.name === 'New York') {
          mockTemp = 18;
          mockHumidity = 55;
          mockCondition = 'Partly Cloudy';
          mockIcon = 'partly-sunny';
          mockRiskLevel = 'low';
        } else if (location.name === 'London') {
          mockTemp = 15;
          mockHumidity = 80;
          mockCondition = 'Rain';
          mockIcon = 'rainy';
          mockRiskLevel = 'high';
        } else if (location.name === 'Tokyo') {
          mockTemp = 26;
          mockHumidity = 70;
          mockCondition = 'Sunny';
          mockIcon = 'sunny';
          mockRiskLevel = 'moderate';
        } else {
          // Default mock data
          mockTemp = 22;
          mockHumidity = 65;
          mockCondition = 'Partly Cloudy';
          mockIcon = 'partly-sunny';
          mockRiskLevel = 'moderate';
        }

        // Create mock forecast data for a full week
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const today = new Date();
        const mockForecast = [];

        // Add today and the next 6 days
        for (let i = 0; i < 7; i++) {
          const date = new Date();
          date.setDate(today.getDate() + i);
          const dayName = i === 0 ? 'Today' : i === 1 ? 'Tomorrow' : days[date.getDay()];

          // Generate somewhat realistic weather patterns
          let dayTemp, dayHumidity, dayCondition, dayIcon, dayRiskLevel;
          let dayMinTemp, dayMaxTemp, dayWindSpeed, dayPrecipitation;

          if (i === 0) {
            // Today
            dayTemp = mockTemp;
            dayMinTemp = mockTemp - 3;
            dayMaxTemp = mockTemp + 3;
            dayHumidity = mockHumidity;
            dayCondition = mockCondition;
            dayIcon = mockIcon;
            dayRiskLevel = mockRiskLevel;
            dayWindSpeed = Math.floor(Math.random() * 5) + 2; // 2-7 m/s
            dayPrecipitation = mockCondition === 'Rain' ? 80 : mockCondition === 'Partly Cloudy' ? 30 : 10;
          } else if (i === 1) {
            // Tomorrow
            dayTemp = mockTemp + 2;
            dayMinTemp = (mockTemp + 2) - 3;
            dayMaxTemp = (mockTemp + 2) + 3;
            dayHumidity = mockHumidity - 5;
            dayCondition = mockCondition === 'Rain' ? 'Partly Cloudy' : mockCondition;
            dayIcon = mockCondition === 'Rain' ? 'partly-sunny' : mockIcon;
            dayRiskLevel = mockRiskLevel === 'high' ? 'moderate' : mockRiskLevel;
            dayWindSpeed = Math.floor(Math.random() * 5) + 1; // 1-6 m/s
            dayPrecipitation = dayCondition === 'Rain' ? 70 : dayCondition === 'Partly Cloudy' ? 20 : 5;
          } else if (i === 2 || i === 3) {
            // Mid-week rain
            dayTemp = mockTemp - i;
            dayMinTemp = (mockTemp - i) - 2;
            dayMaxTemp = (mockTemp - i) + 4;
            dayHumidity = mockHumidity + (i * 5);
            dayCondition = 'Rain';
            dayIcon = 'rainy';
            dayRiskLevel = 'high';
            dayWindSpeed = Math.floor(Math.random() * 4) + 4; // 4-8 m/s
            dayPrecipitation = 75 + Math.floor(Math.random() * 20); // 75-95%
          } else {
            // End of week
            dayTemp = mockTemp + (i - 4);
            dayMinTemp = (mockTemp + (i - 4)) - 3;
            dayMaxTemp = (mockTemp + (i - 4)) + 4;
            dayHumidity = Math.max(30, mockHumidity - ((i - 3) * 10));
            dayCondition = i === 6 ? 'Sunny' : mockCondition;
            dayIcon = i === 6 ? 'sunny' : mockIcon;
            dayRiskLevel = i === 6 ? 'low' : mockRiskLevel;
            dayWindSpeed = Math.floor(Math.random() * 3) + 2; // 2-5 m/s
            dayPrecipitation = i === 6 ? 0 : 15;
          }

          mockForecast.push({
            day: dayName,
            date: date,
            temperature: dayTemp,
            minTemp: dayMinTemp,
            maxTemp: dayMaxTemp,
            humidity: dayHumidity,
            condition: dayCondition,
            icon: dayIcon,
            riskLevel: dayRiskLevel,
            windSpeed: dayWindSpeed,
            precipitation: dayPrecipitation,
          });
        }

        // Generate a message based on the mock data
        let mockMessage = '';
        if (mockRiskLevel === 'high') {
          mockMessage = `High humidity and ${mockCondition.toLowerCase()} conditions in ${location.name} may affect asthma. Consider limiting outdoor activities.`;
        } else if (mockRiskLevel === 'moderate') {
          mockMessage = `Moderate conditions in ${location.name}. Keep your inhaler handy when going outside.`;
        } else {
          mockMessage = `Weather conditions in ${location.name} are favorable for asthma management.`;
        }

        // Update weather data with mock data
        setWeatherData({
          ...weatherData,
          current: {
            temperature: mockTemp,
            humidity: mockHumidity,
            condition: mockCondition,
            icon: mockIcon,
            airQuality: mockRiskLevel === 'high' ? 'Poor' : mockRiskLevel === 'moderate' ? 'Moderate' : 'Good',
            riskLevel: mockRiskLevel,
            message: mockMessage,
          },
          forecast: mockForecast,
        });
      }
    } catch (err) {
      console.error('Error in location selection:', err);
      setError('Unable to process this location. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderForecastItem = (item, index) => (
    <Card key={index} style={styles.forecastCard}>
      <Text style={styles.forecastDay}>{item.day}</Text>
      <Ionicons name={item.icon} size={32} color={COLORS.TEXT} />

      <View style={styles.forecastTempContainer}>
        {item.minTemp !== undefined && item.maxTemp !== undefined ? (
          <>
            <Text style={styles.forecastTempRange}>
              <Text style={styles.forecastTempMin}>{item.minTemp}°</Text>
              <Text style={styles.forecastTempSeparator}> / </Text>
              <Text style={styles.forecastTempMax}>{item.maxTemp}°</Text>
            </Text>
            <Text style={styles.forecastTempAvg}>{item.temperature}°C</Text>
          </>
        ) : (
          <Text style={styles.forecastTemp}>{item.temperature}°C</Text>
        )}
      </View>

      <View style={styles.forecastDetailsContainer}>
        <View style={styles.forecastDetailItem}>
          <Ionicons name="water-outline" size={16} color={COLORS.TEXT} />
          <Text style={styles.forecastDetailText}>{item.humidity}%</Text>
        </View>

        {item.precipitation !== undefined && (
          <View style={styles.forecastDetailItem}>
            <Ionicons name="rainy-outline" size={16} color={COLORS.TEXT} />
            <Text style={styles.forecastDetailText}>{item.precipitation}%</Text>
          </View>
        )}

        {item.windSpeed !== undefined && (
          <View style={styles.forecastDetailItem}>
            <Ionicons name="speedometer-outline" size={16} color={COLORS.TEXT} />
            <Text style={styles.forecastDetailText}>{item.windSpeed} m/s</Text>
          </View>
        )}
      </View>

      <View
        style={[
          styles.riskIndicator,
          { backgroundColor: getRiskColor(item.riskLevel) },
        ]}
      >
        <Text style={styles.riskIndicatorText}>
          {getRiskText(item.riskLevel)}
        </Text>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title={i18n.t('weather.title')}
        showBackButton
        onLeftPress={() => navigation.goBack()}
        rightIcon={
          <TouchableOpacity onPress={handleRefresh} disabled={isLoading}>
            <Ionicons
              name={isLoading ? 'refresh-circle' : 'refresh'}
              size={24}
              color={COLORS.WHITE}
            />
          </TouchableOpacity>
        }
      />

      <ScrollView
        style={styles.content}
        contentContainerStyle={{ paddingBottom: 100 }} // Additional padding for the content
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.PRIMARY]}
            tintColor={COLORS.PRIMARY}
          />
        }>
        {/* Location with Search Button */}
        <View style={styles.locationContainer}>
          <View style={styles.locationTextContainer}>
            <Ionicons name="location" size={20} color={COLORS.PRIMARY} />
            <Text style={styles.locationText}>{currentLocation}</Text>
          </View>
          <TouchableOpacity
            style={styles.searchButton}
            onPress={() => setSearchModalVisible(true)}
          >
            <Ionicons name="search" size={22} color={COLORS.WHITE} />
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <Card style={styles.loadingCard}>
            <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            <Text style={styles.loadingText}>Loading weather information...</Text>
          </Card>
        ) : error ? (
          <Card style={styles.errorCard}>
            <Ionicons name="cloud-offline" size={24} color={COLORS.DANGER} />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchWeatherData}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </Card>
        ) : (
          <>
            {/* Current Weather */}
            <Card style={styles.currentWeatherCard}>
              <View style={styles.currentWeatherHeader}>
                <View>
                  <Text style={styles.currentTemp}>
                    {weatherData.current.temperature}°C
                  </Text>
                  <Text style={styles.currentCondition}>
                    {weatherData.current.condition}
                  </Text>
                </View>
                <Ionicons
                  name={weatherData.current.icon}
                  size={64}
                  color={COLORS.PRIMARY}
                />
              </View>

              <View style={styles.weatherDetails}>
                <View style={styles.weatherDetail}>
                  <Ionicons name="water-outline" size={20} color={COLORS.TEXT} />
                  <Text style={styles.detailLabel}>{i18n.t('weather.humidity')}</Text>
                  <Text style={styles.detailValue}>{weatherData.current.humidity}%</Text>
                </View>
                <View style={styles.weatherDetail}>
                  <Ionicons name="leaf-outline" size={20} color={COLORS.TEXT} />
                  <Text style={styles.detailLabel}>{i18n.t('weather.airQuality')}</Text>
                  <Text style={styles.detailValue}>{weatherData.current.airQuality}</Text>
                </View>
              </View>
            </Card>

            {/* Weather Alert */}
            <WeatherAlert
              riskLevel={weatherData.current.riskLevel}
              message={weatherData.current.message}
              temperature={weatherData.current.temperature}
              humidity={weatherData.current.humidity}
              condition={weatherData.current.condition}
            />
          </>
        )}

        {!isLoading && !error && (
          <>
            {/* Risk Assessment */}
            <Card title={i18n.t('weather.riskAssessment')} style={styles.riskCard}>
              <View style={styles.riskContent}>
                <View
                  style={[
                    styles.riskLevelIndicator,
                    { backgroundColor: getRiskColor(weatherData.current.riskLevel) },
                  ]}
                >
                  <Text style={styles.riskLevelText}>
                    {getRiskText(weatherData.current.riskLevel)}
                  </Text>
                </View>
                <View style={styles.riskFactors}>
                  {weatherData.current.humidity > 70 && (
                    <View style={styles.riskFactor}>
                      <Ionicons name="warning-outline" size={20} color={COLORS.WARNING} />
                      <Text style={styles.riskFactorText}>
                        {i18n.t('weather.highHumidity')} ({weatherData.current.humidity}%)
                      </Text>
                    </View>
                  )}
                  <View style={styles.riskFactor}>
                    <Ionicons name="information-circle-outline" size={20} color={COLORS.PRIMARY} />
                    <Text style={styles.riskFactorText}>
                      {i18n.t('weather.carryInhaler')}
                    </Text>
                  </View>
                  {weatherData.current.riskLevel === 'high' && (
                    <View style={styles.riskFactor}>
                      <Ionicons name="home-outline" size={20} color={COLORS.DANGER} />
                      <Text style={styles.riskFactorText}>
                        {i18n.t('weather.stayIndoors')}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </Card>

            {/* Forecast */}
            {weatherData.forecast && weatherData.forecast.length > 0 && (
              <>
                <Text style={styles.sectionTitle}>{i18n.t('weather.forecast')}</Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.forecastContainer}
                >
                  {weatherData.forecast.map((item, index) => renderForecastItem(item, index))}
                </ScrollView>
              </>
            )}
          </>
        )}


      </ScrollView>

      {/* Search Modal */}
      <Modal
        visible={searchModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setSearchModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Search Location</Text>
              <TouchableOpacity
                onPress={() => setSearchModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
            </View>

            <View style={styles.searchInputContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Enter city name..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                onSubmitEditing={handleSearch}
                returnKeyType="search"
                autoFocus
              />
              <TouchableOpacity
                style={styles.searchInputButton}
                onPress={handleSearch}
              >
                <Ionicons name="search" size={20} color={COLORS.WHITE} />
              </TouchableOpacity>
            </View>

            {searchLoading ? (
              <ActivityIndicator style={styles.searchLoading} size="large" color={COLORS.PRIMARY} />
            ) : searchResults.length > 0 ? (
              <ScrollView style={styles.searchResults}>
                {searchResults.map((result, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.searchResultItem,
                      index < searchResults.length - 1 && styles.searchResultItemBorder,
                    ]}
                    onPress={() => handleSearchResultSelect(result)}
                  >
                    <Text style={styles.searchResultText}>
                      {result.name}, {result.country}
                    </Text>
                    <Ionicons name="chevron-forward" size={20} color={COLORS.TEXT} />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            ) : searchQuery.length > 0 ? (
              <View style={styles.noResultsContainer}>
                <Ionicons name="search-outline" size={48} color={COLORS.LIGHT_TEXT} />
                <Text style={styles.noResultsText}>No locations found</Text>
                <Text style={styles.noResultsSubtext}>Try a different search term</Text>
              </View>
            ) : (
              <View style={styles.searchPromptContainer}>
                <Ionicons name="location-outline" size={48} color={COLORS.LIGHT_TEXT} />
                <Text style={styles.searchPromptText}>Search for a city</Text>
                <Text style={styles.searchPromptSubtext}>Example: "London", "New York", "Tokyo"</Text>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_BG,
  },
  content: {
    flex: 1,
    padding: SPACING.medium,
    paddingBottom: 200, // Significantly increased padding at the bottom for the tab bar
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.medium,
  },
  locationTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationText: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginLeft: SPACING.xs,
    flex: 1,
  },
  searchButton: {
    backgroundColor: COLORS.PRIMARY,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  currentWeatherCard: {
    marginBottom: SPACING.medium,
  },
  currentWeatherHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.medium,
  },
  currentTemp: {
    fontSize: FONTS.SIZES.xxxl,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.TEXT,
    marginBottom: SPACING.xs,
  },
  currentCondition: {
    fontSize: FONTS.SIZES.large,
    color: COLORS.TEXT,
    opacity: 0.7,
  },
  weatherDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_BG,
    paddingTop: SPACING.medium,
  },
  weatherDetail: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    opacity: 0.7,
    marginVertical: SPACING.xs,
  },
  detailValue: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  riskCard: {
    marginBottom: SPACING.medium,
  },
  riskContent: {
    alignItems: 'center',
  },
  riskLevelIndicator: {
    paddingHorizontal: SPACING.medium,
    paddingVertical: SPACING.small,
    borderRadius: BORDER_RADIUS.medium,
    marginBottom: SPACING.medium,
  },
  riskLevelText: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.bold,
    color: COLORS.WHITE,
  },
  riskFactors: {
    width: '100%',
  },
  riskFactor: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  riskFactorText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    marginLeft: SPACING.small,
    flex: 1,
  },
  sectionTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.medium,
  },
  forecastContainer: {
    marginBottom: 80, // Fixed large value to ensure enough space at the bottom
  },
  forecastCard: {
    width: 150,
    marginRight: SPACING.medium,
    alignItems: 'center',
    paddingVertical: SPACING.medium,
  },
  forecastDay: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
    marginBottom: SPACING.small,
  },
  forecastTempContainer: {
    alignItems: 'center',
    marginTop: SPACING.small,
    marginBottom: SPACING.small,
  },
  forecastTemp: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  forecastTempRange: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  forecastTempMin: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.LIGHT_TEXT,
  },
  forecastTempSeparator: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.LIGHT_TEXT,
  },
  forecastTempMax: {
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
  },
  forecastTempAvg: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  forecastDetailsContainer: {
    width: '100%',
    marginBottom: SPACING.small,
  },
  forecastDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.xs,
  },
  forecastDetailText: {
    fontSize: FONTS.SIZES.small,
    color: COLORS.TEXT,
    marginLeft: SPACING.xs,
  },
  riskIndicator: {
    paddingHorizontal: SPACING.small,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.small,
    marginTop: SPACING.small,
  },
  riskIndicatorText: {
    fontSize: FONTS.SIZES.small,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.WHITE,
  },
  locationsCard: {
    marginBottom: SPACING.large,
  },
  locationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.medium,
  },
  locationItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  selectedLocation: {
    backgroundColor: COLORS.PRIMARY + '10',
  },
  locationItemText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  loadingCard: {
    marginBottom: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.large,
  },
  loadingText: {
    marginTop: SPACING.medium,
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  errorCard: {
    marginBottom: SPACING.medium,
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.large,
    backgroundColor: COLORS.LIGHT_BG,
  },
  errorText: {
    marginTop: SPACING.small,
    marginBottom: SPACING.medium,
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.small,
    paddingHorizontal: SPACING.medium,
    borderRadius: BORDER_RADIUS.medium,
  },
  retryButtonText: {
    color: COLORS.WHITE,
    fontSize: FONTS.SIZES.medium,
    fontWeight: FONTS.WEIGHTS.medium,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: SPACING.medium,
    paddingBottom: SPACING.large,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  modalTitle: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.semibold,
    color: COLORS.TEXT,
  },
  closeButton: {
    padding: SPACING.small,
  },
  searchInputContainer: {
    flexDirection: 'row',
    marginVertical: SPACING.medium,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_BG,
    borderRadius: BORDER_RADIUS.medium,
    overflow: 'hidden',
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: SPACING.medium,
    paddingVertical: SPACING.medium,
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  searchInputButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.medium,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchLoading: {
    marginTop: SPACING.large,
  },
  searchResults: {
    maxHeight: 300,
  },
  searchResultItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.medium,
  },
  searchResultItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_BG,
  },
  searchResultText: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.TEXT,
  },
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxlarge,
  },
  noResultsText: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginTop: SPACING.medium,
  },
  noResultsSubtext: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.LIGHT_TEXT,
    marginTop: SPACING.small,
  },
  searchPromptContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxlarge,
  },
  searchPromptText: {
    fontSize: FONTS.SIZES.large,
    fontWeight: FONTS.WEIGHTS.medium,
    color: COLORS.TEXT,
    marginTop: SPACING.medium,
  },
  searchPromptSubtext: {
    fontSize: FONTS.SIZES.medium,
    color: COLORS.LIGHT_TEXT,
    marginTop: SPACING.small,
  },
});

export default WeatherScreen;
